<template>
  <div class="knowledge-point-select">
    <el-form-item label="知识点">
      <el-tree
        ref="treeRef"
        :data="knowledgePoints"
        :props="treeProps"
        show-checkbox
        node-key="id"
        :default-checked-keys="modelValue"
        @check="handleCheck"
        style="max-height: 300px; overflow-y: auto; border: 1px solid var(--el-border-color); border-radius: 4px; padding: 8px;"
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { ElTree } from 'element-plus'

interface KnowledgePoint {
  id: string
  label: string
  children?: KnowledgePoint[]
}

interface Props {
  modelValue?: string[]
  subject?: string
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'change', value: string[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const treeRef = ref<InstanceType<typeof ElTree>>()

const treeProps = {
  children: 'children',
  label: 'label'
}

const knowledgePoints = ref<KnowledgePoint[]>([
  {
    id: 'math_basic',
    label: '基础数学',
    children: [
      { id: 'math_arithmetic', label: '四则运算' },
      { id: 'math_fraction', label: '分数运算' },
      { id: 'math_decimal', label: '小数运算' }
    ]
  },
  {
    id: 'math_algebra',
    label: '代数',
    children: [
      { id: 'math_equation', label: '方程' },
      { id: 'math_inequality', label: '不等式' },
      { id: 'math_function', label: '函数' }
    ]
  },
  {
    id: 'math_geometry',
    label: '几何',
    children: [
      { id: 'math_plane_geometry', label: '平面几何' },
      { id: 'math_solid_geometry', label: '立体几何' },
      { id: 'math_coordinate_geometry', label: '解析几何' }
    ]
  },
  {
    id: 'chinese_basic',
    label: '语文基础',
    children: [
      { id: 'chinese_pinyin', label: '拼音' },
      { id: 'chinese_character', label: '汉字' },
      { id: 'chinese_word', label: '词语' }
    ]
  },
  {
    id: 'chinese_reading',
    label: '阅读理解',
    children: [
      { id: 'chinese_modern_reading', label: '现代文阅读' },
      { id: 'chinese_classical_reading', label: '文言文阅读' },
      { id: 'chinese_poetry_reading', label: '诗歌鉴赏' }
    ]
  }
])

const modelValue = computed({
  get: () => props.modelValue || [],
  set: (value: string[]) => emit('update:modelValue', value)
})

const handleCheck = () => {
  if (treeRef.value) {
    const checkedKeys = treeRef.value.getCheckedKeys() as string[]
    const halfCheckedKeys = treeRef.value.getHalfCheckedKeys() as string[]
    const allKeys = [...checkedKeys, ...halfCheckedKeys]
    
    modelValue.value = allKeys
    emit('change', allKeys)
  }
}

// Update knowledge points based on subject
watch(
  () => props.subject,
  (newSubject) => {
    if (newSubject === 'math') {
      knowledgePoints.value = knowledgePoints.value.filter(kp => kp.id.startsWith('math_'))
    } else if (newSubject === 'chinese') {
      knowledgePoints.value = knowledgePoints.value.filter(kp => kp.id.startsWith('chinese_'))
    } else {
      // Show all knowledge points
      knowledgePoints.value = [
        {
          id: 'math_basic',
          label: '基础数学',
          children: [
            { id: 'math_arithmetic', label: '四则运算' },
            { id: 'math_fraction', label: '分数运算' },
            { id: 'math_decimal', label: '小数运算' }
          ]
        },
        {
          id: 'math_algebra',
          label: '代数',
          children: [
            { id: 'math_equation', label: '方程' },
            { id: 'math_inequality', label: '不等式' },
            { id: 'math_function', label: '函数' }
          ]
        },
        {
          id: 'math_geometry',
          label: '几何',
          children: [
            { id: 'math_plane_geometry', label: '平面几何' },
            { id: 'math_solid_geometry', label: '立体几何' },
            { id: 'math_coordinate_geometry', label: '解析几何' }
          ]
        },
        {
          id: 'chinese_basic',
          label: '语文基础',
          children: [
            { id: 'chinese_pinyin', label: '拼音' },
            { id: 'chinese_character', label: '汉字' },
            { id: 'chinese_word', label: '词语' }
          ]
        },
        {
          id: 'chinese_reading',
          label: '阅读理解',
          children: [
            { id: 'chinese_modern_reading', label: '现代文阅读' },
            { id: 'chinese_classical_reading', label: '文言文阅读' },
            { id: 'chinese_poetry_reading', label: '诗歌鉴赏' }
          ]
        }
      ]
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.knowledge-point-select {
  margin-bottom: 16px;
}
</style>