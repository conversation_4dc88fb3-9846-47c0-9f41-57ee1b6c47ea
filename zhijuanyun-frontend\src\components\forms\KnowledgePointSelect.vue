<template>
  <div class="knowledge-point-select">
    <el-form-item label="知识点">
      <el-tree
        ref="treeRef"
        :data="knowledgePoints"
        :props="treeProps"
        show-checkbox
        node-key="id"
        :default-checked-keys="modelValue"
        @check="handleCheck"
        style="max-height: 300px; overflow-y: auto; border: 1px solid var(--el-border-color); border-radius: 4px; padding: 8px;"
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { ElTree } from 'element-plus'

interface KnowledgePoint {
  id: string
  label: string
  children?: KnowledgePoint[]
}

interface Props {
  modelValue?: string[]
  subject?: string
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'change', value: string[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const treeRef = ref<InstanceType<typeof ElTree>>()

const treeProps = {
  children: 'children',
  label: 'label'
}

const knowledgePoints = ref<KnowledgePoint[]>([
  { id: 'basic', label: '基础知识' },
  { id: 'intermediate', label: '进阶知识' },
  { id: 'advanced', label: '高级知识' },
  { id: 'comprehensive', label: '综合应用' }
])

const modelValue = computed({
  get: () => props.modelValue || [],
  set: (value: string[]) => emit('update:modelValue', value)
})

const handleCheck = () => {
  if (treeRef.value) {
    const checkedKeys = treeRef.value.getCheckedKeys() as string[]
    const halfCheckedKeys = treeRef.value.getHalfCheckedKeys() as string[]
    const allKeys = [...checkedKeys, ...halfCheckedKeys]
    
    modelValue.value = allKeys
    emit('change', allKeys)
  }
}

// 知识点现在是通用的，不需要根据学科过滤
</script>

<style scoped>
.knowledge-point-select {
  margin-bottom: 16px;
}
</style>