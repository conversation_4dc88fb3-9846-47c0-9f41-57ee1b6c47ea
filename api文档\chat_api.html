<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Apifox 接口文档 - Chat API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        pre { background-color: #f5f5f5; padding: 10px; overflow-x: auto; }
        .endpoint { margin-bottom: 30px; border-left: 4px solid #007BFF; padding-left: 10px; }
    </style>
</head>
<body>

<h1>Chat API 接口文档 (Apifox 风格)</h1>

<div class="endpoint">
    <h2>1. 获取 Profile ID</h2>
    <p><strong>URL:</strong> <code>/api/application/{application_id}</code></p>
    <p><strong>Method:</strong> GET</p>
    <p><strong>Headers:</strong></p>
    <pre>
{
  "accept": "application/json",
  "AUTHORIZATION": "application-f8934d839b1c8209c91ef4c6cacaaf7a"
}
    </pre>
    <p><strong>Description:</strong> 获取应用的 profile ID。</p>
    <p><strong>Response 示例:</strong></p>
    <pre>
{
    'code': 200, 
    'message': '成功', 
    'data': 
       {
          'id': 'e69bd1b6-2a24-11f0-89bb-0242ac130003', 
          'create_time': '2025-05-06T10:50:42.464945+08:00', 
          'update_time': '2025-05-21T15:24:02.004106+08:00', 
          'name': 'AI数字人', 
          'desc': '用于职中AI数字人问答', 
          'prologue': '您好，我是江门一职校园助手，您可以向我提出江门一职相关问题。', 
          'dialogue_number': 1, 
          'dataset_setting': 
              {
                  'search_mode': 'blend', 
                  'no_references_setting': 
                       {
                           'value': '{question}', 
                           'status': 'ai_questioning'
                       }, 
                  'top_n': 3, 
                  'similarity': 0.6, 
                  'max_paragraph_char_number': 5000
              }, 
          'model_setting': 
              {
                  'prompt': '已知信息：{data}\n用户问题：{question}\n回答要求：\n - 请使用中文回答用户问题',
                  'system': '你是AI小助手', 
                  'no_references_prompt': '{question}'}, 
                  'model_params_setting': {}, 
                  'tts_model_params_setting': {}, 
                  'problem_optimization': False, 
                  'icon': '/ui/favicon.ico', 
                  'work_flow': {}, 
                  'type': 'SIMPLE', 
                  'problem_optimization_prompt': '()里面是用户问题,根据上下文回答揣测用户问题({question}) 要求: 输出一个补全问题,并且放在<data></data>标签中', 
                  'tts_model_enable': False, 
                  'stt_model_enable': False, 
                  'tts_type': 'BROWSER', 
                  'tts_autoplay': False, 
                  'stt_autosend': False, 
                  'clean_time': 180, 
                  'file_upload_enable': False, 
                  'file_upload_setting': {}, 
                  'user': 'c5a2f7be-2187-11f0-9b3b-0242ac130003', 
                  'model': 'd2cefa9c-f9a4-11ef-8303-0242ac130003', 
                  'tts_model': None, 
                  'stt_model': None, 
                  'multiple_rounds_dialogue': True, 
                  'dataset_id_list': ['59b30dba-2a23-11f0-865c-0242ac130003']
               }
    }
}
    </pre>
</div>

<div class="endpoint">
    <h2>2. 获取 Chat ID</h2>
    <p><strong>URL:</strong> <code>/api/application/{profile_id}/chat/open</code></p>
    <p><strong>Method:</strong> GET</p>
    <p><strong>Headers:</strong></p>
    <pre>
{
  "accept": "application/json",
  "AUTHORIZATION": "application-f8934d839b1c8209c91ef4c6cacaaf7a"
}
    </pre>
    <p><strong>Description:</strong> 打开一个会话并返回 chat ID。</p>
    <p><strong>Response 示例:</strong></p>
    <pre>
{
   'code': 200, 
   'message': '成功', 
   'data': 'c1cfa798-50af-11f0-87a4-0242ac130003'
}
    </pre>
</div>

<div class="endpoint">
    <h2>3. 发送聊天消息</h2>
    <p><strong>URL:</strong> <code>/api/application/chat_message/{chat_id}</code></p>
    <p><strong>Method:</strong> POST</p>
    <p><strong>Headers:</strong></p>
    <pre>
{
  "accept": "application/json",
  "AUTHORIZATION": "application-f8934d839b1c8209c91ef4c6cacaaf7a",
  "Content-Type": "application/json"
}
    </pre>
    <p><strong>Payload 示例:</strong></p>
    <pre>
{
  "message": "用户输入的消息内容",
  "re_chat": false,
  "form_data": {},
  "image_list": [
    {
      "name": "图片2.png",
      "url": "/api/file/985388b4-5229-11f0-bbb8-00163e4f3d7b",
      "file_id": "985388b4-5229-11f0-bbb8-00163e4f3d7b"
    },
    {
      "name": "图片3.png",
      "url": "/api/file/973392bc-5229-11f0-bbb8-00163e4f3d7b",
      "file_id": "973392bc-5229-11f0-bbb8-00163e4f3d7b"
    }
  ],
  "document_list": [],
  "audio_list": []
}
    </pre>
    <p><strong>Description:</strong> 向指定 chat_id 发送消息，并支持携带图像等附件。</p>
    <p><strong>Response 示例:</strong></p>
    <pre>
data: {"chat_id": "e77b638a-51ac-11f0-bbb8-00163e4f3d7b", "chat_record_id": "4e66966e-522a-11f0-bbb8-00163e4f3d7b", "operate": true, "content": "AI回复内容", "node_id": "86ba2ad9-fb82-451d-ac3e-4cb82938f803", "up_node_id_list": ["start-node", "a112d254-778e-4d95-a851-9a018e02b2fe", "d2fecc8e-3e61-4c23-9a27-2db81447b13f", "97679712-b100-47ff-8d50-9ad36bace711", "b248d05b-893e-483a-ad09-7107bd9ba935", "4dbfd3e8-1481-43b7-9b08-c34238e7d84d", "464b29b8-b61b-4068-8c76-fefdcedc03e6", "08136777-8a23-41bc-a1e9-cd8b55b46bdb", "917cf377-af81-48d3-bf7e-2b024a03ebd2"], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_type": "reply-node", "runtime_node_id": "1d2c0bef778e661fd4c21798d0d7659fc7947044", "view_type": "many_view", "child_node": {}, "node_is_end": false, "real_node_id": "1d2c0bef778e661fd4c21798d0d7659fc7947044", "reasoning_content": ""}

data: {"chat_id": "e77b638a-51ac-11f0-bbb8-00163e4f3d7b", "chat_record_id": "4e66966e-522a-11f0-bbb8-00163e4f3d7b", "operate": true, "content": "", "node_id": "86ba2ad9-fb82-451d-ac3e-4cb82938f803", "up_node_id_list": ["start-node", "a112d254-778e-4d95-a851-9a018e02b2fe", "d2fecc8e-3e61-4c23-9a27-2db81447b13f", "97679712-b100-47ff-8d50-9ad36bace711", "b248d05b-893e-483a-ad09-7107bd9ba935", "4dbfd3e8-1481-43b7-9b08-c34238e7d84d", "464b29b8-b61b-4068-8c76-fefdcedc03e6", "08136777-8a23-41bc-a1e9-cd8b55b46bdb", "917cf377-af81-48d3-bf7e-2b024a03ebd2"], "is_end": false, "usage": {"completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0}, "node_is_end": true, "runtime_node_id": "1d2c0bef778e661fd4c21798d0d7659fc7947044", "node_type": "reply-node", "view_type": "many_view", "child_node": {}, "real_node_id": "1d2c0bef778e661fd4c21798d0d7659fc7947044", "reasoning_content": ""}

data: {"chat_id": "e77b638a-51ac-11f0-bbb8-00163e4f3d7b", "chat_record_id": "4e66966e-522a-11f0-bbb8-00163e4f3d7b", "operate": true, "content": "", "node_id": "", "up_node_id_list": [], "is_end": true, "usage": {"completion_tokens": 17099, "prompt_tokens": 2763, "total_tokens": 19862}}


    </pre>
    <p><strong>AI 回复结果示例:</strong></p>
    <pre>
{
   "result": [
      {
         'number': '1', 
         'score': 8, 
         'full_score': 8, 
         'reason': '所有得数均正确，包括小数点位置和进位处理都无误。'
      }, {
        …………
      }
   ],
  "redar": 
     {
        "actionType": "EVAL", 
         "option": "\noption = {\n title: {\n text: '答题情况雷达图'\n },\n legend: {\n data: ['实际得分', '满分']\n },\n radar: {\n indicator: [\n { name: '1题', max: 8 },\n { name: '2.1题', max: 3 },\n { name: '2.2题', max: 3 },\n { name: '2.3题', max: 2 },\n { name: '3.1题', max: 3 },\n { name: '3.2题', max: 3 },\n { name: '3.3题', max: 3 },\n { name: '3.4题', max: 3 },\n { name: '二.1题', max: 2 },\n { name: '二.2题', max: 4 },\n { name: '二.3题', max: 4 },\n { name: '二.4题', max: 4 },\n { name: '二.5题', max: 3 },\n { name: '二.6题', max: 2 },\n { name: '二.7题', max: 3 },\n { name: '二.8题', max: 2 },\n { name: '二.9题', max: 1 },\n { name: '二.10题', max: 1 },\n { name: '二.11题', max: 2 },\n { name: '三.1题', max: 2 },\n { name: '三.2题', max: 2 },\n { name: '三.3题', max: 2 },\n { name: '三.4题', max: 2 },\n { name: '三.5题', max: 2 },\n { name: '三.6题', max: 2 }\n ]\n },\n series: [\n {\n name: '答题情况',\n type: 'radar',\n data: [\n {\n value: [8, 3, 3, 2, 3, 3, 3, 3, 2, 4, 4, 4, 3, 2, 3, 2, 1, 1, 2, 2, 2, 2, 2, 2],\n name: '实际得分'\n },\n {\n value: [8, 3, 3, 2, 3, 3, 3, 3, 2, 4, 4, 4, 3, 2, 3, 2, 1, 1, 2, 2, 2, 2, 2, 2],\n name: '满分'\n }\n ]\n }\n ]\n};\n", 
         "style": {"height": "500px", "width": "100%"}
     },
  "bar": 
     {
        "actionType": "EVAL", 
        "option": "\noption = {\n legend: {\n data: ['得分', '总分']\n },\n tooltip: {\n trigger: 'axis'\n },\n xAxis: {\n type: 'category',\n data: [\n '1', '2.1', '2.2', '2.3', '3.1', '3.2', '3.3', '3.4', \n '二.1', '二.2', '二.3', '二.4', '二.5', '二.6', '二.7', '二.8', \n '二.9', '二.10', '二.11', '三.1', '三.2', '三.3', '三.4', '三.5', '三.6'\n ]\n },\n yAxis: {\n type: 'value'\n },\n series: [\n {\n name: '得分',\n data: [8, 3, 3, 2, 3, 3, 3, 3, 2, 4, 4, 4, 3, 2, 3, 2, 1, 1, 2, 2, 2, 2, 2, 2, 2],\n type: 'bar',\n barWidth: '60%',\n itemStyle: {\n color: '#5470c6'\n }\n },\n {\n name: '总分',\n data: [8, 3, 3, 2, 3, 3, 3, 3, 2, 4, 4, 4, 3, 2, 3, 2, 1, 1, 2, 2, 2, 2, 2, 2, 2],\n type: 'bar',\n barWidth: '60%',\n itemStyle: {\n color: '#91cc75'\n }\n }\n ]\n};\n", 
        "style": {"height": "500px", "width": "100%"}
      },
  "analysis":
        {
            'weak_points': [
                  '1. 没有明显的失分项，所有题目均得满分或按标准评分，说明学生整体掌握较好。', 
                  '2. 在试卷中未发现明显的知识薄弱点，所有计算、简算、单位换算、判断题等都完成得很好。', 
                  '3. 所有操作类和理解类问题（如小数除法余数的理解、简便运算的应用）都能正确解答，没有明显漏洞。'
                  …………
               ], 
             'strengthening_areas': [
                 '1. 可进一步提升复杂应用题的解题速度与准确率，尽管本次考试无错题，但可为更高难度挑战做准备。', 
                 '2. 提高对数学语言的理解能力，例如更复杂的文字题转化为数学表达式的能力。', 
                 '3. 加强对数学思维方法的系统训练，如逆向思维、代数建模等，以应对综合性更强的问题。'
                  …………
              ], 
              'subjection': [
                 '1. 建议每天进行一定量的口算和笔算练习，保持计算的熟练度和准确性。', 
                 '2. 鼓励尝试一些拓展性题目或奥数类题目，提升逻辑推理能力和综合运用能力。', 
                 '3. 定期回顾错题本（即使当前无错题），建立良好的学习反思习惯。', 
                 '4. 结合生活实际进行数学建模练习，如购物预算、路程计算等，增强数学应用意识。', 
                 …………
               ]
}
}
    </pre>
</div>

</body>
</html>