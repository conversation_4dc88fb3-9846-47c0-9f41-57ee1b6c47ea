<template>
  <div class="sample-upload">
    <el-form-item label="样题上传">
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        :drag="true"
        :action="uploadAction"
        :multiple="false"
        :show-file-list="true"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :on-error="handleError"
        :on-progress="handleProgress"
        :accept="acceptedTypes"
        :limit="1"
        :on-exceed="handleExceed"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 Word(.doc/.docx)、PDF(.pdf)、图片(.jpg/.png) 格式，文件大小不超过 20MB
          </div>
        </template>
      </el-upload>
      
      <!-- Upload Progress -->
      <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
        <el-progress
          :percentage="uploadProgress"
          :status="uploadStatus"
          :stroke-width="6"
        />
        <p class="progress-text">上传中... {{ uploadProgress }}%</p>
      </div>
      
      <!-- Upload Success -->
      <div v-if="uploadedFile" class="upload-success">
        <el-alert
          :title="`文件 ${uploadedFile.filename} 上传成功`"
          type="success"
          :closable="false"
          show-icon
        />
        <div class="file-info">
          <p><strong>文件名:</strong> {{ uploadedFile.filename }}</p>
          <p><strong>文件大小:</strong> {{ formatFileSize(uploadedFile.size) }}</p>
          <p><strong>上传时间:</strong> {{ formatDateTime(uploadedFile.uploadTime) }}</p>
          <p><strong>处理状态:</strong> 
            <el-tag 
              :type="getStatusType(uploadedFile.status)"
              size="small"
            >
              {{ getStatusText(uploadedFile.status) }}
            </el-tag>
          </p>
        </div>
      </div>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { UploadProps, UploadUserFile, UploadProgressEvent } from 'element-plus'
import type { SampleInfo } from '@/types'

interface Props {
  modelValue?: SampleInfo | null
}

interface Emits {
  (e: 'update:modelValue', value: SampleInfo | null): void
  (e: 'change', value: SampleInfo | null): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const uploadRef = ref()
const uploadProgress = ref(0)
const uploadStatus = ref<'success' | 'exception' | 'warning' | ''>('')

const uploadAction = computed(() => {
  return import.meta.env.VITE_API_BASE_URL + '/upload' || '/api/upload'
})

const acceptedTypes = '.doc,.docx,.pdf,.jpg,.jpeg,.png'

const uploadedFile = computed({
  get: () => props.modelValue,
  set: (value: SampleInfo | null) => emit('update:modelValue', value)
})

const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  // File type validation
  const allowedTypes = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/pdf', 'image/jpeg', 'image/jpg', 'image/png']
  if (!allowedTypes.includes(rawFile.type)) {
    ElMessage.error('文件格式不支持！请上传 Word、PDF 或图片文件')
    return false
  }
  
  // File size validation (20MB)
  const maxSize = 20 * 1024 * 1024
  if (rawFile.size > maxSize) {
    ElMessage.error('文件大小不能超过 20MB!')
    return false
  }
  
  uploadProgress.value = 0
  uploadStatus.value = ''
  return true
}

const handleProgress = (evt: UploadProgressEvent) => {
  uploadProgress.value = Math.round(evt.percent)
}

const handleSuccess: UploadProps['onSuccess'] = (response: any) => {
  uploadProgress.value = 100
  uploadStatus.value = 'success'
  
  if (response && response.data) {
    const sampleInfo: SampleInfo = {
      id: response.data.id,
      filename: response.data.filename,
      size: response.data.size,
      uploadTime: response.data.uploadTime || new Date().toISOString(),
      status: response.data.status || 'processing'
    }
    
    uploadedFile.value = sampleInfo
    emit('change', sampleInfo)
    ElMessage.success('文件上传成功!')
  }
}

const handleError: UploadProps['onError'] = (error: Error) => {
  uploadProgress.value = 0
  uploadStatus.value = 'exception'
  console.error('Upload error:', error)
  ElMessage.error('文件上传失败，请重试')
}

const handleRemove: UploadProps['onRemove'] = () => {
  uploadedFile.value = null
  uploadProgress.value = 0
  uploadStatus.value = ''
  emit('change', null)
  ElMessage.info('文件已移除')
}

const handlePreview: UploadProps['onPreview'] = (file: UploadUserFile) => {
  if (file.url) {
    window.open(file.url, '_blank')
  }
}

const handleExceed: UploadProps['onExceed'] = () => {
  ElMessage.warning('只能上传一个文件，请先移除当前文件')
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i]
}

const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'processing':
      return 'warning'
    case 'failed':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed':
      return '解析完成'
    case 'processing':
      return '解析中'
    case 'failed':
      return '解析失败'
    default:
      return '未知状态'
  }
}
</script>

<style scoped>
.sample-upload {
  margin-bottom: 16px;
}

.upload-demo {
  width: 100%;
}

.upload-progress {
  margin-top: 16px;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 6px;
}

.progress-text {
  margin-top: 8px;
  text-align: center;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.upload-success {
  margin-top: 16px;
}

.file-info {
  margin-top: 12px;
  padding: 12px;
  background-color: var(--el-fill-color-extra-light);
  border-radius: 6px;
  font-size: 14px;
}

.file-info p {
  margin: 4px 0;
  color: var(--el-text-color-regular);
}

:deep(.el-upload-dragger) {
  width: 100%;
}
</style>