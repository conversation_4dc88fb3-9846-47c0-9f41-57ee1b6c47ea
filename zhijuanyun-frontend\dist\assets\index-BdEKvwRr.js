const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-DND6wGZk.js","assets/vendor-CPqkYfXn.js","assets/ui-CjjzzDsP.js","assets/filter-t8MhSl_r.js","assets/utils-DxgFcSvi.js","assets/index-DJU-vpmA.css","assets/index-D3jSAnIC.js","assets/index-Dz0QjGEW.css","assets/index-DOjxvuhb.js","assets/index-VGhzFmdl.css","assets/index-Bq_jTjJH.js","assets/index-BQSl1eeZ.css"])))=>i.map(i=>d[i]);
import{x as L,ax as b,ay as A,al as u,y as g,Q as p,I as _,z as P,A as h,P as x,a6 as w,H as I,L as D,O as R,az as C,aA as V,au as B,aB as k}from"./vendor-CPqkYfXn.js";import{E as q,i as T}from"./ui-CjjzzDsP.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const e of document.querySelectorAll('link[rel="modulepreload"]'))s(e);new MutationObserver(e=>{for(const o of e)if(o.type==="childList")for(const t of o.addedNodes)t.tagName==="LINK"&&t.rel==="modulepreload"&&s(t)}).observe(document,{childList:!0,subtree:!0});function r(e){const o={};return e.integrity&&(o.integrity=e.integrity),e.referrerPolicy&&(o.referrerPolicy=e.referrerPolicy),e.crossOrigin==="use-credentials"?o.credentials="include":e.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(e){if(e.ep)return;e.ep=!0;const o=r(e);fetch(e.href,o)}})();const $=L({__name:"App",setup(a,{expose:n}){n();const r=b(),s=A(),t={router:r,route:s,menuItems:[{path:"/filter",title:"题目筛选",icon:"Search"},{path:"/edit",title:"题目编辑",icon:"Edit"},{path:"/layout",title:"试卷排版",icon:"Document"},{path:"/export",title:"导出试卷",icon:"Download"}],handleMenuSelect:i=>{r.push(i)}};return Object.defineProperty(t,"__isScriptSetup",{enumerable:!1,value:!0}),t}}),j=(a,n)=>{const r=a.__vccOpts||a;for(const[s,e]of n)r[s]=e;return r},z={class:"app"},N={class:"header-content"};function U(a,n,r,s,e,o){const t=u("el-icon"),i=u("el-menu-item"),c=u("el-menu"),m=u("el-header"),v=u("router-view"),l=u("el-main"),E=u("el-container");return P(),g("div",z,[p(E,null,{default:_(()=>[p(m,{class:"app-header"},{default:_(()=>[h("div",N,[n[0]||(n[0]=h("div",{class:"logo"},[h("h2",null,"智卷云")],-1)),p(c,{"default-active":s.route.path,mode:"horizontal",onSelect:s.handleMenuSelect,class:"header-menu"},{default:_(()=>[(P(),g(x,null,w(s.menuItems,d=>p(i,{key:d.path,index:d.path},{default:_(()=>[p(t,null,{default:_(()=>[(P(),I(D(d.icon)))]),_:2},1024),h("span",null,R(d.title),1)]),_:2},1032,["index"])),64))]),_:1},8,["default-active"])])]),_:1}),p(l,{class:"app-main"},{default:_(()=>[p(v)]),_:1})]),_:1})])}const F=j($,[["render",U],["__scopeId","data-v-7a7a37b1"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/App.vue"]]),H="modulepreload",W=function(a){return"/"+a},O={},y=function(n,r,s){let e=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const t=document.querySelector("meta[property=csp-nonce]"),i=(t==null?void 0:t.nonce)||(t==null?void 0:t.getAttribute("nonce"));e=Promise.allSettled(r.map(c=>{if(c=W(c),c in O)return;O[c]=!0;const m=c.endsWith(".css"),v=m?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${v}`))return;const l=document.createElement("link");if(l.rel=m?"stylesheet":H,m||(l.as="script"),l.crossOrigin="",l.href=c,i&&l.setAttribute("nonce",i),document.head.appendChild(l),m)return new Promise((E,d)=>{l.addEventListener("load",E),l.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${c}`)))})}))}function o(t){const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=t,window.dispatchEvent(i),!i.defaultPrevented)throw t}return e.then(t=>{for(const i of t||[])i.status==="rejected"&&o(i.reason);return n().catch(o)})},S=C({history:V("/"),routes:[{path:"/",name:"home",redirect:"/filter"},{path:"/filter",name:"question-filter",component:()=>y(()=>import("./index-DND6wGZk.js"),__vite__mapDeps([0,1,2,3,4,5])),meta:{title:"题目筛选"}},{path:"/edit",name:"question-edit",component:()=>y(()=>import("./index-D3jSAnIC.js"),__vite__mapDeps([6,1,3,4,2,7])),meta:{title:"题目编辑"}},{path:"/layout",name:"paper-layout",component:()=>y(()=>import("./index-DOjxvuhb.js"),__vite__mapDeps([8,1,2,3,4,9])),meta:{title:"试卷排版"}},{path:"/export",name:"export",component:()=>y(()=>import("./index-Bq_jTjJH.js"),__vite__mapDeps([10,1,2,3,4,11])),meta:{title:"导出试卷"}}]});S.beforeEach((a,n,r)=>{var s;(s=a.meta)!=null&&s.title?document.title=`${a.meta.title} - 智卷云`:document.title="智卷云",r()});const f=B(F);for(const[a,n]of Object.entries(q))f.component(a,n);f.use(k());f.use(S);f.use(T);f.mount("#app");export{j as _,y as a};
