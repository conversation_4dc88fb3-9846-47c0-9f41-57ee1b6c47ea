<template>
  <div class="region-count-select">
    <el-row :gutter="16">
      <el-col :span="12">
        <el-form-item label="地区">
          <el-select
            v-model="region"
            placeholder="请选择地区"
            clearable
            style="width: 100%"
            @change="handleRegionChange"
          >
            <el-option
              v-for="area in regionOptions"
              :key="area.value"
              :label="area.label"
              :value="area.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="题目数量" required>
          <el-input-number
            v-model="questionCount"
            :min="1"
            :max="100"
            :step="1"
            placeholder="请输入题目数量"
            style="width: 100%"
            @change="handleCountChange"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Option {
  label: string
  value: string
}

interface Props {
  modelValueRegion?: string
  modelValueQuestionCount?: number
}

interface Emits {
  (e: 'update:modelValueRegion', value: string): void
  (e: 'update:modelValueQuestionCount', value: number): void
  (e: 'change', type: 'region' | 'questionCount', value: string | number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const regionOptions = ref<Option[]>([
  { label: '全国', value: 'national' },
  { label: '北京', value: 'beijing' },
  { label: '上海', value: 'shanghai' },
  { label: '广东', value: 'guangdong' },
  { label: '浙江', value: 'zhejiang' },
  { label: '江苏', value: 'jiangsu' },
  { label: '山东', value: 'shandong' },
  { label: '河南', value: 'henan' },
  { label: '四川', value: 'sichuan' },
  { label: '湖南', value: 'hunan' },
  { label: '湖北', value: 'hubei' },
  { label: '河北', value: 'hebei' },
  { label: '安徽', value: 'anhui' },
  { label: '福建', value: 'fujian' },
  { label: '江西', value: 'jiangxi' },
  { label: '辽宁', value: 'liaoning' },
  { label: '黑龙江', value: 'heilongjiang' },
  { label: '吉林', value: 'jilin' },
  { label: '山西', value: 'shanxi' },
  { label: '陕西', value: 'shaanxi' },
  { label: '甘肃', value: 'gansu' },
  { label: '青海', value: 'qinghai' },
  { label: '新疆', value: 'xinjiang' },
  { label: '西藏', value: 'tibet' },
  { label: '内蒙古', value: 'neimenggu' },
  { label: '广西', value: 'guangxi' },
  { label: '宁夏', value: 'ningxia' },
  { label: '海南', value: 'hainan' },
  { label: '重庆', value: 'chongqing' },
  { label: '天津', value: 'tianjin' },
  { label: '云南', value: 'yunnan' },
  { label: '贵州', value: 'guizhou' }
])

const region = computed({
  get: () => props.modelValueRegion || '',
  set: (value: string) => emit('update:modelValueRegion', value)
})

const questionCount = computed({
  get: () => props.modelValueQuestionCount || 10,
  set: (value: number) => emit('update:modelValueQuestionCount', value)
})

const handleRegionChange = (value: string) => {
  emit('change', 'region', value)
}

const handleCountChange = (value: number | undefined) => {
  const count = value || 10
  
  // Validate count range
  if (count < 1) {
    emit('change', 'questionCount', 1)
  } else if (count > 100) {
    emit('change', 'questionCount', 100)
  } else {
    emit('change', 'questionCount', count)
  }
  
  // Add haptic feedback on mobile
  if ('vibrate' in navigator) {
    navigator.vibrate(50)
  }
}
</script>

<style scoped>
.region-count-select {
  margin-bottom: 16px;
}
</style>