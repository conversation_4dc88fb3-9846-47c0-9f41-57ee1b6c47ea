<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + Vue + TS</title>
    <script type="module" crossorigin src="/assets/index-BdEKvwRr.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-CPqkYfXn.js">
    <link rel="modulepreload" crossorigin href="/assets/ui-CjjzzDsP.js">
    <link rel="stylesheet" crossorigin href="/assets/index-DO--s7BF.css">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
