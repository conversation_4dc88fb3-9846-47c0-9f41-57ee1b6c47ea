import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      redirect: '/filter'
    },
    {
      path: '/filter',
      name: 'question-filter',
      component: () => import('@/views/QuestionFilter/index.vue'),
      meta: {
        title: '题目筛选'
      }
    },
    {
      path: '/edit',
      name: 'question-edit',
      component: () => import('@/views/QuestionEdit/index.vue'),
      meta: {
        title: '题目编辑'
      }
    },
    {
      path: '/layout',
      name: 'paper-layout',
      component: () => import('@/views/PaperLayout/index.vue'),
      meta: {
        title: '试卷排版'
      }
    },
    {
      path: '/export',
      name: 'export',
      component: () => import('@/views/Export/index.vue'),
      meta: {
        title: '导出试卷'
      }
    }
  ],
})

// Navigation guards
router.beforeEach((to, _from, next) => {
  // Set page title
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 智卷云`
  } else {
    document.title = '智卷云'
  }
  
  next()
})

export default router