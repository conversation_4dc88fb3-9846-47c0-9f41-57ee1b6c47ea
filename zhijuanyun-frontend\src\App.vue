<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const menuItems = [
  { path: '/filter', title: '题目筛选', icon: 'Search' },
  { path: '/edit', title: '题目编辑', icon: 'Edit' },
  { path: '/layout', title: '试卷排版', icon: 'Document' },
  { path: '/export', title: '导出试卷', icon: 'Download' }
]

const handleMenuSelect = (path: string) => {
  router.push(path)
}
</script>

<template>
  <div class="app">
    <el-container>
      <!-- Header -->
      <el-header class="app-header">
        <div class="header-content">
          <div class="logo">
            <h2>智卷云</h2>
          </div>
          <el-menu
            :default-active="route.path"
            mode="horizontal"
            @select="handleMenuSelect"
            class="header-menu"
          >
            <el-menu-item
              v-for="item in menuItems"
              :key="item.path"
              :index="item.path"
            >
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.title }}</span>
            </el-menu-item>
          </el-menu>
        </div>
      </el-header>

      <!-- Main Content -->
      <el-main class="app-main">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background-color: #fff;
  border-bottom: 1px solid var(--border-light);
  padding: 0;
  flex-shrink: 0;
  z-index: 1000;
  position: sticky;
  top: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo h2 {
  margin: 0;
  color: var(--primary-color);
  font-weight: bold;
  font-size: 20px;
}

.header-menu {
  border-bottom: none;
  flex: 1;
  justify-content: center;
}

.header-menu .el-menu-item {
  height: 60px;
  line-height: 60px;
  font-size: 14px;
}

.app-main {
  background-color: var(--background-base);
  padding: 0;
  flex: 1;
  min-height: calc(100vh - 60px);
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
    flex-direction: column;
    height: auto;
    padding: 8px 15px;
  }
  
  .logo h2 {
    font-size: 18px;
    margin-bottom: 8px;
  }
  
  .header-menu {
    width: 100%;
    justify-content: space-between;
  }
  
  .header-menu .el-menu-item {
    height: 50px;
    line-height: 50px;
    font-size: 12px;
    padding: 0 8px;
  }
  
  .header-menu .el-menu-item span {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 6px 12px;
  }
  
  .logo h2 {
    font-size: 16px;
  }
  
  .header-menu .el-menu-item {
    height: 45px;
    line-height: 45px;
    padding: 0 4px;
  }
}
</style>
