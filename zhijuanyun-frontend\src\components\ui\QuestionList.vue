<template>
  <div class="question-list">
    <div class="list-header">
      <div class="header-info">
        <span class="title">题目列表</span>
        <el-tag type="info" size="small">共 {{ questions.length }} 题</el-tag>
      </div>
      <div class="header-actions">
        <el-button size="small" @click="handleSelectAll">
          {{ isAllSelected ? '取消全选' : '全选' }}
        </el-button>
        <el-button size="small" type="danger" @click="handleBatchDelete" :disabled="selectedQuestions.length === 0">
          批量删除 ({{ selectedQuestions.length }})
        </el-button>
        <el-button size="small" type="primary" @click="handleAddQuestion">
          添加题目
        </el-button>
      </div>
    </div>

    <div class="list-content">
      <!-- Empty State -->
      <div v-if="questions.length === 0" class="empty-state">
        <el-empty description="暂无题目">
          <el-button type="primary" @click="handleAddQuestion">添加第一道题目</el-button>
        </el-empty>
      </div>

      <!-- Question Items -->
      <div v-else class="question-items">
        <draggable
          :list="questionList"
          item-key="id"
          handle=".drag-handle"
          @change="handleDragChange"
          :animation="200"
        >
          <template #item="{ element: question, index }">
            <div 
              class="question-item"
              :class="{ 
                'selected': selectedQuestions.includes(question.id),
                'editing': editingQuestion === question.id 
              }"
            >
              <!-- Selection and Drag Handle -->
              <div class="item-controls">
                <el-checkbox
                  :model-value="selectedQuestions.includes(question.id)"
                  @change="(checked: boolean) => handleQuestionSelect(question.id, checked)"
                />
                <div class="drag-handle">
                  <el-icon><Menu /></el-icon>
                </div>
                <span class="question-number">{{ index + 1 }}</span>
              </div>

              <!-- Question Content -->
              <div class="item-content">
                <!-- Question Header -->
                <div class="content-header">
                  <div class="question-tags">
                    <el-tag size="small" type="info">{{ getQuestionTypeLabel(question.tags.questionType) }}</el-tag>
                    <el-tag size="small" type="warning">{{ getDifficultyLabel(question.tags.difficulty) }}</el-tag>
                    <el-tag size="small">{{ question.score }}分</el-tag>
                  </div>
                  <div class="question-actions">
                    <el-button-group size="small">
                      <el-button @click="handleEditQuestion(question.id)" size="small">
                        <el-icon><Edit /></el-icon>
                      </el-button>
                      <el-button @click="handlePreviewQuestion(question)" size="small">
                        <el-icon><View /></el-icon>
                      </el-button>
                      <el-button type="danger" @click="handleDeleteQuestion(question.id)" size="small">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </el-button-group>
                  </div>
                </div>

                <!-- Question Body -->
                <div class="content-body">
                  <!-- Question Stem -->
                  <div class="question-stem" v-html="question.content.stem"></div>

                  <!-- Options (if applicable) -->
                  <div v-if="question.content.options && question.content.options.length > 0" class="question-options">
                    <div 
                      v-for="(option, optIndex) in question.content.options" 
                      :key="optIndex"
                      class="option-item"
                    >
                      {{ String.fromCharCode(65 + optIndex) }}. {{ option }}
                    </div>
                  </div>

                  <!-- Answer -->
                  <div class="question-answer">
                    <strong>答案：</strong>{{ question.content.answer }}
                  </div>

                  <!-- Explanation (collapsible) -->
                  <div v-if="question.content.explanation" class="question-explanation">
                    <el-collapse>
                      <el-collapse-item title="查看解析" name="explanation">
                        <div v-html="question.content.explanation"></div>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                </div>

                <!-- Question Meta -->
                <div class="content-meta">
                  <div class="meta-info">
                    <span>知识点：{{ question.tags.knowledgePoint.join('、') }}</span>
                    <span>来源：{{ question.tags.sourceType }}</span>
                  </div>
                  <div class="score-editor">
                    <span>分值：</span>
                    <el-input-number
                      :model-value="question.score"
                      :min="1"
                      :max="100"
                      size="small"
                      style="width: 80px"
                      @change="(value: number | undefined) => handleScoreChange(question.id, value)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </div>

    <!-- Question Preview Dialog -->
    <el-dialog
      v-model="previewVisible"
      title="题目预览"
      width="60%"
      :before-close="handlePreviewClose"
    >
      <div v-if="previewQuestion" class="preview-content">
        <div class="preview-header">
          <h3>第 {{ getQuestionIndex(previewQuestion.id) + 1 }} 题 ({{ previewQuestion.score }}分)</h3>
          <div class="preview-tags">
            <el-tag type="info">{{ getQuestionTypeLabel(previewQuestion.tags.questionType) }}</el-tag>
            <el-tag type="warning">{{ getDifficultyLabel(previewQuestion.tags.difficulty) }}</el-tag>
          </div>
        </div>
        
        <div class="preview-body">
          <div class="preview-stem" v-html="previewQuestion.content.stem"></div>
          
          <div v-if="previewQuestion.content.options" class="preview-options">
            <div 
              v-for="(option, index) in previewQuestion.content.options" 
              :key="index"
              class="preview-option"
            >
              {{ String.fromCharCode(65 + index) }}. {{ option }}
            </div>
          </div>
          
          <div class="preview-answer">
            <strong>答案：</strong>{{ previewQuestion.content.answer }}
          </div>
          
          <div v-if="previewQuestion.content.explanation" class="preview-explanation">
            <strong>解析：</strong>
            <div v-html="previewQuestion.content.explanation"></div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Menu, Edit, View, Delete } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import type { Question } from '@/types'

interface Props {
  questions: Question[]
  loading?: boolean
}

interface Emits {
  (e: 'update:questions', questions: Question[]): void
  (e: 'question-select', questionIds: string[]): void
  (e: 'question-edit', questionId: string): void
  (e: 'question-delete', questionId: string): void
  (e: 'questions-reorder', questions: Question[]): void
  (e: 'score-change', questionId: string, score: number): void
  (e: 'add-question'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// Local state
const selectedQuestions = ref<string[]>([])
const editingQuestion = ref<string | null>(null)
const previewVisible = ref(false)
const previewQuestion = ref<Question | null>(null)

// Computed
const questionList = computed(() => props.questions)

const isAllSelected = computed(() => 
  props.questions.length > 0 && selectedQuestions.value.length === props.questions.length
)

// Question type and difficulty labels
const questionTypeLabels: Record<string, string> = {
  'single_choice': '单选题',
  'multiple_choice': '多选题',
  'true_false': '判断题',
  'fill_blank': '填空题',
  'short_answer': '简答题',
  'essay': '解答题',
  'calculation': '计算题',
  'application': '应用题',
  'analysis': '分析题',
  'comprehensive': '综合题'
}

const difficultyLabels: Record<string, string> = {
  'easy': '容易',
  'medium': '中等',
  'hard': '困难',
  'very_hard': '很难'
}

// Methods
const getQuestionTypeLabel = (type: string) => questionTypeLabels[type] || type
const getDifficultyLabel = (difficulty: string) => difficultyLabels[difficulty] || difficulty

const getQuestionIndex = (questionId: string) => {
  return props.questions.findIndex(q => q.id === questionId)
}

const handleQuestionSelect = (questionId: string, checked: boolean) => {
  if (checked) {
    selectedQuestions.value.push(questionId)
  } else {
    selectedQuestions.value = selectedQuestions.value.filter(id => id !== questionId)
  }
  emit('question-select', selectedQuestions.value)
}

const handleSelectAll = () => {
  if (isAllSelected.value) {
    selectedQuestions.value = []
  } else {
    selectedQuestions.value = props.questions.map(q => q.id)
  }
  emit('question-select', selectedQuestions.value)
}

const handleBatchDelete = async () => {
  if (selectedQuestions.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedQuestions.value.length} 道题目吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // Emit delete events for each selected question
    selectedQuestions.value.forEach(questionId => {
      emit('question-delete', questionId)
    })

    selectedQuestions.value = []
    ElMessage.success('批量删除成功')
  } catch {
    // User cancelled
  }
}

const handleEditQuestion = (questionId: string) => {
  editingQuestion.value = questionId
  emit('question-edit', questionId)
}

const handleDeleteQuestion = async (questionId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这道题目吗？',
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    emit('question-delete', questionId)
    selectedQuestions.value = selectedQuestions.value.filter(id => id !== questionId)
    ElMessage.success('题目删除成功')
  } catch {
    // User cancelled
  }
}

const handlePreviewQuestion = (question: Question) => {
  previewQuestion.value = question
  previewVisible.value = true
}

const handlePreviewClose = () => {
  previewVisible.value = false
  previewQuestion.value = null
}

const handleDragChange = () => {
  // Update question order based on new positions
  const reorderedQuestions = props.questions.map((question, index) => ({
    ...question,
    order: index + 1
  }))
  
  emit('questions-reorder', reorderedQuestions)
}

const handleScoreChange = (questionId: string, score: number | undefined) => {
  if (score && score > 0) {
    emit('score-change', questionId, score)
  }
}

const handleAddQuestion = () => {
  emit('add-question')
}

// Watch for questions changes to clear invalid selections
watch(
  () => props.questions,
  (newQuestions) => {
    const validIds = newQuestions.map(q => q.id)
    selectedQuestions.value = selectedQuestions.value.filter(id => validIds.includes(id))
  },
  { deep: true, immediate: false }
)
</script>

<style scoped>
.question-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color);
  background-color: var(--el-fill-color-extra-light);
  border-radius: 8px 8px 0 0;
  flex-shrink: 0;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title {
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 6px;
}

.list-content {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  padding: 20px;
  text-align: center;
}

.empty-state .el-empty {
  width: 100%;
}

.question-items {
  padding: 12px;
}

.question-item {
  display: flex;
  gap: 8px;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  background-color: var(--el-fill-color-blank);
  transition: all 0.2s;
}

.question-item:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.question-item.selected {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.question-item.editing {
  border-color: var(--el-color-warning);
  background-color: var(--el-color-warning-light-9);
}

.item-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  width: 50px;
  flex-shrink: 0;
}

.drag-handle {
  cursor: move;
  padding: 4px;
  border-radius: 4px;
  color: var(--el-text-color-secondary);
}

.drag-handle:hover {
  color: var(--el-color-primary);
  background-color: var(--el-fill-color-light);
}

.question-number {
  font-weight: 600;
  color: var(--el-color-primary);
  font-size: 14px;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.question-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.question-actions {
  display: flex;
  gap: 4px;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.question-item:hover .question-actions {
  opacity: 1;
}

.question-actions .el-button-group {
  display: flex;
}

.content-body {
  margin-bottom: 8px;
}

.question-stem {
  margin-bottom: 8px;
  line-height: 1.6;
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.question-options {
  margin: 8px 0;
  padding-left: 16px;
  font-size: 13px;
}

.option-item {
  margin: 3px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.question-answer {
  margin: 8px 0;
  padding: 6px 10px;
  background-color: var(--el-fill-color-extra-light);
  border-radius: 4px;
  font-size: 13px;
}

.question-explanation {
  margin-top: 8px;
}

.content-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid var(--el-border-color-lighter);
  font-size: 11px;
  color: var(--el-text-color-secondary);
}

.meta-info {
  display: flex;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.meta-info span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.score-editor {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Preview Dialog Styles */
.preview-content {
  max-height: 70vh;
  overflow-y: auto;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color);
}

.preview-header h3 {
  margin: 0;
  color: var(--el-color-primary);
}

.preview-tags {
  display: flex;
  gap: 4px;
}

.preview-body {
  line-height: 1.6;
}

.preview-stem {
  margin-bottom: 16px;
  font-size: 16px;
}

.preview-options {
  margin: 16px 0;
  padding-left: 20px;
}

.preview-option {
  margin: 8px 0;
  line-height: 1.5;
}

.preview-answer {
  margin: 16px 0;
  padding: 12px;
  background-color: var(--el-fill-color-extra-light);
  border-radius: 6px;
  font-weight: 500;
}

.preview-explanation {
  margin-top: 16px;
  padding: 12px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 6px;
}

.preview-explanation strong {
  display: block;
  margin-bottom: 8px;
  color: var(--el-color-primary);
}

/* Responsive improvements */
@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .question-item {
    flex-direction: column;
    gap: 8px;
  }
  
  .item-controls {
    flex-direction: row;
    width: 100%;
    justify-content: space-between;
  }
  
  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .content-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .meta-info {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .list-header {
    padding: 8px 12px;
  }
  
  .question-items {
    padding: 8px;
  }
  
  .question-item {
    padding: 8px;
  }
}
</style>