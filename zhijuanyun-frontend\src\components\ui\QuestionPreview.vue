<template>
  <div 
    class="question-preview" 
    :style="{ 
      fontSize: fontSize + 'px', 
      lineHeight: lineHeight 
    }"
  >
    <!-- Question Number and Score -->
    <div class="question-header">
      <span class="question-number">{{ number }}.</span>
      <span class="question-score">({{ question.score }}分)</span>
    </div>
    
    <!-- Question Stem -->
    <div class="question-stem" v-html="question.content.stem"></div>
    
    <!-- Options (for choice questions) -->
    <div 
      v-if="question.content.options && question.content.options.length > 0" 
      class="question-options"
    >
      <div 
        v-for="(option, index) in question.content.options" 
        :key="index"
        class="option-item"
      >
        {{ String.fromCharCode(65 + index) }}. {{ option }}
      </div>
    </div>
    
    <!-- Fill in the blank space for non-choice questions -->
    <div 
      v-else-if="['fill_blank', 'short_answer', 'essay'].includes(question.tags.questionType)"
      class="answer-space"
    >
      <div class="answer-lines">
        <div 
          v-for="i in getAnswerLines(question.tags.questionType)" 
          :key="i"
          class="answer-line"
        ></div>
      </div>
    </div>
    
    <!-- Answer (if showAnswer is true) -->
    <div v-if="showAnswer" class="question-answer">
      <strong>答案：</strong>{{ question.content.answer }}
    </div>
    
    <!-- Explanation (if showExplanation is true) -->
    <div v-if="showExplanation && question.content.explanation" class="question-explanation">
      <strong>解析：</strong>
      <div v-html="question.content.explanation"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Question } from '@/types'

interface Props {
  question: Question
  number: number
  showAnswer?: boolean
  showExplanation?: boolean
  fontSize?: number
  lineHeight?: number
}

withDefaults(defineProps<Props>(), {
  showAnswer: false,
  showExplanation: false,
  fontSize: 14,
  lineHeight: 1.5
})

const getAnswerLines = (questionType: string): number => {
  switch (questionType) {
    case 'fill_blank':
      return 2
    case 'short_answer':
      return 4
    case 'essay':
      return 8
    default:
      return 0
  }
}
</script>

<style scoped>
.question-preview {
  margin-bottom: 20px;
  padding: 16px 0;
  color: #333;
  line-height: 1.6;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  max-width: 100%;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  overflow: hidden;
}

.question-number {
  font-weight: bold;
  color: #333;
  flex-shrink: 0;
}

.question-score {
  font-size: 0.9em;
  color: #666;
  flex-shrink: 0;
}

.question-stem {
  margin-bottom: 12px;
  line-height: inherit;
  overflow: hidden;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

.question-options {
  margin: 12px 0;
  padding-left: 20px;
  overflow: hidden;
}

.option-item {
  margin: 6px 0;
  line-height: inherit;
  overflow: hidden;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.answer-space {
  margin: 16px 0;
  min-height: 60px;
}

.answer-lines {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.answer-line {
  height: 1px;
  border-bottom: 1px solid #333;
  margin: 12px 0;
  min-height: 20px;
}

.question-answer {
  margin: 12px 0;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
  font-size: 0.9em;
  overflow: hidden;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.question-explanation {
  margin: 12px 0;
  padding: 8px 12px;
  background-color: #fefce8;
  border: 1px solid #fde047;
  border-radius: 4px;
  font-size: 0.9em;
  overflow: hidden;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.question-explanation strong {
  display: block;
  margin-bottom: 4px;
  color: #a16207;
  overflow: hidden;
  word-wrap: break-word;
}

/* Print styles */
@media print {
  .question-preview {
    break-inside: avoid;
    page-break-inside: avoid;
  }
  
  .question-answer,
  .question-explanation {
    background-color: transparent !important;
    border: 1px solid #ccc !important;
  }
}
</style>