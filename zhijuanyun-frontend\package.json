{"name": "<PERSON><PERSON><PERSON><PERSON>un-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "test": "vitest"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fontsource/noto-sans-sc": "^5.2.6", "@vueup/vue-quill": "^1.2.0", "axios": "^1.11.0", "docx": "^9.5.1", "element-plus": "^2.10.4", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "pinia": "^3.0.3", "quill": "^2.0.3", "sass": "^1.89.2", "vue": "^3.5.17", "vue-draggable-plus": "^0.6.0", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@rollup/rollup-win32-x64-msvc": "^4.46.2", "@types/node": "^24.1.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.32.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "vite": "^5.4.10", "vitest": "^3.2.4", "vue-tsc": "^2.2.12"}}