var te=Object.defineProperty,ne=Object.defineProperties;var oe=Object.getOwnPropertyDescriptors;var X=Object.getOwnPropertySymbols;var se=Object.prototype.hasOwnProperty,ie=Object.prototype.propertyIsEnumerable;var Y=(f,l,s)=>l in f?te(f,l,{enumerable:!0,configurable:!0,writable:!0,value:s}):f[l]=s,G=(f,l)=>{for(var s in l||(l={}))se.call(l,s)&&Y(f,s,l[s]);if(X)for(var s of X(l))ie.call(l,s)&&Y(f,s,l[s]);return f},Z=(f,l)=>ne(f,oe(l));var $=(f,l,s)=>new Promise((e,r)=>{var S=u=>{try{d(s.next(u))}catch(c){r(c)}},a=u=>{try{d(s.throw(u))}catch(c){r(c)}},d=u=>u.done?e(u.value):Promise.resolve(u.value).then(S,a);d((s=s.apply(f,l)).next())});import{x as P,r as F,c as q,al as _,y as C,Q as n,I as i,z as y,P as I,a6 as O,H as U,j as re,K as j,A as t,M as w,O as V,ax as de,h as ue,D as B}from"./vendor-CPqkYfXn.js";import{a as x,u as ce,b as ee,l as me}from"./ui-CjjzzDsP.js";import{u as _e}from"./filter-t8MhSl_r.js";import{_ as D}from"./index-BdEKvwRr.js";import"./utils-DxgFcSvi.js";const pe=P({__name:"GradeSelect",props:{modelValue:{type:String,required:!1}},emits:["update:modelValue","change"],setup(f,{expose:l,emit:s}){l();const e=f,r=s,S=F([{label:"小学一年级",value:"grade1"},{label:"小学二年级",value:"grade2"},{label:"小学三年级",value:"grade3"},{label:"小学四年级",value:"grade4"},{label:"小学五年级",value:"grade5"},{label:"小学六年级",value:"grade6"},{label:"初中一年级",value:"grade7"},{label:"初中二年级",value:"grade8"},{label:"初中三年级",value:"grade9"},{label:"高中一年级",value:"grade10"},{label:"高中二年级",value:"grade11"},{label:"高中三年级",value:"grade12"}]),a=q({get:()=>e.modelValue||"",set:c=>r("update:modelValue",c)}),u={props:e,emit:r,grades:S,modelValue:a,handleChange:c=>{r("change",c),"vibrate"in navigator&&navigator.vibrate(50)}};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}}),ge={class:"grade-select"};function fe(f,l,s,e,r,S){const a=_("el-option"),d=_("el-select"),u=_("el-form-item");return y(),C("div",ge,[n(u,{label:"年级",required:""},{default:i(()=>[n(d,{modelValue:e.modelValue,"onUpdate:modelValue":l[0]||(l[0]=c=>e.modelValue=c),placeholder:"请选择年级",clearable:"",style:{width:"100%"},onChange:e.handleChange},{default:i(()=>[(y(!0),C(I,null,O(e.grades,c=>(y(),U(a,{key:c.value,label:c.label,value:c.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])}const he=D(pe,[["render",fe],["__scopeId","data-v-7c99d9e8"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/components/forms/GradeSelect.vue"]]),ve=P({__name:"SubjectSelect",props:{modelValue:{type:String,required:!1},grade:{type:String,required:!1}},emits:["update:modelValue","change"],setup(f,{expose:l,emit:s}){l();const e=f,r=s,S={expandTrigger:"hover",value:"value",label:"label",children:"children"},a=F([{label:"语文",value:"chinese",children:[{label:"基础知识",value:"chinese_basic"},{label:"阅读理解",value:"chinese_reading"},{label:"作文",value:"chinese_writing"}]},{label:"数学",value:"math",children:[{label:"代数",value:"math_algebra"},{label:"几何",value:"math_geometry"},{label:"概率统计",value:"math_statistics"},{label:"函数",value:"math_function"}]},{label:"英语",value:"english",children:[{label:"语法",value:"english_grammar"},{label:"词汇",value:"english_vocabulary"},{label:"阅读",value:"english_reading"},{label:"写作",value:"english_writing"}]},{label:"物理",value:"physics",children:[{label:"力学",value:"physics_mechanics"},{label:"电学",value:"physics_electricity"},{label:"光学",value:"physics_optics"},{label:"热学",value:"physics_thermodynamics"}]},{label:"化学",value:"chemistry",children:[{label:"无机化学",value:"chemistry_inorganic"},{label:"有机化学",value:"chemistry_organic"},{label:"物理化学",value:"chemistry_physical"},{label:"分析化学",value:"chemistry_analytical"}]},{label:"生物",value:"biology",children:[{label:"细胞生物学",value:"biology_cell"},{label:"遗传学",value:"biology_genetics"},{label:"生态学",value:"biology_ecology"},{label:"分子生物学",value:"biology_molecular"}]}]),d=q({get:()=>e.modelValue||"",set:h=>r("update:modelValue",h)}),c={props:e,emit:r,cascaderProps:S,subjectOptions:a,modelValue:d,handleChange:h=>{r("change",h),"vibrate"in navigator&&navigator.vibrate(50)}};return Object.defineProperty(c,"__isScriptSetup",{enumerable:!1,value:!0}),c}}),be={class:"subject-select"};function ye(f,l,s,e,r,S){const a=_("el-cascader"),d=_("el-form-item");return y(),C("div",be,[n(d,{label:"学科",required:""},{default:i(()=>[n(a,{modelValue:e.modelValue,"onUpdate:modelValue":l[0]||(l[0]=u=>e.modelValue=u),options:e.subjectOptions,props:e.cascaderProps,placeholder:"请选择学科",clearable:"",style:{width:"100%"},onChange:e.handleChange},null,8,["modelValue","options"])]),_:1})])}const Se=D(ve,[["render",ye],["__scopeId","data-v-e6a92026"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/components/forms/SubjectSelect.vue"]]),Ce=P({__name:"KnowledgePointSelect",props:{modelValue:{type:Array,required:!1},subject:{type:String,required:!1}},emits:["update:modelValue","change"],setup(f,{expose:l,emit:s}){l();const e=f,r=s,S=F(),a={children:"children",label:"label"},d=F([{id:"math_basic",label:"基础数学",children:[{id:"math_arithmetic",label:"四则运算"},{id:"math_fraction",label:"分数运算"},{id:"math_decimal",label:"小数运算"}]},{id:"math_algebra",label:"代数",children:[{id:"math_equation",label:"方程"},{id:"math_inequality",label:"不等式"},{id:"math_function",label:"函数"}]},{id:"math_geometry",label:"几何",children:[{id:"math_plane_geometry",label:"平面几何"},{id:"math_solid_geometry",label:"立体几何"},{id:"math_coordinate_geometry",label:"解析几何"}]},{id:"chinese_basic",label:"语文基础",children:[{id:"chinese_pinyin",label:"拼音"},{id:"chinese_character",label:"汉字"},{id:"chinese_word",label:"词语"}]},{id:"chinese_reading",label:"阅读理解",children:[{id:"chinese_modern_reading",label:"现代文阅读"},{id:"chinese_classical_reading",label:"文言文阅读"},{id:"chinese_poetry_reading",label:"诗歌鉴赏"}]}]),u=q({get:()=>e.modelValue||[],set:m=>r("update:modelValue",m)}),c=()=>{if(S.value){const m=S.value.getCheckedKeys(),b=S.value.getHalfCheckedKeys(),T=[...m,...b];u.value=T,r("change",T)}};re(()=>e.subject,m=>{m==="math"?d.value=d.value.filter(b=>b.id.startsWith("math_")):m==="chinese"?d.value=d.value.filter(b=>b.id.startsWith("chinese_")):d.value=[{id:"math_basic",label:"基础数学",children:[{id:"math_arithmetic",label:"四则运算"},{id:"math_fraction",label:"分数运算"},{id:"math_decimal",label:"小数运算"}]},{id:"math_algebra",label:"代数",children:[{id:"math_equation",label:"方程"},{id:"math_inequality",label:"不等式"},{id:"math_function",label:"函数"}]},{id:"math_geometry",label:"几何",children:[{id:"math_plane_geometry",label:"平面几何"},{id:"math_solid_geometry",label:"立体几何"},{id:"math_coordinate_geometry",label:"解析几何"}]},{id:"chinese_basic",label:"语文基础",children:[{id:"chinese_pinyin",label:"拼音"},{id:"chinese_character",label:"汉字"},{id:"chinese_word",label:"词语"}]},{id:"chinese_reading",label:"阅读理解",children:[{id:"chinese_modern_reading",label:"现代文阅读"},{id:"chinese_classical_reading",label:"文言文阅读"},{id:"chinese_poetry_reading",label:"诗歌鉴赏"}]}]},{immediate:!0});const h={props:e,emit:r,treeRef:S,treeProps:a,knowledgePoints:d,modelValue:u,handleCheck:c};return Object.defineProperty(h,"__isScriptSetup",{enumerable:!1,value:!0}),h}}),we={class:"knowledge-point-select"};function Ve(f,l,s,e,r,S){const a=_("el-tree"),d=_("el-form-item");return y(),C("div",we,[n(d,{label:"知识点"},{default:i(()=>[n(a,{ref:"treeRef",data:e.knowledgePoints,props:e.treeProps,"show-checkbox":"","node-key":"id","default-checked-keys":e.modelValue,onCheck:e.handleCheck,style:{"max-height":"300px","overflow-y":"auto",border:"1px solid var(--el-border-color)","border-radius":"4px",padding:"8px"}},null,8,["data","default-checked-keys"])]),_:1})])}const xe=D(Ce,[["render",Ve],["__scopeId","data-v-7fc1c5c5"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/components/forms/KnowledgePointSelect.vue"]]),qe=P({__name:"QuestionTypeDifficultySelect",props:{modelValueQuestionTypes:{type:Array,required:!1},modelValueDifficulty:{type:Array,required:!1}},emits:["update:modelValueQuestionTypes","update:modelValueDifficulty","change"],setup(f,{expose:l,emit:s}){l();const e=f,r=s,S=F([{label:"单选题",value:"single_choice"},{label:"多选题",value:"multiple_choice"},{label:"判断题",value:"true_false"},{label:"填空题",value:"fill_blank"},{label:"简答题",value:"short_answer"},{label:"解答题",value:"essay"},{label:"计算题",value:"calculation"},{label:"应用题",value:"application"},{label:"分析题",value:"analysis"},{label:"综合题",value:"comprehensive"}]),a=F([{label:"容易",value:"easy"},{label:"中等",value:"medium"},{label:"困难",value:"hard"},{label:"很难",value:"very_hard"}]),d=q({get:()=>e.modelValueQuestionTypes||[],set:b=>r("update:modelValueQuestionTypes",b)}),u=q({get:()=>e.modelValueDifficulty||[],set:b=>r("update:modelValueDifficulty",b)}),m={props:e,emit:r,questionTypeOptions:S,difficultyOptions:a,questionTypes:d,difficulty:u,handleQuestionTypeChange:b=>{r("change","questionTypes",b)},handleDifficultyChange:b=>{r("change","difficulty",b)}};return Object.defineProperty(m,"__isScriptSetup",{enumerable:!1,value:!0}),m}}),Fe={class:"question-type-difficulty-select"};function ke(f,l,s,e,r,S){const a=_("el-option"),d=_("el-select"),u=_("el-form-item"),c=_("el-col"),h=_("el-row");return y(),C("div",Fe,[n(h,{gutter:16},{default:i(()=>[n(c,{span:12},{default:i(()=>[n(u,{label:"题型"},{default:i(()=>[n(d,{modelValue:e.questionTypes,"onUpdate:modelValue":l[0]||(l[0]=m=>e.questionTypes=m),multiple:"",placeholder:"请选择题型",clearable:"",style:{width:"100%"},onChange:e.handleQuestionTypeChange},{default:i(()=>[(y(!0),C(I,null,O(e.questionTypeOptions,m=>(y(),U(a,{key:m.value,label:m.label,value:m.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),n(c,{span:12},{default:i(()=>[n(u,{label:"难度"},{default:i(()=>[n(d,{modelValue:e.difficulty,"onUpdate:modelValue":l[1]||(l[1]=m=>e.difficulty=m),multiple:"",placeholder:"请选择难度",clearable:"",style:{width:"100%"},onChange:e.handleDifficultyChange},{default:i(()=>[(y(!0),C(I,null,O(e.difficultyOptions,m=>(y(),U(a,{key:m.value,label:m.label,value:m.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})])}const Te=D(qe,[["render",ke],["__scopeId","data-v-141b6285"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/components/forms/QuestionTypeDifficultySelect.vue"]]),je=P({__name:"RegionCountSelect",props:{modelValueRegion:{type:String,required:!1},modelValueQuestionCount:{type:Number,required:!1}},emits:["update:modelValueRegion","update:modelValueQuestionCount","change"],setup(f,{expose:l,emit:s}){l();const e=f,r=s,S=F([{label:"全国",value:"national"},{label:"北京",value:"beijing"},{label:"上海",value:"shanghai"},{label:"广东",value:"guangdong"},{label:"浙江",value:"zhejiang"},{label:"江苏",value:"jiangsu"},{label:"山东",value:"shandong"},{label:"河南",value:"henan"},{label:"四川",value:"sichuan"},{label:"湖南",value:"hunan"},{label:"湖北",value:"hubei"},{label:"河北",value:"hebei"},{label:"安徽",value:"anhui"},{label:"福建",value:"fujian"},{label:"江西",value:"jiangxi"},{label:"辽宁",value:"liaoning"},{label:"黑龙江",value:"heilongjiang"},{label:"吉林",value:"jilin"},{label:"山西",value:"shanxi"},{label:"陕西",value:"shaanxi"},{label:"甘肃",value:"gansu"},{label:"青海",value:"qinghai"},{label:"新疆",value:"xinjiang"},{label:"西藏",value:"tibet"},{label:"内蒙古",value:"neimenggu"},{label:"广西",value:"guangxi"},{label:"宁夏",value:"ningxia"},{label:"海南",value:"hainan"},{label:"重庆",value:"chongqing"},{label:"天津",value:"tianjin"},{label:"云南",value:"yunnan"},{label:"贵州",value:"guizhou"}]),a=q({get:()=>e.modelValueRegion||"",set:m=>r("update:modelValueRegion",m)}),d=q({get:()=>e.modelValueQuestionCount||10,set:m=>r("update:modelValueQuestionCount",m)}),h={props:e,emit:r,regionOptions:S,region:a,questionCount:d,handleRegionChange:m=>{r("change","region",m)},handleCountChange:m=>{const b=m||10;b<1?r("change","questionCount",1):b>100?r("change","questionCount",100):r("change","questionCount",b),"vibrate"in navigator&&navigator.vibrate(50)}};return Object.defineProperty(h,"__isScriptSetup",{enumerable:!1,value:!0}),h}}),Pe={class:"region-count-select"};function De(f,l,s,e,r,S){const a=_("el-option"),d=_("el-select"),u=_("el-form-item"),c=_("el-col"),h=_("el-input-number"),m=_("el-row");return y(),C("div",Pe,[n(m,{gutter:16},{default:i(()=>[n(c,{span:12},{default:i(()=>[n(u,{label:"地区"},{default:i(()=>[n(d,{modelValue:e.region,"onUpdate:modelValue":l[0]||(l[0]=b=>e.region=b),placeholder:"请选择地区",clearable:"",style:{width:"100%"},onChange:e.handleRegionChange},{default:i(()=>[(y(!0),C(I,null,O(e.regionOptions,b=>(y(),U(a,{key:b.value,label:b.label,value:b.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),n(c,{span:12},{default:i(()=>[n(u,{label:"题目数量",required:""},{default:i(()=>[n(h,{modelValue:e.questionCount,"onUpdate:modelValue":l[1]||(l[1]=b=>e.questionCount=b),min:1,max:100,step:1,placeholder:"请输入题目数量",style:{width:"100%"},onChange:e.handleCountChange},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})])}const ze=D(je,[["render",De],["__scopeId","data-v-32e91ed3"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/components/forms/RegionCountSelect.vue"]]),Re=".doc,.docx,.pdf,.jpg,.jpeg,.png",Ie=P({__name:"SampleUpload",props:{modelValue:{type:[Object,null],required:!1}},emits:["update:modelValue","change"],setup(f,{expose:l,emit:s}){l();const e=f,r=s,S=F(),a=F(0),d=F(""),u=q(()=>"http://ai.procaas.com:3000/api/upload"),c=q({get:()=>e.modelValue,set:g=>r("update:modelValue",g)}),A={props:e,emit:r,uploadRef:S,uploadProgress:a,uploadStatus:d,uploadAction:u,acceptedTypes:Re,uploadedFile:c,beforeUpload:g=>{if(!["application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/pdf","image/jpeg","image/jpg","image/png"].includes(g.type))return x.error("文件格式不支持！请上传 Word、PDF 或图片文件"),!1;const z=20*1024*1024;return g.size>z?(x.error("文件大小不能超过 20MB!"),!1):(a.value=0,d.value="",!0)},handleProgress:g=>{a.value=Math.round(g.percent)},handleSuccess:g=>{if(a.value=100,d.value="success",g&&g.data){const k={id:g.data.id,filename:g.data.filename,size:g.data.size,uploadTime:g.data.uploadTime||new Date().toISOString(),status:g.data.status||"processing"};c.value=k,r("change",k),x.success("文件上传成功!")}},handleError:g=>{a.value=0,d.value="exception",console.error("Upload error:",g),x.error("文件上传失败，请重试")},handleRemove:()=>{c.value=null,a.value=0,d.value="",r("change",null),x.info("文件已移除")},handlePreview:g=>{g.url&&window.open(g.url,"_blank")},handleExceed:()=>{x.warning("只能上传一个文件，请先移除当前文件")},formatFileSize:g=>{if(g===0)return"0 B";const k=1024,z=["B","KB","MB","GB"],R=Math.floor(Math.log(g)/Math.log(k));return Math.round(g/Math.pow(k,R)*100)/100+" "+z[R]},formatDateTime:g=>new Date(g).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}),getStatusType:g=>{switch(g){case"completed":return"success";case"processing":return"warning";case"failed":return"danger";default:return"info"}},getStatusText:g=>{switch(g){case"completed":return"解析完成";case"processing":return"解析中";case"failed":return"解析失败";default:return"未知状态"}},get UploadFilled(){return ce}};return Object.defineProperty(A,"__isScriptSetup",{enumerable:!1,value:!0}),A}}),Oe={class:"sample-upload"},Ue={key:0,class:"upload-progress"},Qe={class:"progress-text"},Ae={key:1,class:"upload-success"},Be={class:"file-info"};function Ee(f,l,s,e,r,S){const a=_("el-icon"),d=_("el-upload"),u=_("el-progress"),c=_("el-alert"),h=_("el-tag"),m=_("el-form-item");return y(),C("div",Oe,[n(m,{label:"样题上传"},{default:i(()=>[n(d,{ref:"uploadRef",class:"upload-demo",drag:!0,action:e.uploadAction,multiple:!1,"show-file-list":!0,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload,"on-success":e.handleSuccess,"on-error":e.handleError,"on-progress":e.handleProgress,accept:e.acceptedTypes,limit:1,"on-exceed":e.handleExceed},{tip:i(()=>l[0]||(l[0]=[t("div",{class:"el-upload__tip"}," 支持 Word(.doc/.docx)、PDF(.pdf)、图片(.jpg/.png) 格式，文件大小不超过 20MB ",-1)])),default:i(()=>[n(a,{class:"el-icon--upload"},{default:i(()=>[n(e.UploadFilled)]),_:1}),l[1]||(l[1]=t("div",{class:"el-upload__text"},[w(" 将文件拖到此处，或"),t("em",null,"点击上传")],-1))]),_:1,__:[1]},8,["action"]),e.uploadProgress>0&&e.uploadProgress<100?(y(),C("div",Ue,[n(u,{percentage:e.uploadProgress,status:e.uploadStatus,"stroke-width":6},null,8,["percentage","status"]),t("p",Qe,"上传中... "+V(e.uploadProgress)+"%",1)])):j("",!0),e.uploadedFile?(y(),C("div",Ae,[n(c,{title:`文件 ${e.uploadedFile.filename} 上传成功`,type:"success",closable:!1,"show-icon":""},null,8,["title"]),t("div",Be,[t("p",null,[l[2]||(l[2]=t("strong",null,"文件名:",-1)),w(" "+V(e.uploadedFile.filename),1)]),t("p",null,[l[3]||(l[3]=t("strong",null,"文件大小:",-1)),w(" "+V(e.formatFileSize(e.uploadedFile.size)),1)]),t("p",null,[l[4]||(l[4]=t("strong",null,"上传时间:",-1)),w(" "+V(e.formatDateTime(e.uploadedFile.uploadTime)),1)]),t("p",null,[l[5]||(l[5]=t("strong",null,"处理状态:",-1)),n(h,{type:e.getStatusType(e.uploadedFile.status),size:"small"},{default:i(()=>[w(V(e.getStatusText(e.uploadedFile.status)),1)]),_:1},8,["type"])])])])):j("",!0)]),_:1})])}const Ke=D(Ie,[["render",Ee],["__scopeId","data-v-556f03dd"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/components/forms/SampleUpload.vue"]]),Ne=P({__name:"index",setup(f,{expose:l}){l();const s=_e(),e=de(),r=F(),S=F(!1),a=F({grade:"",subject:"",knowledgePoints:[],questionTypes:[],difficulty:[],region:"",questionCount:10,sampleFile:void 0}),d=q(()=>s.loading),u=q(()=>s.questions),c=q(()=>s.error),h=q(()=>s.progress),m=o=>{a.value.grade=o,a.value.subject="",a.value.knowledgePoints=[]},b=o=>{a.value.subject=o,a.value.knowledgePoints=[]},T=o=>{a.value.knowledgePoints=o},E=(o,v)=>{o==="questionTypes"?a.value.questionTypes=v:a.value.difficulty=v},p=(o,v)=>{o==="region"?a.value.region=v:a.value.questionCount=v},K=o=>{a.value.sampleFile=o||void 0},N=()=>$(this,null,function*(){const o=A();if(o.length>0){x.error(`请完善以下信息：${o.join("、")}`);return}try{x.info("正在使用AI智能组卷，请稍候..."),s.updateFilterParams(a.value),k(),yield s.generatePaperWithAI(),S.value=!0,u.value.length>0&&(x.success(`AI组卷成功，生成 ${u.value.length} 道题目`),L({params:a.value,resultCount:u.value.length,timestamp:new Date().toISOString()}))}catch(v){console.error("AI paper generation error:",v);const M=v instanceof Error?v.message:"AI组卷失败";g(M)}}),Q=()=>{ee.confirm("确定要重置所有筛选条件吗？","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{a.value={grade:"",subject:"",knowledgePoints:[],questionTypes:[],difficulty:[],region:"",questionCount:10,sampleFile:void 0},s.resetFilter(),S.value=!1,R(),x.success("筛选条件已重置")}).catch(()=>{})},H=()=>{S.value=!1,s.clearQuestions()},W=()=>{k(),e.push("/edit")},A=()=>{const o=[];return a.value.grade||o.push("年级"),a.value.subject||o.push("学科"),(a.value.questionCount<=0||a.value.questionCount>100)&&o.push("题目数量（1-100）"),a.value.subject&&a.value.knowledgePoints.length===0&&x.warning("建议选择相关知识点以获得更精准的组卷结果"),o},g=o=>{let v="";o.includes("timeout")||o.includes("超时")?v="建议：减少题目数量或稍后重试":o.includes("网络")||o.includes("network")?v="建议：检查网络连接后重试":o.includes("重试")||o.includes("retry")?v="建议：稍后重试或联系技术支持":o.includes("参数")||o.includes("parameter")?v="建议：检查筛选条件后重试":v="建议：检查筛选条件或稍后重试",x.error(`${o}。${v}`)},k=()=>{try{const o=Z(G({},a.value),{savedAt:new Date().toISOString()});localStorage.setItem("questionFilterState",JSON.stringify(o))}catch(o){console.warn("Failed to save form state:",o)}},z=()=>{try{const o=localStorage.getItem("questionFilterState");if(o){const v=JSON.parse(o),M=new Date(v.savedAt);if((new Date().getTime()-M.getTime())/(1e3*60*60)<24)return v}}catch(o){console.warn("Failed to load form state:",o)}return null},R=()=>{try{localStorage.removeItem("questionFilterState")}catch(o){console.warn("Failed to clear form state:",o)}},L=o=>{try{const v=JSON.parse(localStorage.getItem("searchHistory")||"[]");v.unshift(o),v.length>10&&v.splice(10),localStorage.setItem("searchHistory",JSON.stringify(v))}catch(v){console.warn("Failed to save search result:",v)}},le=()=>h.value<25?"正在分析您的组卷需求...":h.value<50?"正在智能匹配题目...":h.value<75?"正在生成题目内容...":h.value<100?"正在优化题目质量...":"组卷完成！",ae=()=>h.value<25?"#409EFF":h.value<50?"#67C23A":h.value<75?"#E6A23C":"#F56C6C";ue(()=>{const o=z();if(o)a.value={grade:o.grade||"",subject:o.subject||"",knowledgePoints:o.knowledgePoints||[],questionTypes:o.questionTypes||[],difficulty:o.difficulty||[],region:o.region||"",questionCount:o.questionCount||10,sampleFile:o.sampleFile||void 0},ee.confirm("检测到上次未完成的筛选，是否恢复？","恢复筛选条件",{confirmButtonText:"恢复",cancelButtonText:"重新开始",type:"info"}).then(()=>{s.updateFilterParams(a.value),x.success("已恢复上次筛选条件")}).catch(()=>{R()});else{const v=s.filterParams;v&&(v.grade||v.subject)&&(a.value=G({},v))}});const J={filterStore:s,router:e,formRef:r,hasSearched:S,filterForm:a,loading:d,questions:u,error:c,progress:h,handleGradeChange:m,handleSubjectChange:b,handleKnowledgePointChange:T,handleTypeDifficultyChange:E,handleRegionCountChange:p,handleSampleChange:K,handleSearch:N,handleReset:Q,handleNewSearch:H,handleContinueEdit:W,validateForm:A,handleErrorWithGuidance:g,saveFormState:k,loadFormState:z,clearFormState:R,saveSearchResult:L,getProgressMessage:le,getProgressColor:ae,get Loading(){return me},GradeSelect:he,SubjectSelect:Se,KnowledgePointSelect:xe,QuestionTypeDifficultySelect:Te,RegionCountSelect:ze,SampleUpload:Ke};return Object.defineProperty(J,"__isScriptSetup",{enumerable:!1,value:!0}),J}}),Me={class:"question-filter"},Ge={class:"card-header"},He={class:"header-actions"},We={class:"filter-content"},Le={class:"search-actions"},Je={class:"card-header"},Xe={class:"results-content"},Ye={key:0,class:"loading-state"},Ze={class:"loading-animation"},$e={class:"loading-steps"},el={class:"loading-text"},ll={key:0,class:"progress-container"},al={class:"progress-text"},tl={key:1,class:"error-state"},nl={key:2,class:"empty-state"},ol={key:3,class:"questions-list"},sl={class:"question-header"},il={class:"question-number"},rl={class:"question-tags"},dl={class:"question-content"},ul=["innerHTML"],cl={key:0,class:"question-options"},ml={class:"question-answer"},_l={key:1,class:"question-explanation"},pl={class:"question-meta"},gl={class:"list-actions"};function fl(f,l,s,e,r,S){const a=_("el-button"),d=_("el-form-item"),u=_("el-form"),c=_("el-card"),h=_("el-tag"),m=_("el-icon"),b=_("el-progress"),T=_("el-alert"),E=_("el-empty");return y(),C("div",Me,[n(c,null,{header:i(()=>[t("div",Ge,[l[10]||(l[10]=t("span",null,"题目筛选",-1)),t("div",He,[n(a,{onClick:e.handleReset,type:"info",size:"small"},{default:i(()=>l[8]||(l[8]=[w("重置",-1)])),_:1,__:[8]}),n(a,{onClick:e.handleSearch,type:"primary",size:"small",loading:e.loading},{default:i(()=>l[9]||(l[9]=[w(" AI组卷 ",-1)])),_:1,__:[9]},8,["loading"])])])]),default:i(()=>[t("div",We,[n(u,{model:e.filterForm,ref:"formRef","label-width":"80px"},{default:i(()=>[n(e.GradeSelect,{modelValue:e.filterForm.grade,"onUpdate:modelValue":l[0]||(l[0]=p=>e.filterForm.grade=p),onChange:e.handleGradeChange},null,8,["modelValue"]),n(e.SubjectSelect,{modelValue:e.filterForm.subject,"onUpdate:modelValue":l[1]||(l[1]=p=>e.filterForm.subject=p),grade:e.filterForm.grade,onChange:e.handleSubjectChange},null,8,["modelValue","grade"]),n(e.KnowledgePointSelect,{modelValue:e.filterForm.knowledgePoints,"onUpdate:modelValue":l[2]||(l[2]=p=>e.filterForm.knowledgePoints=p),subject:e.filterForm.subject,onChange:e.handleKnowledgePointChange},null,8,["modelValue","subject"]),n(e.QuestionTypeDifficultySelect,{"model-value-question-types":e.filterForm.questionTypes,"onUpdate:modelValueQuestionTypes":l[3]||(l[3]=p=>e.filterForm.questionTypes=p),"model-value-difficulty":e.filterForm.difficulty,"onUpdate:modelValueDifficulty":l[4]||(l[4]=p=>e.filterForm.difficulty=p),onChange:e.handleTypeDifficultyChange},null,8,["model-value-question-types","model-value-difficulty"]),n(e.RegionCountSelect,{"model-value-region":e.filterForm.region,"onUpdate:modelValueRegion":l[5]||(l[5]=p=>e.filterForm.region=p),"model-value-question-count":e.filterForm.questionCount,"onUpdate:modelValueQuestionCount":l[6]||(l[6]=p=>e.filterForm.questionCount=p),onChange:e.handleRegionCountChange},null,8,["model-value-region","model-value-question-count"]),n(e.SampleUpload,{modelValue:e.filterForm.sampleFile,"onUpdate:modelValue":l[7]||(l[7]=p=>e.filterForm.sampleFile=p),onChange:e.handleSampleChange},null,8,["modelValue"]),n(d,null,{default:i(()=>[t("div",Le,[n(a,{onClick:e.handleReset,size:"default"},{default:i(()=>l[11]||(l[11]=[w("重置筛选",-1)])),_:1,__:[11]}),n(a,{type:"primary",onClick:e.handleSearch,loading:e.loading,size:"default"},{default:i(()=>[w(" 开始搜索 ("+V(e.filterForm.questionCount)+" 题) ",1)]),_:1},8,["loading"])])]),_:1})]),_:1},8,["model"])])]),_:1}),e.hasSearched?(y(),U(c,{key:0,class:"results-card"},{header:i(()=>[t("div",Je,[l[12]||(l[12]=t("span",null,"AI组卷结果",-1)),e.loading?j("",!0):(y(),U(h,{key:0,type:"info",size:"small"},{default:i(()=>[w(" 共生成 "+V(e.questions.length)+" 道题目 ",1)]),_:1}))])]),default:i(()=>[t("div",Xe,[e.loading?(y(),C("div",Ye,[t("div",Ze,[n(m,{class:"loading-icon"},{default:i(()=>[n(e.loading)]),_:1}),t("div",$e,[t("div",{class:B(["step",{active:e.progress>=0}])},l[13]||(l[13]=[t("span",{class:"step-number"},"1",-1),t("span",{class:"step-text"},"分析需求",-1)]),2),t("div",{class:B(["step",{active:e.progress>=25}])},l[14]||(l[14]=[t("span",{class:"step-number"},"2",-1),t("span",{class:"step-text"},"智能匹配",-1)]),2),t("div",{class:B(["step",{active:e.progress>=50}])},l[15]||(l[15]=[t("span",{class:"step-number"},"3",-1),t("span",{class:"step-text"},"生成题目",-1)]),2),t("div",{class:B(["step",{active:e.progress>=75}])},l[16]||(l[16]=[t("span",{class:"step-number"},"4",-1),t("span",{class:"step-text"},"优化调整",-1)]),2),t("div",{class:B(["step",{active:e.progress>=100}])},l[17]||(l[17]=[t("span",{class:"step-number"},"5",-1),t("span",{class:"step-text"},"完成组卷",-1)]),2)])]),t("div",el,[l[18]||(l[18]=t("h3",null,"AI智能组卷中...",-1)),t("p",null,V(e.getProgressMessage()),1),e.progress>0?(y(),C("div",ll,[n(b,{percentage:e.progress,"stroke-width":8,"show-text":!0,status:"active",color:e.getProgressColor()},null,8,["percentage","color"]),t("div",al,V(e.progress)+"%",1)])):j("",!0)])])):e.error?(y(),C("div",tl,[n(T,{title:e.error,type:"error","show-icon":"",closable:!1},null,8,["title"]),n(a,{onClick:e.handleSearch,type:"primary",style:{"margin-top":"16px"}},{default:i(()=>l[19]||(l[19]=[w(" 重新搜索 ",-1)])),_:1,__:[19]})])):e.questions.length===0?(y(),C("div",nl,[n(E,{description:"未找到符合条件的题目"},{default:i(()=>[n(a,{onClick:e.handleSearch,type:"primary"},{default:i(()=>l[20]||(l[20]=[w("重新搜索",-1)])),_:1,__:[20]})]),_:1})])):(y(),C("div",ol,[(y(!0),C(I,null,O(e.questions,(p,K)=>(y(),C("div",{key:p.id,class:"question-item"},[n(c,{shadow:"hover"},{default:i(()=>[t("div",sl,[t("span",il,"第 "+V(K+1)+" 题",1),t("div",rl,[n(h,{size:"small",type:"info"},{default:i(()=>[w(V(p.tags.questionType),1)]),_:2},1024),n(h,{size:"small",type:"warning"},{default:i(()=>[w(V(p.tags.difficulty),1)]),_:2},1024),n(h,{size:"small"},{default:i(()=>[w(V(p.score)+"分",1)]),_:2},1024)])]),t("div",dl,[t("div",{class:"question-stem",innerHTML:p.content.stem},null,8,ul),p.content.options&&p.content.options.length>0?(y(),C("div",cl,[(y(!0),C(I,null,O(p.content.options,(N,Q)=>(y(),C("div",{key:Q,class:"option-item"},V(String.fromCharCode(65+Q))+". "+V(N),1))),128))])):j("",!0),t("div",ml,[l[21]||(l[21]=t("strong",null,"答案：",-1)),w(V(p.content.answer),1)]),p.content.explanation?(y(),C("div",_l,[l[22]||(l[22]=t("strong",null,"解析：",-1)),w(V(p.content.explanation),1)])):j("",!0)]),t("div",pl,[t("span",null,"知识点："+V(p.tags.knowledgePoint.join("、")),1),t("span",null,"来源："+V(p.tags.sourceType),1)])]),_:2},1024)]))),128)),t("div",gl,[n(a,{onClick:e.handleContinueEdit,type:"primary",size:"large"},{default:i(()=>l[23]||(l[23]=[w(" 继续编辑题目 ",-1)])),_:1,__:[23]}),n(a,{onClick:e.handleNewSearch,size:"large"},{default:i(()=>l[24]||(l[24]=[w(" 重新组卷 ",-1)])),_:1,__:[24]})])]))])]),_:1})):j("",!0)])}const xl=D(Ne,[["render",fl],["__scopeId","data-v-52d9478b"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/views/QuestionFilter/index.vue"]]);export{xl as default};
