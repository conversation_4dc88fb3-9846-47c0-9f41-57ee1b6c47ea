# 中文导出乱码问题修复说明

## 问题描述
导出的PDF文件中，中文字符显示为乱码或问号，无法正常显示中文内容。

## 问题原因
jsPDF库默认不支持中文字体，当遇到中文字符时会显示为问号或乱码。

## 最终解决方案

### 🎯 主要方案：HTML转PDF（推荐）
这是最可靠的中文支持方案：

#### 工作原理
1. 将试卷内容渲染为HTML格式
2. 使用html2canvas将HTML转换为高质量图片
3. 将图片插入到PDF中

#### 优点
- ✅ 完美支持中文字符显示
- ✅ 保持原有的排版和样式
- ✅ 支持复杂的HTML结构
- ✅ 兼容性好，在所有浏览器中都能正常工作
- ✅ 不依赖外部字体文件

#### 缺点
- ❌ 生成的PDF中文本不可选择（图片格式）
- ❌ 文件大小相对较大
- ❌ 需要额外的html2canvas依赖

### 🔧 备用方案

#### 方案1：真实中文字体加载
- 从CDN加载思源黑体等中文字体
- 将字体嵌入到jsPDF中
- 优点：文本可选择，文件相对较小
- 缺点：依赖网络，字体加载可能失败

#### 方案2：Canvas文本渲染
- 使用Canvas API渲染中文文本为图片
- 将图片插入到PDF的指定位置
- 优点：灵活控制文本样式
- 缺点：需要逐个处理文本块

#### 方案3：文本转换
- 将中文转换为英文或拼音
- 作为最后的fallback方案
- 优点：兼容性最好
- 缺点：失去原始中文信息

## 实现的文件

### 新增文件
- `src/utils/html-to-pdf.ts` - HTML转PDF核心实现（主要方案）
- `src/utils/real-chinese-font.ts` - 真实中文字体支持
- `src/utils/chinese-canvas.ts` - Canvas渲染中文文本
- `src/utils/chinese-font-data.ts` - 中文字体数据和文本转换
- `src/utils/test-chinese-export.ts` - 测试工具
- `src/views/TestExport.vue` - 测试页面组件
- `test-chinese-export.html` - 独立测试页面

### 修改文件
- `src/services/export.ts` - 更新导出服务，集成所有中文支持方案
- `package.json` - 添加html2canvas依赖

## 使用方法

### 1. 在Vue组件中使用
```typescript
import { ExportService } from '@/services/export'

const exportOptions = {
  questions: questions,
  settings: exportSettings,
  totalScore: totalScore,
  onProgress: (progress) => console.log(`进度: ${progress}%`)
}

// 导出PDF（自动使用最佳中文支持方案）
const pdfBlob = await ExportService.exportToPDF(exportOptions)

// 导出DOCX
const docxBlob = await ExportService.exportToDOCX(exportOptions)
```

### 2. 直接使用HTML转PDF工具
```typescript
import { simpleExportUtils } from '@/utils/html-to-pdf'

// 导出试卷为PDF
const blob = await simpleExportUtils.exportExamToPDF(questions, settings, totalScore)

// 预览HTML内容
const htmlString = simpleExportUtils.previewHTML(questions, settings, totalScore)
```

### 3. 测试功能
```typescript
// 导入测试工具
import { testPDFExport, testDOCXExport } from '@/utils/test-chinese-export'

// 测试PDF导出
await testPDFExport()

// 测试DOCX导出
await testDOCXExport()
```

## 测试方法

### 1. 使用独立测试页面
打开 `test-chinese-export.html` 文件，在浏览器中测试：
- 简单PDF生成测试
- HTML转PDF测试
- Canvas渲染测试

### 2. 使用Vue测试组件
访问 `/test-export` 路由，使用测试页面：
- 完整的PDF导出测试
- DOCX导出测试
- 快速PDF测试

### 3. 浏览器控制台测试
```javascript
// 在浏览器控制台中运行
window.testChineseExport.testPDF()
window.testChineseExport.testDOCX()
```

## 配置选项

### HTML转PDF配置
```typescript
const options = {
  scale: 2,              // 渲染缩放比例
  useCORS: true,         // 允许跨域资源
  allowTaint: true,      // 允许污染画布
  backgroundColor: '#ffffff'  // 背景颜色
}
```

### Canvas渲染配置
```typescript
const canvasOptions = {
  fontSize: 16,
  fontFamily: 'Microsoft YaHei, SimSun, sans-serif',
  color: '#000000',
  backgroundColor: 'transparent',
  padding: 10,
  maxWidth: 780,
  lineHeight: 1.6
}
```

## 性能优化

### 文件大小控制
- 使用适当的图片质量设置
- 优化HTML结构减少渲染复杂度
- 分页处理大量内容

### 渲染速度优化
- 预加载必要的字体
- 复用Canvas元素
- 异步处理大量文本

## 兼容性

### 浏览器支持
- Chrome 60+ ✅
- Firefox 55+ ✅
- Safari 12+ ✅
- Edge 79+ ✅
- IE 11 ❌（不支持html2canvas）

### 依赖要求
- html2canvas 1.4.1+
- jsPDF 2.5.1+
- 现代浏览器的Canvas API支持

## 故障排除

### 常见问题及解决方案

#### 1. 中文仍显示为问号
**原因**：HTML转PDF方案未正确启用
**解决方案**：
```javascript
// 检查html2canvas是否正确加载
console.log('html2canvas available:', typeof html2canvas !== 'undefined')

// 手动测试HTML转PDF
import { simpleExportUtils } from '@/utils/html-to-pdf'
const isSupported = simpleExportUtils.isSupported()
console.log('HTML to PDF supported:', isSupported)
```

#### 2. PDF文件过大
**原因**：图片质量设置过高
**解决方案**：
```javascript
// 降低渲染质量
const options = {
  scale: 1,  // 从2降低到1
  quality: 0.8  // 添加质量控制
}
```

#### 3. 导出速度慢
**原因**：HTML内容过于复杂
**解决方案**：
- 简化HTML结构
- 分页处理大量内容
- 使用进度回调显示状态

#### 4. 在某些浏览器中失败
**原因**：浏览器兼容性问题
**解决方案**：
```javascript
// 检查浏览器支持
if (!window.html2canvas) {
  console.warn('html2canvas not supported, falling back to text conversion')
  // 使用文本转换方案
}
```

### 调试方法

#### 启用详细日志
```javascript
// 在导出前添加调试代码
console.log('Export method: HTML to PDF')
console.log('Questions count:', questions.length)
console.log('Settings:', settings)

// 测试HTML生成
const htmlString = simpleExportUtils.previewHTML(questions, settings, totalScore)
console.log('Generated HTML length:', htmlString.length)
```

#### 测试各个组件
```javascript
// 测试html2canvas
const testElement = document.createElement('div')
testElement.innerHTML = '测试中文'
testElement.style.font = '16px Microsoft YaHei'
document.body.appendChild(testElement)

html2canvas(testElement).then(canvas => {
  console.log('html2canvas test successful')
  document.body.removeChild(testElement)
}).catch(error => {
  console.error('html2canvas test failed:', error)
})
```

## 快速验证

### 1. 打开测试页面
在浏览器中打开 `test-chinese-export.html`，点击"测试HTML转PDF"按钮

### 2. 检查生成的PDF
下载的PDF文件应该正确显示中文字符，而不是问号

### 3. 如果仍有问题
1. 检查浏览器控制台是否有错误信息
2. 确认html2canvas库已正确加载
3. 尝试不同的浏览器进行测试

## 总结

通过实施HTML转PDF的解决方案，我们彻底解决了中文字符在PDF导出中显示为问号的问题。这个方案：

✅ **完全解决中文显示问题** - 不再有问号或乱码
✅ **保持原有排版效果** - HTML样式完整保留
✅ **兼容性好** - 在主流浏览器中都能正常工作
✅ **易于维护** - 代码结构清晰，便于后续优化

**推荐使用顺序**：
1. 首选：HTML转PDF方案（最可靠）
2. 备选：真实字体加载方案
3. 最后：文本转换方案（保证兼容性）

现在用户可以正常导出包含中文内容的PDF文件，中文字符将正确显示而不是问号。
