<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>上传文件接口文档 - Apifox风格</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0 auto;
            max-width: 960px;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        pre {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            background-color: #eee;
            padding: 2px 4px;
            border-radius: 3px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 8px;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>

<h1>📄 接口名称：上传文件</h1>

<h2>🔗 请求地址</h2>
<pre>POST http://ai.procaas.com:3000/api/application/{application_id}/chat/{chat_id}/upload_file</pre>
<p><strong>示例完整 URL：</strong></p>
<pre>http://ai.procaas.com:3000/api/application/2497567c-4a62-11f0-bbb8-00163e4f3d7b/chat/72c0eef2-509e-11f0-bbb8-00163e4f3d7b/upload_file</pre>

<h2>⚙️ 请求头（Headers）</h2>
<table>
    <tr><th>Key</th><th>Value</th></tr>
    <tr><td>AUTHORIZATION</td><td>application-233519075379df0758e30155cba76d9a</td></tr>
    <tr><td>Accept</td><td>application/json, text/plain, */*</td></tr>
</table>

<h2>📦 请求参数（Form Data）</h2>
<table>
    <tr><th>参数名</th><th>类型</th><th>必填</th><th>描述</th></tr>
    <tr><td>file</td><td>file</td><td>是</td><td>要上传的图片</td></tr>
    <tr><td>debug</td><td>string</td><td>否</td><td>是否开启调试模式</td></tr>
</table>

<h2>📤 请求体（Body） - Form Data 格式</h2>
<pre>
{
  "file": "image.png",
  "debug": "false"
}
</pre>
<p><strong>注意：</strong><code>file</code> 字段为二进制文件流，实际上传时由客户端处理打开和读取。</p>

<h2>✅ 成功响应示例（Status Code: 200）</h2>
<pre>
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "name": "image.png",
      "url": "/api/file/a904c3fa-50a6-11f0-bbb8-00163e4f3d7b",
      "file_id": "a904c3fa-50a6-11f0-bbb8-00163e4f3d7b"
    }
  ]
}
</pre>

<h2>❌ 失败响应示例（例如 Status Code: 400）</h2>
<pre>
{
  "code": 400,
  "message": "Invalid request",
  "data": null
}
</pre>

<h2>⚠️ 非 JSON 响应（文本格式）</h2>
<pre>
Internal Server Error
</pre>

<h2>📎 备注</h2>
<ul>
    <li>替换 <code>{application_id}</code> 和 <code>{chat_id}</code> 为你自己的应用和会话 ID。</li>
    <li>确保上传文件路径正确，并有读取权限。</li>
</ul>

</body>
</html>