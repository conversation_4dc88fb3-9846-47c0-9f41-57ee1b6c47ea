const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/export-B4KjNfe_.js","assets/index-BdEKvwRr.js","assets/vendor-CPqkYfXn.js","assets/ui-CjjzzDsP.js","assets/index-DO--s7BF.css"])))=>i.map(i=>d[i]);
var H=(L,e,i)=>new Promise((o,x)=>{var E=f=>{try{p(i.next(f))}catch(k){x(k)}},s=f=>{try{p(i.throw(f))}catch(k){x(k)}},p=f=>f.done?o(f.value):Promise.resolve(f.value).then(E,s);p((i=i.apply(L,e)).next())});import{_ as te,a as ee}from"./index-BdEKvwRr.js";import{x as oe,c as M,y as u,A as t,K as V,O as c,P as S,a6 as T,aD as se,z as a,M as h,ax as le,r as C,h as ae,S as ie,al as g,Q as l,I as r,H as G,C as re}from"./vendor-CPqkYfXn.js";import{a as D,b as de,h as ue,z as ce,j as _e}from"./ui-CjjzzDsP.js";import{u as me}from"./filter-t8MhSl_r.js";import"./utils-DxgFcSvi.js";const ve=oe({__name:"ExportPreview",props:{questions:{type:Array,required:!0},settings:{type:Object,required:!0},totalScore:{type:Number,required:!0}},setup(L,{expose:e}){e();const i=L,o={single_choice:"单选题",multiple_choice:"多选题",true_false:"判断题",fill_blank:"填空题",short_answer:"简答题",essay:"解答题",calculation:"计算题",application:"应用题",analysis:"分析题",comprehensive:"综合题"},x=M(()=>{const m={};return i.questions.forEach(w=>{const U=w.tags.questionType;m[U]=(m[U]||0)+1}),m}),E=M(()=>i.questions.filter(m=>["single_choice","multiple_choice","true_false"].includes(m.tags.questionType))),s=M(()=>i.questions.filter(m=>!["single_choice","multiple_choice","true_false"].includes(m.tags.questionType))),p=M(()=>{if(i.questions.length>0){const m=[...new Set(i.questions.map(w=>w.tags.subject))];return m.length===1?m[0]:"综合"}return"数学"}),O={props:i,questionTypeLabels:o,questionTypeStats:x,choiceQuestions:E,nonChoiceQuestions:s,paperSubject:p,getQuestionNumber:m=>i.questions.findIndex(w=>w.id===m)+1,getOptionsForQuestion:m=>{const w=m.tags.questionType;return w==="true_false"?["A","B"]:w==="multiple_choice"?["A","B","C","D","E","F"]:["A","B","C","D"]},getQuestionTypeLabel:m=>o[m]||m,getTypeScore:m=>i.questions.filter(w=>w.tags.questionType===m).reduce((w,U)=>w+U.score,0),needsAnswerSpace:m=>["fill_blank","short_answer","essay","calculation","application"].includes(m),getAnswerLines:m=>({fill_blank:2,short_answer:4,essay:8,calculation:6,application:8})[m]||3};return Object.defineProperty(O,"__isScriptSetup",{enumerable:!1,value:!0}),O}}),pe={class:"export-preview"},fe={key:0,class:"paper-container"},ge={class:"paper-header"},be={class:"paper-title"},xe={class:"paper-info"},ye={class:"info-row"},we={class:"instructions"},he={class:"questions-content"},ke={class:"question-header"},Se={class:"question-number"},Te={class:"question-score"},qe=["innerHTML"],Fe={key:0,class:"question-options"},Ve={key:1,class:"answer-space"},Ce={key:2,class:"question-answer"},De={key:3,class:"question-explanation"},Ue=["innerHTML"],Ee={key:0,class:"score-breakdown"},Oe={class:"breakdown-content"},ze={key:1,class:"watermark"},Le={key:1,class:"answer-sheet-container"},Pe={class:"answer-sheet-header"},Be={class:"sheet-title"},Qe={class:"sheet-info"},He={class:"info-section"},Me={class:"info-grid"},Ne={class:"info-item"},Ae={class:"fill-boxes"},je={class:"info-item"},Ie={class:"fill-boxes"},Ze={class:"info-item"},Re={class:"fill-boxes"},Je={class:"info-item"},Ke={class:"fill-boxes"},We={class:"exam-info"},Xe={class:"exam-details"},Ge={class:"answer-areas"},Ye={key:0,class:"choice-section"},$e={class:"choice-grid"},et={class:"question-num"},tt={class:"choice-options"},ot={class:"option-label"},nt={key:1,class:"essay-section"},st={class:"essay-questions"},lt={class:"essay-header"},at={class:"question-number"},it={class:"question-score"},rt={class:"question-type"},dt={class:"essay-answer-area"},ut={class:"score-table"},ct={class:"score-grid"},_t={key:0,class:"score-overflow"},mt={key:0,class:"watermark"};function vt(L,e,i,o,x,E){return a(),u("div",pe,[i.settings.answerSheet?(a(),u("div",Le,[t("div",Pe,[t("h1",Be,c(i.settings.title)+" - 答题卡",1),t("div",Qe,[t("div",He,[t("div",Me,[t("div",Ne,[e[8]||(e[8]=t("label",null,"姓名：",-1)),t("div",Ae,[(a(),u(S,null,T(4,s=>t("div",{key:s,class:"fill-box"})),64))])]),t("div",je,[e[9]||(e[9]=t("label",null,"学号：",-1)),t("div",Ie,[(a(),u(S,null,T(8,s=>t("div",{key:s,class:"fill-box"})),64))])]),t("div",Ze,[e[10]||(e[10]=t("label",null,"班级：",-1)),t("div",Re,[(a(),u(S,null,T(6,s=>t("div",{key:s,class:"fill-box"})),64))])]),t("div",Je,[e[11]||(e[11]=t("label",null,"考场：",-1)),t("div",Ke,[(a(),u(S,null,T(3,s=>t("div",{key:s,class:"fill-box"})),64))])])])]),t("div",We,[t("div",Xe,[t("span",null,"考试科目："+c(o.paperSubject),1),t("span",null,"考试时间："+c(i.settings.duration)+"分钟",1),t("span",null,"总分："+c(i.totalScore)+"分",1)])])]),e[12]||(e[12]=se('<div class="sheet-instructions" data-v-ea2b9b4d><h4 data-v-ea2b9b4d>填涂说明：</h4><div class="instruction-content" data-v-ea2b9b4d><div class="instruction-text" data-v-ea2b9b4d><p data-v-ea2b9b4d>1. 答题前，考生先将自己的姓名、学号、班级等信息填写清楚。</p><p data-v-ea2b9b4d>2. 选择题部分请用2B铅笔填涂答题卡，如需改动，用橡皮擦擦干净后，再选涂其他答案。</p><p data-v-ea2b9b4d>3. 非选择题部分请用黑色签字笔在答题区域内作答，超出答题区域的答案无效。</p><p data-v-ea2b9b4d>4. 保持答题卡清洁，不要折叠、不要弄破。</p></div><div class="fill-example" data-v-ea2b9b4d><span data-v-ea2b9b4d>正确填涂：</span><div class="example-bubble filled" data-v-ea2b9b4d></div><span data-v-ea2b9b4d>错误填涂：</span><div class="example-bubble wrong1" data-v-ea2b9b4d></div><div class="example-bubble wrong2" data-v-ea2b9b4d></div><div class="example-bubble wrong3" data-v-ea2b9b4d></div></div></div></div>',1))]),t("div",Ge,[o.choiceQuestions.length>0?(a(),u("div",Ye,[e[14]||(e[14]=t("h3",{class:"section-title"},"选择题答题区",-1)),t("div",$e,[(a(!0),u(S,null,T(o.choiceQuestions,s=>(a(),u("div",{key:s.id,class:"choice-item"},[t("div",et,c(o.getQuestionNumber(s.id)),1),t("div",tt,[(a(!0),u(S,null,T(o.getOptionsForQuestion(s),p=>(a(),u("div",{key:p,class:"option-bubble"},[t("span",ot,c(p),1),e[13]||(e[13]=t("div",{class:"bubble"},null,-1))]))),128))])]))),128))])])):V("",!0),o.nonChoiceQuestions.length>0?(a(),u("div",nt,[e[15]||(e[15]=t("h3",{class:"section-title"},"非选择题答题区",-1)),t("div",st,[(a(!0),u(S,null,T(o.nonChoiceQuestions,s=>(a(),u("div",{key:s.id,class:"essay-item"},[t("div",lt,[t("span",at,c(o.getQuestionNumber(s.id))+".",1),t("span",it,"("+c(s.score)+"分)",1),t("span",rt,c(o.getQuestionTypeLabel(s.tags.questionType)),1)]),t("div",dt,[(a(!0),u(S,null,T(o.getAnswerLines(s.tags.questionType),p=>(a(),u("div",{key:p,class:"answer-line"}))),128))])]))),128))])])):V("",!0)]),t("div",ut,[e[21]||(e[21]=t("h4",null,"评分表",-1)),t("table",ct,[t("thead",null,[t("tr",null,[e[16]||(e[16]=t("th",null,"题号",-1)),(a(!0),u(S,null,T(i.questions.slice(0,Math.min(i.questions.length,20)),s=>(a(),u("th",{key:s.id},c(o.getQuestionNumber(s.id)),1))),128)),e[17]||(e[17]=t("th",null,"小计",-1))])]),t("tbody",null,[t("tr",null,[e[18]||(e[18]=t("td",null,"得分",-1)),(a(!0),u(S,null,T(i.questions.slice(0,Math.min(i.questions.length,20)),s=>(a(),u("td",{key:s.id,class:"score-cell"}))),128)),e[19]||(e[19]=t("td",{class:"total-score-cell"},null,-1))])])]),i.questions.length>20?(a(),u("div",_t,e[20]||(e[20]=[t("p",null,"注：超过20题的评分请在背面或另附评分表",-1)]))):V("",!0)]),i.settings.watermark.enabled?(a(),u("div",mt,c(i.settings.watermark.text),1)):V("",!0)])):(a(),u("div",fe,[t("div",ge,[t("h1",be,c(i.settings.title),1),t("div",xe,[e[0]||(e[0]=t("div",{class:"info-row"},[t("span",null,"姓名：______________"),t("span",null,"班级：______________"),t("span",null,"学号：______________")],-1)),t("div",ye,[t("span",null,"总分："+c(i.totalScore)+"分",1),t("span",null,"时间："+c(i.settings.duration)+"分钟",1),t("span",null,"题目数："+c(i.questions.length)+"题",1)])]),t("div",we,[e[4]||(e[4]=t("h4",null,"注意事项：",-1)),t("ol",null,[t("li",null,"本试卷共"+c(i.questions.length)+"题，满分"+c(i.totalScore)+"分，考试时间"+c(i.settings.duration)+"分钟。",1),e[1]||(e[1]=t("li",null,"请在答题前仔细阅读各题目要求。",-1)),e[2]||(e[2]=t("li",null,"所有答案必须写在答题纸上，写在试卷上无效。",-1)),e[3]||(e[3]=t("li",null,"考试结束后，将试卷和答题纸一并交回。",-1))])])]),t("div",he,[(a(!0),u(S,null,T(i.questions,(s,p)=>(a(),u("div",{key:s.id,class:"question-item"},[t("div",ke,[t("span",Se,c(p+1)+".",1),t("span",Te,"("+c(s.score)+"分)",1)]),t("div",{class:"question-stem",innerHTML:s.content.stem},null,8,qe),s.content.options&&s.content.options.length>0?(a(),u("div",Fe,[(a(!0),u(S,null,T(s.content.options,(f,k)=>(a(),u("div",{key:k,class:"option-item"},c(String.fromCharCode(65+k))+". "+c(f),1))),128))])):o.needsAnswerSpace(s.tags.questionType)?(a(),u("div",Ve,[(a(!0),u(S,null,T(o.getAnswerLines(s.tags.questionType),f=>(a(),u("div",{key:f,class:"answer-line"}))),128))])):V("",!0),i.settings.includes.includes("answers")?(a(),u("div",Ce,[e[5]||(e[5]=t("strong",null,"答案：",-1)),h(c(s.content.answer),1)])):V("",!0),i.settings.includes.includes("explanations")&&s.content.explanation?(a(),u("div",De,[e[6]||(e[6]=t("strong",null,"解析：",-1)),t("div",{innerHTML:s.content.explanation},null,8,Ue)])):V("",!0)]))),128))]),i.settings.includes.includes("score_breakdown")?(a(),u("div",Ee,[e[7]||(e[7]=t("h3",null,"分值分布",-1)),t("div",Oe,[(a(!0),u(S,null,T(o.questionTypeStats,(s,p)=>(a(),u("div",{key:p,class:"breakdown-item"},[t("span",null,c(o.getQuestionTypeLabel(p))+"：",1),t("span",null,c(s)+"题，共"+c(o.getTypeScore(p))+"分",1)]))),128))])])):V("",!0),i.settings.watermark.enabled?(a(),u("div",ze,c(i.settings.watermark.text),1)):V("",!0)]))])}const pt=te(ve,[["render",vt],["__scopeId","data-v-ea2b9b4d"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/components/ui/ExportPreview.vue"]]),ft=oe({__name:"index",setup(L,{expose:e}){e();const i=me(),o=le(),x=C([]),E=C("preview"),s=C(!1),p=C(!1),f=C(0),k=C(""),y=C(""),P=C(.8),b=C({format:"pdf",filename:"试卷",title:"期末考试试卷",duration:120,includes:["questions"],answerSheet:!1,watermark:{enabled:!1,text:"内部资料 请勿外传"}}),F=C([{id:"1",filename:"数学期末试卷_2024-01-15",format:"pdf",questionCount:25,exportTime:"2024-01-15T10:30:00Z"},{id:"2",filename:"语文模拟试卷_2024-01-10",format:"word",questionCount:20,exportTime:"2024-01-10T14:20:00Z"}]),O=M(()=>x.value.reduce((n,d)=>n+d.score,0)),m=M(()=>{if(x.value.length===0)return null;const n=b.value.format==="pdf"?50:100,d=b.value.format==="pdf"?2:3,v=n+x.value.length*d;return v<1024?`${v}KB`:`${(v/1024).toFixed(1)}MB`}),w=()=>{o.push("/layout")},U=()=>{const n=new Date().toISOString().slice(0,10),d=b.value.filename.replace(/\.pdf$|\.docx$/,"");b.value.filename=`${d}_${n}`},j=()=>H(this,null,function*(){if(x.value.length===0){D.warning("没有题目可以导出");return}if(!b.value.filename.trim()){D.warning("请输入文件名");return}try{s.value=!0,p.value=!0,f.value=0,k.value="";const{ExportService:n}=yield ee(()=>H(this,null,function*(){const{ExportService:W}=yield import("./export-B4KjNfe_.js").then(ne=>ne.a);return{ExportService:W}}),__vite__mapDeps([0,1,2,3,4])),d=b.value.format==="word"?"docx":"pdf",v=n.validateExportSize(x.value,d);if(!v.valid){D.warning(v.warning);return}v.warning&&D.info(v.warning),f.value=5,y.value="正在验证导出参数...";const q={questions:x.value,settings:b.value,totalScore:O.value,onProgress:W=>{f.value=W,y.value=R(W,b.value.format)}};let z,Q;b.value.format==="pdf"?(y.value="正在生成PDF文件...",z=yield n.exportToPDF(q),Q=`${b.value.filename}.pdf`):(y.value="正在生成Word文件...",z=yield n.exportToDOCX(q),Q=`${b.value.filename}.docx`),k.value="success",y.value="导出完成",A(z,Q);const $={id:Date.now().toString(),filename:Q,format:b.value.format,questionCount:x.value.length,exportTime:new Date().toISOString(),downloadUrl:URL.createObjectURL(z),fileSize:z.size};F.value.unshift($),J($),D.success(`${b.value.format.toUpperCase()} 文件导出成功`),E.value="history"}catch(n){console.error("Export failed:",n),k.value="exception",y.value="导出失败";let d="导出失败，请重试";n instanceof Error&&(n.message.includes("memory")||n.message.includes("内存")?d="内存不足，请减少题目数量后重试":n.message.includes("timeout")||n.message.includes("超时")?d="导出超时，请减少题目数量后重试":n.message.includes("format")||n.message.includes("格式")?d="文件格式错误，请检查题目内容后重试":n.message.includes("没有题目")&&(d="没有题目可以导出，请先生成题目")),D.error(d)}finally{setTimeout(()=>{s.value=!1,p.value=!1,f.value=0,k.value="",y.value=""},1e3)}}),B=n=>H(this,null,function*(){try{if(n.downloadUrl)Z(n.downloadUrl,n.filename);else{D.info("正在重新生成文件...");const{ExportService:d}=yield ee(()=>H(this,null,function*(){const{ExportService:z}=yield import("./export-B4KjNfe_.js").then(Q=>Q.a);return{ExportService:z}}),__vite__mapDeps([0,1,2,3,4])),v={questions:x.value,settings:b.value,totalScore:O.value};let q;n.format==="pdf"?q=yield d.exportToPDF(v):q=yield d.exportToDOCX(v),A(q,n.filename),D.success(`文件 ${n.filename} 下载成功`)}}catch(d){console.error("Download failed:",d),D.error("文件下载失败，请重新导出")}}),N=n=>H(this,null,function*(){try{yield de.confirm("确定要删除这条导出记录吗？","删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}),F.value=F.value.filter(d=>d.id!==n),D.success("删除成功")}catch(d){}}),I=n=>new Date(n).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),X=n=>n<1024?`${n}B`:n<1024*1024?`${(n/1024).toFixed(1)}KB`:`${(n/1024/1024).toFixed(1)}MB`,A=(n,d)=>{const v=URL.createObjectURL(n),q=document.createElement("a");q.href=v,q.download=d,document.body.appendChild(q),q.click(),document.body.removeChild(q),URL.revokeObjectURL(v)},Z=(n,d)=>{const v=document.createElement("a");v.href=n,v.download=d,document.body.appendChild(v),v.click(),document.body.removeChild(v)},R=(n,d)=>n<20?"准备导出数据...":n<40?`正在生成${d.toUpperCase()}内容...`:n<60?"应用排版设置...":n<80?"生成文件格式...":n<100?"完成导出...":"导出完成",J=n=>{try{let d=JSON.parse(localStorage.getItem("exportHistory")||"[]");d=d.filter(v=>v.filename!==n.filename),d.unshift(n),d.length>20&&(d=d.slice(0,20)),localStorage.setItem("exportHistory",JSON.stringify(d))}catch(d){console.warn("Failed to save export history:",d)}},_=()=>{try{const n=JSON.parse(localStorage.getItem("exportHistory")||"[]"),d=new Date;d.setDate(d.getDate()-30);const v=n.filter(q=>new Date(q.exportTime)>d);F.value=v,v.length!==n.length&&localStorage.setItem("exportHistory",JSON.stringify(v))}catch(n){console.warn("Failed to load export history:",n),F.value=[]}},K=()=>{F.value.forEach(n=>{n.downloadUrl&&(URL.revokeObjectURL(n.downloadUrl),n.downloadUrl=void 0)})};ae(()=>{i.questions.length>0&&(x.value=[...i.questions]);const n=new Date().toISOString().slice(0,10);b.value.filename=`试卷_${n}`,_()}),ie(()=>{K()});const Y={filterStore:i,router:o,questions:x,activeTab:E,exporting:s,exportProgressVisible:p,exportProgress:f,exportStatus:k,exportProgressText:y,previewZoom:P,exportForm:b,exportHistory:F,totalScore:O,estimatedSize:m,handleBack:w,handleFormatChange:U,handleExport:j,handleDownload:B,handleDeleteHistory:N,formatDateTime:I,formatFileSize:X,downloadFile:A,downloadFileFromUrl:Z,getExportProgressText:R,saveExportHistory:J,loadExportHistory:_,cleanupBlobUrls:K,get ZoomIn(){return _e},get ZoomOut(){return ce},get Download(){return ue},ExportPreview:pt};return Object.defineProperty(Y,"__isScriptSetup",{enumerable:!1,value:!0}),Y}}),gt={class:"export"},bt={class:"card-header"},xt={class:"export-content"},yt={class:"preview-container"},wt={class:"preview-toolbar"},ht={class:"preview-info"},kt={class:"preview-actions"},St={class:"preview-content"},Tt={class:"export-history"},qt={key:1,class:"history-list"},Ft={class:"history-info"},Vt={class:"history-title"},Ct={class:"history-meta"},Dt={key:0},Ut={class:"history-actions"},Et={class:"export-progress"},Ot={class:"progress-text"};function zt(L,e,i,o,x,E){const s=g("el-button"),p=g("el-button-group"),f=g("el-radio"),k=g("el-radio-group"),y=g("el-form-item"),P=g("el-input"),b=g("el-input-number"),F=g("el-checkbox"),O=g("el-checkbox-group"),m=g("el-switch"),w=g("el-form"),U=g("el-card"),j=g("el-col"),B=g("el-tag"),N=g("el-icon"),I=g("el-tab-pane"),X=g("el-empty"),A=g("el-tabs"),Z=g("el-row"),R=g("el-progress"),J=g("el-dialog");return a(),u("div",gt,[l(U,null,{header:r(()=>[t("div",bt,[e[15]||(e[15]=t("span",null,"试卷导出",-1)),l(p,null,{default:r(()=>[l(s,{onClick:o.handleBack},{default:r(()=>e[13]||(e[13]=[h("返回",-1)])),_:1,__:[13]}),l(s,{type:"primary",onClick:o.handleExport,loading:o.exporting},{default:r(()=>e[14]||(e[14]=[h(" 立即导出 ",-1)])),_:1,__:[14]},8,["loading"])]),_:1})])]),default:r(()=>[t("div",xt,[l(Z,{gutter:24},{default:r(()=>[l(j,{span:8},{default:r(()=>[l(U,{shadow:"never"},{header:r(()=>e[16]||(e[16]=[t("span",null,"导出设置",-1)])),default:r(()=>[l(w,{model:o.exportForm,"label-width":"100px"},{default:r(()=>[l(y,{label:"导出格式",required:""},{default:r(()=>[l(k,{modelValue:o.exportForm.format,"onUpdate:modelValue":e[0]||(e[0]=_=>o.exportForm.format=_),onChange:o.handleFormatChange},{default:r(()=>[l(f,{value:"pdf"},{default:r(()=>e[17]||(e[17]=[h("PDF 格式",-1)])),_:1,__:[17]}),l(f,{value:"word"},{default:r(()=>e[18]||(e[18]=[h("Word 格式",-1)])),_:1,__:[18]})]),_:1},8,["modelValue"])]),_:1}),l(y,{label:"文件名称",required:""},{default:r(()=>[l(P,{modelValue:o.exportForm.filename,"onUpdate:modelValue":e[1]||(e[1]=_=>o.exportForm.filename=_),placeholder:"请输入文件名"},null,8,["modelValue"])]),_:1}),l(y,{label:"试卷标题"},{default:r(()=>[l(P,{modelValue:o.exportForm.title,"onUpdate:modelValue":e[2]||(e[2]=_=>o.exportForm.title=_),placeholder:"请输入试卷标题"},null,8,["modelValue"])]),_:1}),l(y,{label:"考试时间"},{default:r(()=>[l(b,{modelValue:o.exportForm.duration,"onUpdate:modelValue":e[3]||(e[3]=_=>o.exportForm.duration=_),min:30,max:300,step:15,style:{width:"100%"}},null,8,["modelValue"]),e[19]||(e[19]=t("div",{class:"form-tip"},"单位：分钟",-1))]),_:1,__:[19]}),l(y,{label:"包含内容"},{default:r(()=>[l(O,{modelValue:o.exportForm.includes,"onUpdate:modelValue":e[4]||(e[4]=_=>o.exportForm.includes=_)},{default:r(()=>[l(F,{value:"questions",disabled:"",checked:""},{default:r(()=>e[20]||(e[20]=[h("题目内容",-1)])),_:1,__:[20]}),l(F,{value:"answers"},{default:r(()=>e[21]||(e[21]=[h("参考答案",-1)])),_:1,__:[21]}),l(F,{value:"explanations"},{default:r(()=>e[22]||(e[22]=[h("答案解析",-1)])),_:1,__:[22]}),l(F,{value:"score_breakdown"},{default:r(()=>e[23]||(e[23]=[h("分值分布",-1)])),_:1,__:[23]})]),_:1},8,["modelValue"])]),_:1}),l(y,{label:"答题卡模式"},{default:r(()=>[l(m,{modelValue:o.exportForm.answerSheet,"onUpdate:modelValue":e[5]||(e[5]=_=>o.exportForm.answerSheet=_),"active-text":"生成答题卡","inactive-text":"答案在试卷上"},null,8,["modelValue"])]),_:1}),l(y,{label:"水印设置"},{default:r(()=>[l(m,{modelValue:o.exportForm.watermark.enabled,"onUpdate:modelValue":e[6]||(e[6]=_=>o.exportForm.watermark.enabled=_),"active-text":"添加水印","inactive-text":"无水印"},null,8,["modelValue"]),o.exportForm.watermark.enabled?(a(),G(P,{key:0,modelValue:o.exportForm.watermark.text,"onUpdate:modelValue":e[7]||(e[7]=_=>o.exportForm.watermark.text=_),placeholder:"水印文字",style:{"margin-top":"8px"}},null,8,["modelValue"])):V("",!0)]),_:1})]),_:1},8,["model"])]),_:1})]),_:1}),l(j,{span:16},{default:r(()=>[l(A,{modelValue:o.activeTab,"onUpdate:modelValue":e[11]||(e[11]=_=>o.activeTab=_)},{default:r(()=>[l(I,{label:"导出预览",name:"preview"},{default:r(()=>[t("div",yt,[t("div",wt,[t("div",ht,[l(B,{type:"info"},{default:r(()=>[h(c(o.questions.length)+" 道题目",1)]),_:1}),l(B,{type:"success"},{default:r(()=>[h("总分 "+c(o.totalScore)+" 分",1)]),_:1}),l(B,null,{default:r(()=>[h(c(o.exportForm.format.toUpperCase())+" 格式",1)]),_:1}),o.estimatedSize?(a(),G(B,{key:0,type:"warning"},{default:r(()=>[h(c(o.estimatedSize),1)]),_:1})):V("",!0)]),t("div",kt,[l(p,{size:"small"},{default:r(()=>[l(s,{onClick:e[8]||(e[8]=_=>o.previewZoom=Math.max(.5,o.previewZoom-.1))},{default:r(()=>[l(N,null,{default:r(()=>[l(o.ZoomOut)]),_:1})]),_:1}),l(s,{onClick:e[9]||(e[9]=_=>o.previewZoom=1)},{default:r(()=>[h(c((o.previewZoom*100).toFixed(0))+"% ",1)]),_:1}),l(s,{onClick:e[10]||(e[10]=_=>o.previewZoom=Math.min(2,o.previewZoom+.1))},{default:r(()=>[l(N,null,{default:r(()=>[l(o.ZoomIn)]),_:1})]),_:1})]),_:1})])]),t("div",St,[t("div",{class:"paper-preview",style:re({transform:`scale(${o.previewZoom})`})},[l(o.ExportPreview,{questions:o.questions,settings:o.exportForm,"total-score":o.totalScore},null,8,["questions","settings","total-score"])],4)])])]),_:1}),l(I,{label:"导出历史",name:"history"},{default:r(()=>[t("div",Tt,[o.exportHistory.length===0?(a(),G(X,{key:0,description:"暂无导出记录"})):(a(),u("div",qt,[(a(!0),u(S,null,T(o.exportHistory,_=>(a(),u("div",{key:_.id,class:"history-item"},[t("div",Ft,[t("div",Vt,c(_.filename),1),t("div",Ct,[t("span",null,c(_.format.toUpperCase()),1),t("span",null,c(_.questionCount)+" 题",1),_.fileSize?(a(),u("span",Dt,c(o.formatFileSize(_.fileSize)),1)):V("",!0),t("span",null,c(o.formatDateTime(_.exportTime)),1)])]),t("div",Ut,[l(s,{size:"small",onClick:K=>o.handleDownload(_)},{default:r(()=>[l(N,null,{default:r(()=>[l(o.Download)]),_:1}),e[24]||(e[24]=h(" 下载 ",-1))]),_:2,__:[24]},1032,["onClick"]),l(s,{size:"small",type:"danger",text:"",onClick:K=>o.handleDeleteHistory(_.id)},{default:r(()=>[...e[25]||(e[25]=[h(" 删除 ",-1)])]),_:2,__:[25]},1032,["onClick"])])]))),128))]))])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})])]),_:1}),l(J,{modelValue:o.exportProgressVisible,"onUpdate:modelValue":e[12]||(e[12]=_=>o.exportProgressVisible=_),title:"正在导出...",width:"400px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!1},{default:r(()=>[t("div",Et,[l(R,{percentage:o.exportProgress,status:o.exportStatus,"stroke-width":8},null,8,["percentage","status"]),t("div",Ot,c(o.exportProgressText),1)])]),_:1},8,["modelValue"])])}const Nt=te(ft,[["render",zt],["__scopeId","data-v-8f8ca010"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/views/Export/index.vue"]]);export{Nt as default};
