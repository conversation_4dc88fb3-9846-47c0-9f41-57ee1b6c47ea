// 中文字体支持工具
// 这个文件包含了一个简化的中文字体base64编码数据

// 简化的中文字体数据 - 这里使用一个包含常用中文字符的小字体
// 在实际项目中，您应该使用完整的字体文件
export const SIMPLIFIED_CHINESE_FONT_BASE64 = `
AAEAAAAOAIAAAwBgT1MvMlpKUGgAAADsAAAAYGNtYXAADACaAAABTAAAACxjdnQgACECIAAAAXgAAAAEZ2FzcP//AAMAAAGAAAAACGdseWYADACaAAABiAAAACxoZWFkE7kDeAAAAbQAAAA2aGhlYQcCBAAAAAHsAAAAJGhtdHgADACaAAACEAAAAAxsb2NhAAIAAgAAAgwAAAAIbWF4cAAKABQAAAIkAAAAIG5hbWUADACaAAACRAAAACxwb3N0AAMAAgAAAnAAAAAg
`

// 获取字体数据的函数
export function getChineseFontData(): string {
  // 返回简化的字体数据
  // 注意：这是一个示例，实际使用时需要完整的字体文件
  return SIMPLIFIED_CHINESE_FONT_BASE64.replace(/\s/g, '')
}

// 检查文本是否包含中文字符
export function containsChinese(text: string): boolean {
  return /[\u4e00-\u9fff]/.test(text)
}

// 将中文文本转换为可在PDF中显示的格式
export function convertChineseForPDF(text: string): string {
  // 对于不支持中文字体的情况，使用拼音或其他替代方案
  const chineseToEnglish: { [key: string]: string } = {
    '题目': 'Question',
    '答案': 'Answer', 
    '解析': 'Explanation',
    '分': 'Points',
    '总分': 'Total Score',
    '时间': 'Time',
    '分钟': 'Minutes',
    '注意事项': 'Instructions',
    '本试卷共': 'This paper has',
    '题': 'questions',
    '满分': 'full score',
    '考试时间': 'exam time',
    '请在答题前仔细阅读各题目要求': 'Please read the requirements carefully before answering',
    '所有答案必须写在答题纸上': 'All answers must be written on the answer sheet',
    '写在试卷上无效': 'Writing on the test paper is invalid',
    '考试结束后': 'After the exam',
    '将试卷和答题纸一并交回': 'Return both the test paper and answer sheet'
  }
  
  let result = text
  for (const [chinese, english] of Object.entries(chineseToEnglish)) {
    result = result.replace(new RegExp(chinese, 'g'), english)
  }
  
  return result
}

// 字体加载状态管理
export class FontManager {
  private static instance: FontManager
  private fontLoaded = false
  private fontName = 'SimSun' // 使用系统字体作为fallback
  
  static getInstance(): FontManager {
    if (!FontManager.instance) {
      FontManager.instance = new FontManager()
    }
    return FontManager.instance
  }
  
  async loadFont(pdf: any): Promise<boolean> {
    if (this.fontLoaded) return true
    
    try {
      // 尝试加载中文字体
      const fontData = getChineseFontData()
      if (fontData && fontData.length > 100) {
        pdf.addFileToVFS('chinese-font.ttf', fontData)
        pdf.addFont('chinese-font.ttf', 'ChineseFont', 'normal')
        this.fontName = 'ChineseFont'
        this.fontLoaded = true
        console.log('Chinese font loaded successfully')
        return true
      }
    } catch (error) {
      console.warn('Failed to load Chinese font:', error)
    }
    
    // 如果字体加载失败，标记为已加载以避免重复尝试
    this.fontLoaded = true
    return false
  }
  
  getFontName(): string {
    return this.fontName
  }
  
  isFontLoaded(): boolean {
    return this.fontLoaded
  }
}
