// Core type definitions for 智卷云 frontend

// Question related types
export interface Question {
  id: string
  content: {
    stem: string
    options?: string[]
    answer: string
    explanation: string
    attachments?: string[]
  }
  tags: {
    grade: string
    subject: string
    questionType: string
    difficulty: string
    knowledgePoint: string[]
    scenario: string
    sourceType: string
  }
  score: number
  order: number
}

// Filter parameters
export interface FilterParams {
  grade: string
  subject: string
  knowledgePoints: string[]
  questionTypes: string[]
  difficulty: string[]
  region: string
  questionCount: number
  sampleFile?: SampleInfo
}

// Layout parameters
export interface LayoutParams {
  paperSize: 'A4' | 'A3'
  orientation: 'portrait' | 'landscape'
  fontSize: number
  titleFontSize: number
  lineHeight: number
  margin: {
    top: number
    right: number
    bottom: number
    left: number
  }
  showAnswer: boolean
  showExplanation: boolean
  questionGrouping: 'byType' | 'mixed'
}

// API response types
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// Error types
export const ErrorType = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  FILE_UPLOAD_ERROR: 'FILE_UPLOAD_ERROR',
  EXPORT_ERROR: 'EXPORT_ERROR'
} as const

export type ErrorType = typeof ErrorType[keyof typeof ErrorType]

export interface AppError {
  type: ErrorType
  message: string
  details?: any
}

// Sample info
export interface SampleInfo {
  id: string
  filename: string
  size: number
  uploadTime: string
  status: 'processing' | 'completed' | 'failed'
}

// Generate parameters for AI
export interface GenerateParams {
  sampleId?: string
  filterParams: FilterParams
  count: number
}

// Export settings
export interface ExportSettings {
  format: 'pdf' | 'word'
  filename: string
  title: string
  duration: number
  includes: string[]
  answerSheet: boolean
  watermark: {
    enabled: boolean
    text: string
  }
}