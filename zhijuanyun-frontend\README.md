# 智卷云前端项目

一个基于 Vue 3 + TypeScript + Element Plus 的智能组卷系统前端。

## 项目特性

- 🔍 **智能题目筛选**: 支持按年级、学科、知识点、题型、难度等多维度筛选
- 🤖 **AI出题功能**: 支持样题上传和AI智能生成题目
- ✏️ **题目编辑器**: 提供富文本编辑器，支持题目内容、选项、答案、解析的编辑
- 📋 **拖拽排序**: 支持题目拖拽排序和分值设置
- 📄 **试卷排版**: 可视化试卷排版，支持多种纸张格式和排版参数
- 📤 **多格式导出**: 支持PDF和Word格式导出，可自定义导出内容
- 📱 **响应式设计**: 适配多种设备屏幕尺寸

## 技术栈

- **框架**: Vue 3 + Composition API
- **语言**: TypeScript
- **UI组件库**: Element Plus
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **富文本编辑**: Quill.js
- **拖拽排序**: VueDraggable
- **文件导出**: jsPDF + docx.js
- **构建工具**: Vite
- **代码规范**: ESLint + Prettier

## 项目结构

```
src/
├── components/          # 组件
│   ├── forms/          # 表单组件
│   ├── ui/             # UI组件
│   └── common/         # 通用组件
├── views/              # 页面
│   ├── QuestionFilter/ # 题目筛选页
│   ├── QuestionEdit/   # 题目编辑页
│   ├── PaperLayout/    # 试卷排版页
│   └── Export/         # 导出页面
├── stores/             # 状态管理
├── services/           # API服务
├── types/              # 类型定义
├── utils/              # 工具函数
├── styles/             # 样式文件
└── router/             # 路由配置
```

## 开发指南

### 环境要求

- Node.js >= 16
- npm >= 8

### 安装依赖

```bash
npm install
```

### 开发运行

```bash
npm run dev
```

### 构建部署

```bash
npm run build
```

### 代码检查

```bash
npm run lint
```

### 运行测试

```bash
npm run test
```

## 页面导航

1. **题目筛选** (`/filter`) - 设置筛选条件，搜索题目
2. **题目编辑** (`/edit`) - 编辑题目内容，调整排序和分值
3. **试卷排版** (`/layout`) - 设置排版参数，预览试卷
4. **导出试卷** (`/export`) - 选择导出格式，完成文件导出

## 功能模块

### 1. 题目筛选模块
- 年级、学科级联选择
- 知识点树形多选
- 题型和难度多选
- 地区选择和题目数量设置
- 样题上传功能
- AI出题集成

### 2. 题目编辑模块
- 题目列表展示和筛选
- 拖拽排序功能
- 分值批量设置
- 富文本题目编辑器
- 题目预览和删除

### 3. 试卷排版模块
- 纸张大小和方向设置
- 字体、字号、行距调整
- 页边距自定义设置
- 题型分组或混合排列
- 答案和解析显示控制
- 实时预览和缩放

### 4. 文件导出模块
- PDF/Word格式选择
- 文件名和标题自定义
- 导出内容选择
- 答题卡模式
- 水印设置
- 导出历史管理

## API接口

项目目前使用模拟数据，实际部署时需要配置后端API接口：

- `POST /questions/search` - 题目搜索
- `POST /questions/generate` - AI生成题目
- `POST /samples/upload` - 样题上传
- `PUT /questions/:id` - 更新题目
- `DELETE /questions/:id` - 删除题目

## 部署配置

### 环境变量

```bash
VITE_API_BASE_URL=http://localhost:3000/api  # API基础URL
```

### 生产环境构建

```bash
npm run build
```

构建后的文件位于 `dist/` 目录，可以部署到任何静态文件服务器。

## 浏览器支持

- Chrome >= 88
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 开发注意事项

1. 使用TypeScript进行类型检查
2. 遵循Vue 3 Composition API规范
3. 使用Element Plus组件库保持UI一致性
4. 响应式设计适配移动端
5. 代码注释清晰，便于维护

## 许可证

MIT License
