// Export functionality for PDF and DOCX formats

import jsPDF from 'jspdf'
import { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, WidthType, AlignmentType } from 'docx'
import type { Question } from '@/types'
import type { ExportSettings } from '@/types'
import { ChineseFontLoader, ChineseTextProcessor } from '@/utils/chinese-font-data'
import { PDFChineseTextRenderer, ChineseCanvasRenderer } from '@/utils/chinese-canvas'
import { RealChineseFontSupport, chineseExportUtils } from '@/utils/real-chinese-font'
import { simpleExportUtils } from '@/utils/html-to-pdf'

// Enhanced Chinese font support for jsPDF using real Chinese fonts
class ChineseFontSupport {
  private static fontLoaded = false

  static async loadFont(pdf: jsPDF): Promise<void> {
    if (this.fontLoaded) return

    try {
      // 使用真实的中文字体支持
      const success = await RealChineseFontSupport.loadChineseFont(pdf)
      this.fontLoaded = true

      if (success) {
        console.log('Real Chinese font loaded successfully')
      } else {
        console.log('Chinese font loading failed, using fallback')
      }
    } catch (error) {
      console.warn('Error loading Chinese font:', error)
      this.fontLoaded = true
    }
  }

  // 添加文本的主要方法
  static addText(pdf: jsPDF, text: string, x: number, y: number, options: any = {}): void {
    this.addTextSync(pdf, text, x, y, options)
  }

  // 同步版本的addText，用于不支持async的地方
  static addTextSync(pdf: jsPDF, text: string, x: number, y: number, options: any = {}): void {
    try {
      // 直接使用真实的中文字体支持
      RealChineseFontSupport.renderChineseText(pdf, text, x, y, options)
    } catch (error) {
      console.warn('Chinese text rendering failed, using fallback:', error)

      // Fallback 1: 尝试英文转换
      if (ChineseTextProcessor.containsChinese(text)) {
        const englishText = ChineseTextProcessor.toEnglish(text)
        if (englishText !== text) {
          pdf.setFont('helvetica')
          pdf.text(englishText, x, y, options)
          return
        }
      }

      // Fallback 2: 直接显示原文本（在支持中文的环境中可能正常显示）
      pdf.setFont('helvetica')
      pdf.text(text, x, y, options)
    }
  }
}

export interface ExportOptions {
  questions: Question[]
  settings: ExportSettings
  totalScore: number
  onProgress?: (progress: number) => void
}

export class ExportService {
  // Export to PDF format with enhanced progress tracking
  static async exportToPDF(options: ExportOptions): Promise<Blob> {
    const { questions, settings, totalScore, onProgress } = options

    if (onProgress) onProgress(5)

    // Validate input before processing
    if (!questions || questions.length === 0) {
      throw new Error('没有题目可以导出')
    }

    if (onProgress) onProgress(10)

    // 优先使用HTML转PDF方案（最可靠的中文支持）
    try {
      if (onProgress) onProgress(20)

      if (settings.answerSheet) {
        console.log('Using HTML to PDF conversion for answer sheet with Chinese support')
      } else {
        console.log('Using HTML to PDF conversion for exam paper with Chinese support')
      }

      const blob = await simpleExportUtils.exportExamToPDF(questions, settings, totalScore)

      if (onProgress) onProgress(100)
      return blob
    } catch (htmlError) {
      console.warn('HTML to PDF conversion failed, falling back to jsPDF:', htmlError)
      if (onProgress) onProgress(30)
    }
    
    // Create PDF document
    const pdf = new jsPDF('p', 'mm', 'a4')

    // Load Chinese font support
    await ChineseFontSupport.loadFont(pdf)
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()
    const margin = 20
    let yPosition = margin
    
    if (onProgress) onProgress(15)
    
    // Add title
    pdf.setFontSize(20)
    pdf.setFont('helvetica', 'bold')
    // 使用同步版本避免复杂的异步处理
    ChineseFontSupport.addTextSync(pdf, settings.title, (pageWidth - 100) / 2, yPosition)
    yPosition += 15

    if (onProgress) onProgress(20)

    // Add paper info
    pdf.setFontSize(12)
    pdf.setFont('helvetica', 'normal')
    ChineseFontSupport.addTextSync(pdf, `总分：${totalScore}分`, margin, yPosition)
    ChineseFontSupport.addTextSync(pdf, `时间：${settings.duration}分钟`, pageWidth - 60, yPosition)
    yPosition += 10

    ChineseFontSupport.addTextSync(pdf, `题目数：${questions.length}题`, margin, yPosition)
    yPosition += 20

    if (onProgress) onProgress(25)

    // Add instructions
    pdf.setFontSize(10)
    const instructions = [
      '注意事项：',
      '1. 本试卷共' + questions.length + '题，满分' + totalScore + '分，考试时间' + settings.duration + '分钟。',
      '2. 请在答题前仔细阅读各题目要求。',
      '3. 所有答案必须写在答题纸上，写在试卷上无效。',
      '4. 考试结束后，将试卷和答题纸一并交回。'
    ]

    instructions.forEach((instruction, index) => {
      if (yPosition > pageHeight - 30) {
        pdf.addPage()
        yPosition = margin
      }
      ChineseFontSupport.addTextSync(pdf, instruction, margin, yPosition)
      yPosition += 6

      // Update progress during instruction rendering
      if (onProgress && index === instructions.length - 1) {
        onProgress(30)
      }
    })
    
    yPosition += 10
    
    if (onProgress) onProgress(35)
    
    // Add questions with detailed progress tracking
    const totalQuestions = questions.length
    questions.forEach((question, index) => {
      if (yPosition > pageHeight - 60) {
        pdf.addPage()
        yPosition = margin
      }
      
      // Question number and score
      pdf.setFontSize(12)
      pdf.setFont('helvetica', 'bold')
      ChineseFontSupport.addTextSync(pdf, `${index + 1}. (${question.score}分)`, margin, yPosition)
      yPosition += 8

      // Question stem
      pdf.setFont('helvetica', 'normal')
      const stemLines = this.splitText(pdf, question.content.stem, pageWidth - 2 * margin)
      stemLines.forEach(line => {
        if (yPosition > pageHeight - 20) {
          pdf.addPage()
          yPosition = margin
        }
        ChineseFontSupport.addTextSync(pdf, line, margin, yPosition)
        yPosition += 6
      })

      // Options for choice questions
      if (question.content.options && question.content.options.length > 0) {
        question.content.options.forEach((option, optIndex) => {
          if (yPosition > pageHeight - 20) {
            pdf.addPage()
            yPosition = margin
          }
          const optionText = `${String.fromCharCode(65 + optIndex)}. ${option}`
          const optionLines = this.splitText(pdf, optionText, pageWidth - 2 * margin - 10)
          optionLines.forEach(line => {
            ChineseFontSupport.addTextSync(pdf, line, margin + 10, yPosition)
            yPosition += 6
          })
        })
      }
      
      // Answer space for non-choice questions
      if (this.needsAnswerSpace(question.tags.questionType)) {
        const lines = this.getAnswerLines(question.tags.questionType)
        for (let i = 0; i < lines; i++) {
          if (yPosition > pageHeight - 20) {
            pdf.addPage()
            yPosition = margin
          }
          pdf.line(margin, yPosition, pageWidth - margin, yPosition)
          yPosition += 8
        }
      }
      
      // Answer (if included)
      if (settings.includes.includes('answers')) {
        if (yPosition > pageHeight - 20) {
          pdf.addPage()
          yPosition = margin
        }
        pdf.setFont('helvetica', 'bold')
        ChineseFontSupport.addTextSync(pdf, '答案：', margin, yPosition)
        yPosition += 6
        pdf.setFont('helvetica', 'normal')
        const answerLines = this.splitText(pdf, question.content.answer, pageWidth - 2 * margin - 10)
        answerLines.forEach(line => {
          if (yPosition > pageHeight - 20) {
            pdf.addPage()
            yPosition = margin
          }
          ChineseFontSupport.addTextSync(pdf, line, margin + 10, yPosition)
          yPosition += 6
        })
      }

      // Explanation (if included)
      if (settings.includes.includes('explanations') && question.content.explanation) {
        if (yPosition > pageHeight - 20) {
          pdf.addPage()
          yPosition = margin
        }
        pdf.setFont('helvetica', 'bold')
        ChineseFontSupport.addTextSync(pdf, '解析：', margin, yPosition)
        yPosition += 6
        pdf.setFont('helvetica', 'normal')
        const explanationLines = this.splitText(pdf, question.content.explanation, pageWidth - 2 * margin - 10)
        explanationLines.forEach(line => {
          if (yPosition > pageHeight - 20) {
            pdf.addPage()
            yPosition = margin
          }
          ChineseFontSupport.addTextSync(pdf, line, margin + 10, yPosition)
          yPosition += 6
        })
      }
      
      yPosition += 10
      
      // Update progress based on question completion
      const questionProgress = 35 + (index + 1) / totalQuestions * 50
      if (onProgress) {
        onProgress(Math.min(questionProgress, 85))
      }
    })
    
    if (onProgress) onProgress(90)
    
    // Add watermark if enabled
    if (settings.watermark.enabled) {
      this.addWatermark(pdf, settings.watermark.text)
    }
    
    if (onProgress) onProgress(95)
    
    // Finalize PDF
    const blob = pdf.output('blob')
    
    if (onProgress) onProgress(100)
    
    return blob
  }
  
  // Export to DOCX format with enhanced progress tracking
  static async exportToDOCX(options: ExportOptions): Promise<Blob> {
    const { questions, settings, totalScore, onProgress } = options
    
    if (onProgress) onProgress(5)
    
    // Validate input before processing
    if (!questions || questions.length === 0) {
      throw new Error('没有题目可以导出')
    }
    
    if (onProgress) onProgress(10)
    
    const children: any[] = []
    
    // Title
    children.push(
      new Paragraph({
        text: settings.title,
        heading: HeadingLevel.TITLE,
        alignment: AlignmentType.CENTER,
        spacing: { after: 400 }
      })
    )
    
    if (onProgress) onProgress(15)
    
    // Paper info
    children.push(
      new Paragraph({
        children: [
          new TextRun({ text: `总分：${totalScore}分`, bold: true }),
          new TextRun({ text: `     时间：${settings.duration}分钟`, bold: true }),
          new TextRun({ text: `     题目数：${questions.length}题`, bold: true })
        ],
        spacing: { after: 200 }
      })
    )
    
    if (onProgress) onProgress(20)
    
    // Instructions
    children.push(
      new Paragraph({
        text: '注意事项：',
        heading: HeadingLevel.HEADING_3,
        spacing: { before: 200, after: 100 }
      }),
      new Paragraph({
        children: [
          new TextRun({ text: `1. 本试卷共${questions.length}题，满分${totalScore}分，考试时间${settings.duration}分钟。` })
        ],
        spacing: { after: 50 }
      }),
      new Paragraph({
        children: [
          new TextRun({ text: '2. 请在答题前仔细阅读各题目要求。' })
        ],
        spacing: { after: 50 }
      }),
      new Paragraph({
        children: [
          new TextRun({ text: '3. 所有答案必须写在答题纸上，写在试卷上无效。' })
        ],
        spacing: { after: 50 }
      }),
      new Paragraph({
        children: [
          new TextRun({ text: '4. 考试结束后，将试卷和答题纸一并交回。' })
        ],
        spacing: { after: 200 }
      })
    )
    
    if (onProgress) onProgress(30)
    
    // Questions with detailed progress tracking
    const totalQuestions = questions.length
    questions.forEach((question, index) => {
      children.push(
        new Paragraph({
          children: [
            new TextRun({ text: `${index + 1}.`, bold: true }),
            new TextRun({ text: ` (${question.score}分)`, bold: true })
          ],
          spacing: { before: 200, after: 100 }
        })
      )
      
      // Question stem
      children.push(
        new Paragraph({
          children: [new TextRun({ text: question.content.stem })],
          spacing: { after: 100 }
        })
      )
      
      // Options for choice questions
      if (question.content.options && question.content.options.length > 0) {
        question.content.options.forEach((option, optIndex) => {
          children.push(
            new Paragraph({
              children: [
                new TextRun({ text: `${String.fromCharCode(65 + optIndex)}. ${option}` })
              ],
              spacing: { after: 50 }
            })
          )
        })
      }
      
      // Answer space for non-choice questions
      if (this.needsAnswerSpace(question.tags.questionType)) {
        const lines = this.getAnswerLines(question.tags.questionType)
        for (let i = 0; i < lines; i++) {
          children.push(
            new Paragraph({
              children: [new TextRun({ text: '_________________________' })],
              spacing: { after: 50 }
            })
          )
        }
      }
      
      // Answer (if included)
      if (settings.includes.includes('answers')) {
        children.push(
          new Paragraph({
            children: [new TextRun({ text: '答案：', bold: true })],
            spacing: { before: 100, after: 50 }
          }),
          new Paragraph({
            children: [new TextRun({ text: question.content.answer })],
            spacing: { after: 100 }
          })
        )
      }
      
      // Explanation (if included)
      if (settings.includes.includes('explanations') && question.content.explanation) {
        children.push(
          new Paragraph({
            children: [new TextRun({ text: '解析：', bold: true })],
            spacing: { before: 100, after: 50 }
          }),
          new Paragraph({
            children: [new TextRun({ text: question.content.explanation })],
            spacing: { after: 100 }
          })
        )
      }
      
      // Update progress based on question completion
      const questionProgress = 30 + (index + 1) / totalQuestions * 40
      if (onProgress) {
        onProgress(Math.min(questionProgress, 70))
      }
    })
    
    // Score breakdown (if included)
    if (settings.includes.includes('score_breakdown')) {
      if (onProgress) onProgress(75)
      
      children.push(
        new Paragraph({
          text: '分值分布',
          heading: HeadingLevel.HEADING_2,
          spacing: { before: 400, after: 200 }
        })
      )
      
      const questionTypeStats: Record<string, number> = {}
      questions.forEach(question => {
        const type = question.tags.questionType
        questionTypeStats[type] = (questionTypeStats[type] || 0) + 1
      })
      
      const tableRows = Object.entries(questionTypeStats).map(([type, count]) => {
        const typeScore = questions
          .filter(q => q.tags.questionType === type)
          .reduce((total, q) => total + q.score, 0)
        
        return new TableRow({
          children: [
            new TableCell({
              children: [new Paragraph(this.getQuestionTypeLabel(type))],
              width: { size: 50, type: WidthType.PERCENTAGE }
            }),
            new TableCell({
              children: [new Paragraph(`${count}题，共${typeScore}分`)],
              width: { size: 50, type: WidthType.PERCENTAGE }
            })
          ]
        })
      })
      
      children.push(
        new Table({
          rows: tableRows,
          width: { size: 100, type: WidthType.PERCENTAGE }
        })
      )
      
      if (onProgress) onProgress(85)
    }
    
    if (onProgress) onProgress(90)
    
    // Create document
    const doc = new Document({
      sections: [{
        properties: {},
        children: children
      }]
    })
    
    if (onProgress) onProgress(95)
    
    const blob = await Packer.toBlob(doc)
    
    if (onProgress) onProgress(100)
    
    return blob
  }
  
  // Helper methods
  private static splitText(pdf: jsPDF, text: string, maxWidth: number): string[] {
    const words = text.split(' ')
    const lines: string[] = []
    let currentLine = ''
    
    words.forEach(word => {
      const testLine = currentLine + (currentLine ? ' ' : '') + word
      const testWidth = pdf.getTextWidth(testLine)
      
      if (testWidth > maxWidth && currentLine) {
        lines.push(currentLine)
        currentLine = word
      } else {
        currentLine = testLine
      }
    })
    
    if (currentLine) {
      lines.push(currentLine)
    }
    
    return lines
  }
  
  private static needsAnswerSpace(questionType: string): boolean {
    return ['fill_blank', 'short_answer', 'essay', 'calculation', 'application'].includes(questionType)
  }
  
  private static getAnswerLines(questionType: string): number {
    const lineMap: Record<string, number> = {
      'fill_blank': 2,
      'short_answer': 4,
      'essay': 8,
      'calculation': 6,
      'application': 8
    }
    return lineMap[questionType] || 3
  }
  
  private static getQuestionTypeLabel(type: string): string {
    const labels: Record<string, string> = {
      'single_choice': '单选题',
      'multiple_choice': '多选题',
      'true_false': '判断题',
      'fill_blank': '填空题',
      'short_answer': '简答题',
      'essay': '解答题',
      'calculation': '计算题',
      'application': '应用题',
      'analysis': '分析题',
      'comprehensive': '综合题'
    }
    return labels[type] || type
  }
  
  private static addWatermark(pdf: jsPDF, text: string): void {
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()

    pdf.setGState({ opacity: 0.1 })
    pdf.setFontSize(48)
    pdf.setFont('helvetica', 'bold')

    // Add watermark at an angle using transformation matrix
    ChineseFontSupport.addTextSync(pdf, text, pageWidth / 2, pageHeight / 2, {
      align: 'center',
      angle: -45,
      renderingMode: 'fill'
    })

    pdf.setGState({ opacity: 1 })
  }
  
  // Utility method to estimate file size and optimize for large exports
  static estimateExportSize(questions: Question[], format: 'pdf' | 'docx'): number {
    const baseSize = format === 'pdf' ? 50000 : 100000 // Base size in bytes
    const questionSize = format === 'pdf' ? 2000 : 3000 // Average size per question
    
    return baseSize + (questions.length * questionSize)
  }
  
  // Method to check if export might be too large and warn user
  static validateExportSize(questions: Question[], format: 'pdf' | 'docx'): { valid: boolean; warning?: string } {
    const estimatedSize = this.estimateExportSize(questions, format)
    const maxSize = format === 'pdf' ? 10485760 : 20971520 // 10MB for PDF, 20MB for DOCX
    
    if (estimatedSize > maxSize) {
      return {
        valid: false,
        warning: `导出文件预计过大（${Math.round(estimatedSize / 1024 / 1024)}MB），建议减少题目数量或分批导出`
      }
    }
    
    if (estimatedSize > maxSize * 0.7) {
      return {
        valid: true,
        warning: `导出文件较大（${Math.round(estimatedSize / 1024 / 1024)}MB），可能需要较长时间处理`
      }
    }
    
    return { valid: true }
  }
  
  // Method to optimize memory usage during export
  static async optimizeExportMemory(questions: Question[], chunkSize: number = 50): Promise<Question[][]> {
    const chunks: Question[][] = []
    
    for (let i = 0; i < questions.length; i += chunkSize) {
      chunks.push(questions.slice(i, i + chunkSize))
      
      // Allow event loop to process other events
      if (i % 100 === 0) {
        await new Promise(resolve => setTimeout(resolve, 0))
      }
    }
    
    return chunks
  }
}