# -*- coding: utf-8 -*-  
"""  
Created on Wed Feb 26 16:25:44 2025  
@author: 18268  
"""  
  
import requests  
  
# 定义headers  
headers = {  
    'accept': 'application/json',  
    'AUTHORIZATION': 'application-fbfca452daccc7a3367a91ea4af22d29'  # api key，需替换为实际的API key  
}  
  
# 获取 profile id  
def get_profile_id():  
    profile_url = 'http://ai.procaas.com:3000/api/application/3b756c2e-6ea4-11f0-bbb8-00163e4f3d7b'  # 需替换为自己的baseurl  
    response = requests.get(profile_url, headers=headers)  
    if response.status_code == 200:  
        return response.json()['data']['id']  
    else:  
        print("获取profile id失败")  
        return None  
  
# 获取 chat id  
def get_chat_id(profile_id):  
    chat_open_url = f'http://ai.procaas.com:3000/api/application/{profile_id}/chat/open'  # 需改为自己的ip   
    response = requests.get(chat_open_url, headers=headers)  
    if response.status_code == 200:  
        return response.json()['data']  
    else:  
        print("获取chat id失败")  
        return None  
  
# 发送聊天消息（流式）  
def send_chat_message(chat_id, payload):  
    chat_message_url = f'http://ai.procaas.com:3000/api/application/chat_message/{chat_id}'# 需改为自己的ip   
    try:  
        # 开启流式传输  
        response = requests.post(chat_message_url, headers=headers, json=payload, stream=False)
        response.raise_for_status()  
        print(response.text)
        # for line in response.iter_lines():  
        #     if line:  
        #         try:  
        #             # 去掉 "data: " 前缀  
        #             line = line.lstrip(b'data: ')  
        #             data = line.decode('utf-8')  
        #             # 假设服务器返回的是JSON格式的数据  
        #             import json  
        #             json_data = json.loads(data)  
        #             content = json_data.get('data', json_data).get('content')  
        #             if content:  
        #                 # 打印 content 内容  
        #                 print(content, end='', flush=True)  
        #         except json.JSONDecodeError:  
        #             print(f"无法解析JSON数据: {line}")  
    except requests.RequestException as e:  
        print(f"发送消息失败: {e}") 
  
# 主函数  
def main(message, re_chat=False):  
    profile_id = get_profile_id()  
    if profile_id:  
        print(profile_id)  
        chat_id = get_chat_id(profile_id)  
        if chat_id:  
            print(chat_id)  
            chat_message_payload = {  
                "message": message,  
                "re_chat": re_chat, 
                "form_data": {},
                "image_list": [
                    {
                        "name": "图片2.png",
                        "url": "/api/file/985388b4-5229-11f0-bbb8-00163e4f3d7b",
                        "file_id": "985388b4-5229-11f0-bbb8-00163e4f3d7b"
                    },
                    {
                        "name": "图片3.png",
                        "url": "/api/file/973392bc-5229-11f0-bbb8-00163e4f3d7b",
                        "file_id": "973392bc-5229-11f0-bbb8-00163e4f3d7b"
                    }
                ],
                "document_list": [],
                "audio_list": []
            }  
            send_chat_message(chat_id, chat_message_payload)  
        else:  
            print("获取chat id失败")  
    else:  
        print("获取profile id失败")  



  
if __name__ == "__main__":  
    # 在此自定义消息内容和参数  
    message = "123"  
    main(message, re_chat=False)  

