// 测试中文导出功能
import { ExportService } from '@/services/export'
import type { Question, ExportSettings } from '@/types'

// 创建测试数据
export function createTestData(): { questions: Question[], settings: ExportSettings, totalScore: number } {
  const questions: Question[] = [
    {
      id: '1',
      content: {
        stem: '下列关于计算机网络的描述，正确的是？',
        options: [
          '计算机网络只能连接同一地区的计算机',
          '互联网是世界上最大的计算机网络',
          '局域网不能连接到互联网',
          '计算机网络只能传输文字信息'
        ],
        answer: 'B',
        explanation: '互联网（Internet）是全球最大的计算机网络，连接了世界各地的计算机和网络设备。'
      },
      tags: {
        subject: '计算机基础',
        chapter: '计算机网络',
        difficulty: 'easy',
        questionType: 'single-choice'
      },
      score: 5,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: '2',
      content: {
        stem: '请简述操作系统的主要功能。',
        answer: '操作系统的主要功能包括：1. 进程管理；2. 内存管理；3. 文件系统管理；4. 设备管理；5. 用户界面管理。',
        explanation: '操作系统是计算机系统的核心软件，负责管理和协调计算机硬件与软件资源。'
      },
      tags: {
        subject: '计算机基础',
        chapter: '操作系统',
        difficulty: 'medium',
        questionType: 'short-answer'
      },
      score: 10,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: '3',
      content: {
        stem: '数据库管理系统（DBMS）是用来管理数据库的软件。',
        answer: '正确',
        explanation: 'DBMS（Database Management System）确实是专门用于管理数据库的系统软件。'
      },
      tags: {
        subject: '数据库',
        chapter: '数据库基础',
        difficulty: 'easy',
        questionType: 'true-false'
      },
      score: 3,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ]

  const settings: ExportSettings = {
    title: '计算机基础知识测试卷',
    filename: '计算机基础测试',
    format: 'pdf',
    includes: ['answers', 'explanations'],
    duration: 120,
    watermark: {
      enabled: true,
      text: '智卷云测试'
    }
  }

  const totalScore = questions.reduce((sum, q) => sum + q.score, 0)

  return { questions, settings, totalScore }
}

// 测试PDF导出
export async function testPDFExport(): Promise<void> {
  try {
    console.log('开始测试PDF导出...')
    
    const { questions, settings, totalScore } = createTestData()
    
    const blob = await ExportService.exportToPDF({
      questions,
      settings,
      totalScore,
      onProgress: (progress) => {
        console.log(`导出进度: ${progress}%`)
      }
    })
    
    console.log('PDF导出成功，文件大小:', blob.size, 'bytes')
    
    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = '中文测试.pdf'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    console.log('测试完成，文件已下载')
    
  } catch (error) {
    console.error('PDF导出测试失败:', error)
  }
}

// 测试DOCX导出
export async function testDOCXExport(): Promise<void> {
  try {
    console.log('开始测试DOCX导出...')
    
    const { questions, settings, totalScore } = createTestData()
    settings.format = 'word'
    
    const blob = await ExportService.exportToDOCX({
      questions,
      settings,
      totalScore,
      onProgress: (progress) => {
        console.log(`导出进度: ${progress}%`)
      }
    })
    
    console.log('DOCX导出成功，文件大小:', blob.size, 'bytes')
    
    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = '中文测试.docx'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    console.log('测试完成，文件已下载')
    
  } catch (error) {
    console.error('DOCX导出测试失败:', error)
  }
}

// 在浏览器控制台中运行测试
if (typeof window !== 'undefined') {
  // @ts-ignore
  window.testChineseExport = {
    testPDF: testPDFExport,
    testDOCX: testDOCXExport,
    createTestData
  }
  
  console.log('中文导出测试工具已加载')
  console.log('使用方法:')
  console.log('- window.testChineseExport.testPDF() // 测试PDF导出')
  console.log('- window.testChineseExport.testDOCX() // 测试DOCX导出')
}
