# 智卷云前端项目设计文档

## 概述

智卷云前端项目是一个基于Vue 3 + TypeScript的现代化Web应用，采用组合式API和响应式设计，为教师提供智能出题和组卷功能。系统采用模块化架构，支持多维度题目筛选、AI智能出题、实时编辑和多格式导出。

## 技术架构

### 技术栈选择

- **前端框架**: Vue 3 (Composition API)
- **开发语言**: TypeScript
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **HTTP客户端**: Axios
- **样式方案**: SCSS + CSS Modules
- **拖拽功能**: VueDraggable
- **富文本编辑**: Quill.js
- **文件导出**: jsPDF + docx.js
- **图标库**: Element Plus Icons

### 项目结构

```
src/
├── components/          # 通用组件
│   ├── common/         # 基础组件
│   ├── forms/          # 表单组件
│   └── ui/             # UI组件
├── views/              # 页面组件
│   ├── QuestionFilter/ # 题目筛选页面
│   ├── QuestionEdit/   # 题目编辑页面
│   ├── PaperLayout/    # 试卷排版页面
│   └── Export/         # 导出页面
├── stores/             # Pinia状态管理
├── services/           # API服务
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
├── composables/        # 组合式函数
├── assets/             # 静态资源
└── styles/             # 全局样式
```

## 核心组件设计

### 1. 题目筛选组件 (QuestionFilter)

**组件职责**: 提供多维度题目筛选界面

**主要功能**:
- 年级、学科、知识点级联选择
- 题型、难度多选筛选
- 地区选择和题目数量设置
- 样题文件上传

**组件结构**:
```typescript
interface FilterParams {
  grade: string
  subject: string
  knowledgePoints: string[]
  questionTypes: string[]
  difficulty: string[]
  region: string
  questionCount: number
  sampleFile?: File
}
```

**关键特性**:
- 使用Element Plus的级联选择器实现年级-学科-知识点联动
- 支持多文件格式上传（Word、PDF、图片）
- 实时参数校验和错误提示
- 响应式布局适配移动端

### 2. 题目列表组件 (QuestionList)

**组件职责**: 展示和管理题目列表

**主要功能**:
- 题目列表展示（支持虚拟滚动）
- 拖拽排序
- 批量操作（删除、分值设置）
- 题目内容预览

**组件结构**:
```typescript
interface Question {
  id: string
  content: {
    stem: string
    options?: string[]
    answer: string
    explanation: string
    attachments?: string[]
  }
  tags: {
    grade: string
    subject: string
    questionType: string
    difficulty: string
    knowledgePoint: string[]
    scenario: string
    sourceType: string
  }
  score: number
  order: number
}
```

### 3. 题目编辑器组件 (QuestionEditor)

**组件职责**: 提供题目内容的在线编辑功能

**主要功能**:
- 富文本编辑器集成
- 数学公式支持
- 图片上传和管理
- 实时保存和版本控制

**技术实现**:
- 使用Quill.js作为富文本编辑器
- 集成MathQuill支持数学公式编辑
- 实现自动保存机制（防抖处理）
- 支持撤销/重做操作

### 4. 试卷排版组件 (PaperLayout)

**组件职责**: 提供试卷排版和预览功能

**主要功能**:
- 模板选择（A4/A3、横竖版）
- 排版参数设置
- 实时预览
- 自动分页计算

**排版算法**:
```typescript
interface LayoutParams {
  paperSize: 'A4' | 'A3'
  orientation: 'portrait' | 'landscape'
  fontSize: number
  lineHeight: number
  margin: {
    top: number
    right: number
    bottom: number
    left: number
  }
  showAnswer: boolean
  showExplanation: boolean
  questionGrouping: 'byType' | 'mixed'
}
```

### 5. 文件导出组件 (FileExporter)

**组件职责**: 处理试卷的导出和下载

**主要功能**:
- PDF导出（使用jsPDF）
- Word导出（使用docx.js）
- 打印功能
- 导出历史管理

**导出流程**:
1. 收集题目数据和排版参数
2. 生成HTML模板
3. 转换为目标格式
4. 提供下载链接

## 数据流设计

### 状态管理架构

使用Pinia进行状态管理，按功能模块划分Store：

```typescript
// 题目筛选状态
export const useFilterStore = defineStore('filter', () => {
  const filterParams = ref<FilterParams>({})
  const questions = ref<Question[]>([])
  
  const fetchQuestions = async (params: FilterParams) => {
    // API调用逻辑
  }
  
  return { filterParams, questions, fetchQuestions }
})

// 题目编辑状态
export const useQuestionStore = defineStore('question', () => {
  const selectedQuestions = ref<Question[]>([])
  const editHistory = ref<EditAction[]>([])
  
  const updateQuestion = (id: string, updates: Partial<Question>) => {
    // 更新逻辑
  }
  
  return { selectedQuestions, editHistory, updateQuestion }
})

// 排版状态
export const useLayoutStore = defineStore('layout', () => {
  const layoutParams = ref<LayoutParams>({})
  const previewHtml = ref<string>('')
  
  const generatePreview = () => {
    // 预览生成逻辑
  }
  
  return { layoutParams, previewHtml, generatePreview }
})
```

### API服务设计

```typescript
// API服务接口
export class QuestionService {
  // 题目搜索
  static async searchQuestions(params: FilterParams): Promise<Question[]> {
    return await apiClient.post('/api/questions/search', params)
  }
  
  // AI出题
  static async generateQuestions(params: GenerateParams): Promise<Question[]> {
    return await apiClient.post('/api/questions/generate', params)
  }
  
  // 样题上传
  static async uploadSample(file: File): Promise<SampleInfo> {
    const formData = new FormData()
    formData.append('file', file)
    return await apiClient.post('/api/samples/upload', formData)
  }
}

// 导出服务
export class ExportService {
  static async exportToPDF(questions: Question[], layout: LayoutParams): Promise<Blob> {
    // PDF导出逻辑
  }
  
  static async exportToWord(questions: Question[], layout: LayoutParams): Promise<Blob> {
    // Word导出逻辑
  }
}
```

## 界面设计

### 设计原则

1. **简洁直观**: 界面布局清晰，操作流程符合用户习惯
2. **响应式设计**: 适配桌面端、平板和移动端
3. **一致性**: 统一的视觉风格和交互模式
4. **可访问性**: 支持键盘导航和屏幕阅读器

### 主要页面布局

#### 1. 题目筛选页面
- 左侧：筛选条件面板
- 右侧：题目预览区域
- 底部：操作按钮区域

#### 2. 题目编辑页面
- 左侧：题目列表（支持拖拽排序）
- 右侧：题目详情编辑器
- 顶部：工具栏（保存、撤销、重做等）

#### 3. 试卷排版页面
- 左侧：排版参数设置面板
- 中间：试卷预览区域
- 右侧：题目列表和操作面板

### 响应式断点

```scss
// 响应式断点定义
$breakpoints: (
  'mobile': 768px,
  'tablet': 1024px,
  'desktop': 1200px,
  'wide': 1600px
);
```

## 性能优化

### 1. 组件优化
- 使用`defineAsyncComponent`实现组件懒加载
- 合理使用`v-memo`和`v-once`指令
- 实现虚拟滚动处理大量题目列表

### 2. 数据优化
- 实现题目数据的分页加载
- 使用防抖处理搜索和编辑操作
- 缓存常用的筛选结果

### 3. 资源优化
- 图片懒加载和压缩
- 代码分割和按需加载
- CDN加速静态资源

## 错误处理

### 错误类型定义

```typescript
enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  FILE_UPLOAD_ERROR = 'FILE_UPLOAD_ERROR',
  EXPORT_ERROR = 'EXPORT_ERROR'
}

interface AppError {
  type: ErrorType
  message: string
  details?: any
}
```

### 错误处理策略

1. **网络错误**: 自动重试机制，显示网络状态提示
2. **验证错误**: 实时表单验证，友好的错误提示
3. **文件错误**: 文件格式和大小检查，上传进度显示
4. **导出错误**: 错误日志记录，支持重新导出

## 测试策略

### 单元测试
- 使用Vitest进行组件和工具函数测试
- 覆盖核心业务逻辑和边界情况
- 目标测试覆盖率：80%以上

### 集成测试
- 使用Cypress进行端到端测试
- 覆盖主要用户流程
- 自动化回归测试

### 测试用例示例

```typescript
// 题目筛选组件测试
describe('QuestionFilter', () => {
  it('should filter questions by grade and subject', async () => {
    const wrapper = mount(QuestionFilter)
    await wrapper.find('[data-test="grade-select"]').setValue('高三')
    await wrapper.find('[data-test="subject-select"]').setValue('数学')
    
    expect(wrapper.emitted('filter-change')).toBeTruthy()
  })
})
```

## 部署方案

### 构建配置

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus'],
          utils: ['axios', 'lodash-es']
        }
      }
    }
  }
})
```

### 环境配置

- **开发环境**: 本地开发服务器，热重载
- **测试环境**: 自动化测试和预发布验证
- **生产环境**: CDN部署，性能监控

## 安全考虑

1. **输入验证**: 所有用户输入进行严格验证
2. **文件上传**: 限制文件类型和大小，病毒扫描
3. **XSS防护**: 内容过滤和转义
4. **CSRF防护**: 请求令牌验证
5. **数据加密**: 敏感数据传输加密