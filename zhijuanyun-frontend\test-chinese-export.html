<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文导出测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', SimSun, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
            color: #333;
        }
        .test-info {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
        }
        .question {
            margin-bottom: 25px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .question-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .options {
            margin-left: 20px;
        }
        .option {
            margin: 5px 0;
        }
        .answer {
            margin-top: 10px;
            padding: 8px;
            background: #e8f5e8;
            border-left: 3px solid #4caf50;
        }
        .explanation {
            margin-top: 10px;
            padding: 8px;
            background: #f0f8ff;
            border-left: 3px solid #2196f3;
        }
        .test-buttons {
            text-align: center;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 0 10px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn-primary {
            background: #409eff;
            color: white;
        }
        .btn-success {
            background: #67c23a;
            color: white;
        }
        .btn-warning {
            background: #e6a23c;
            color: white;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 5px;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <div class="test-buttons">
        <button class="btn-primary" onclick="testSimplePDF()">测试简单PDF</button>
        <button class="btn-success" onclick="testHTMLToPDF()">测试HTML转PDF</button>
        <button class="btn-warning" onclick="testCanvasRender()">测试Canvas渲染</button>
    </div>

    <div id="result"></div>

    <div class="test-container" id="examContent">
        <div class="test-title">计算机基础知识测试卷</div>
        <div class="test-info">
            本试卷共 3 题，总分 18 分，考试时间 120 分钟
        </div>
        
        <div style="margin-bottom: 20px; padding: 15px; background: #f9f9f9; border-radius: 5px;">
            <h3 style="margin: 0 0 10px 0;">注意事项：</h3>
            <ol style="margin: 0; padding-left: 20px;">
                <li>请在答题前仔细阅读各题目要求</li>
                <li>所有答案必须写在答题纸上，写在试卷上无效</li>
                <li>考试结束后，将试卷和答题纸一并交回</li>
            </ol>
        </div>

        <div class="question">
            <div class="question-title">1. (5分) 下列关于计算机网络的描述，正确的是？</div>
            <div class="options">
                <div class="option">A. 计算机网络只能连接同一地区的计算机</div>
                <div class="option">B. 互联网是世界上最大的计算机网络</div>
                <div class="option">C. 局域网不能连接到互联网</div>
                <div class="option">D. 计算机网络只能传输文字信息</div>
            </div>
            <div class="answer"><strong>答案：</strong>B</div>
            <div class="explanation"><strong>解析：</strong>互联网（Internet）是全球最大的计算机网络，连接了世界各地的计算机和网络设备。</div>
        </div>

        <div class="question">
            <div class="question-title">2. (10分) 请简述操作系统的主要功能。</div>
            <div class="answer"><strong>答案：</strong>操作系统的主要功能包括：1. 进程管理；2. 内存管理；3. 文件系统管理；4. 设备管理；5. 用户界面管理。</div>
            <div class="explanation"><strong>解析：</strong>操作系统是计算机系统的核心软件，负责管理和协调计算机硬件与软件资源。</div>
        </div>

        <div class="question">
            <div class="question-title">3. (3分) 数据库管理系统（DBMS）是用来管理数据库的软件。</div>
            <div class="answer"><strong>答案：</strong>正确</div>
            <div class="explanation"><strong>解析：</strong>DBMS（Database Management System）确实是专门用于管理数据库的系统软件。</div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
        function log(message) {
            const result = document.getElementById('result');
            result.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }

        function downloadBlob(blob, filename) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 测试简单PDF生成
        function testSimplePDF() {
            log('开始测试简单PDF生成...');
            
            try {
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF();
                
                // 尝试直接添加中文文本
                pdf.setFontSize(16);
                pdf.text('这是一个中文测试', 20, 20);
                pdf.text('测试题目：计算机基础知识', 20, 40);
                pdf.text('答案：正确', 20, 60);
                pdf.text('解析：这是一个测试解析', 20, 80);
                
                const blob = pdf.output('blob');
                downloadBlob(blob, '简单中文测试.pdf');
                log('简单PDF生成成功，文件大小: ' + blob.size + ' bytes');
                
            } catch (error) {
                log('简单PDF生成失败: ' + error.message);
            }
        }

        // 测试HTML转PDF
        async function testHTMLToPDF() {
            log('开始测试HTML转PDF...');
            
            try {
                const element = document.getElementById('examContent');
                
                log('正在渲染HTML为Canvas...');
                const canvas = await html2canvas(element, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff'
                });
                
                log('Canvas渲染完成，正在生成PDF...');
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');
                
                const imgData = canvas.toDataURL('image/png');
                const pdfWidth = pdf.internal.pageSize.getWidth();
                const pdfHeight = pdf.internal.pageSize.getHeight();
                const imgWidth = canvas.width;
                const imgHeight = canvas.height;
                
                // 计算缩放比例
                const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
                const scaledWidth = imgWidth * ratio;
                const scaledHeight = imgHeight * ratio;
                
                // 居中显示
                const x = (pdfWidth - scaledWidth) / 2;
                const y = (pdfHeight - scaledHeight) / 2;
                
                pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight);
                
                const blob = pdf.output('blob');
                downloadBlob(blob, 'HTML转PDF测试.pdf');
                log('HTML转PDF成功，文件大小: ' + blob.size + ' bytes');
                
            } catch (error) {
                log('HTML转PDF失败: ' + error.message);
            }
        }

        // 测试Canvas渲染
        async function testCanvasRender() {
            log('开始测试Canvas渲染...');
            
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = 800;
                canvas.height = 600;
                
                // 设置中文字体
                ctx.font = '16px Microsoft YaHei, SimSun, sans-serif';
                ctx.fillStyle = '#000000';
                ctx.textBaseline = 'top';
                
                // 绘制中文文本
                ctx.fillText('这是Canvas渲染的中文文本', 20, 20);
                ctx.fillText('测试题目：计算机基础知识', 20, 50);
                ctx.fillText('答案：正确', 20, 80);
                ctx.fillText('解析：这是一个测试解析', 20, 110);
                
                // 转换为PDF
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF();
                
                const imgData = canvas.toDataURL('image/png');
                pdf.addImage(imgData, 'PNG', 10, 10, 190, 142.5);
                
                const blob = pdf.output('blob');
                downloadBlob(blob, 'Canvas渲染测试.pdf');
                log('Canvas渲染测试成功，文件大小: ' + blob.size + ' bytes');
                
            } catch (error) {
                log('Canvas渲染测试失败: ' + error.message);
            }
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            log('测试页面加载完成，可以开始测试');
            log('请点击上方按钮进行不同方案的测试');
        };
    </script>
</body>
</html>
