<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>获取 Profile ID 接口文档 - Apifox风格</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0 auto;
            max-width: 960px;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        pre {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            background-color: #eee;
            padding: 2px 4px;
            border-radius: 3px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 8px;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>

<h1>📄 接口名称：获取 Profile ID</h1>

<h2>🔗 请求地址</h2>
<pre>GET https://ai.procaas.com/api/application/e69bd1b6-2a24-11f0-89bb-0242ac130003</pre>
<h2>🔗 不同应用有不同的URL</h2>
<pre>GET base_url</pre>

<h2>⚙️ 请求头（Headers）</h2>
<table>
    <tr><th>Key</th><th>Value</th></tr>
    <tr><td>AUTHORIZATION</td><td>api_key</td></tr>
    <tr><td>Accept</td><td>application/json</td></tr>
</table>

<h2>✅ 成功响应示例（Status Code: 200）</h2>
<pre>
{
  "code": 200,
  "message": "成功",
  "data": {
    "id": "e69bd1b6-2a24-11f0-89bb-0242ac130003",
    "create_time": "2025-05-06T10:50:42.464945+08:00",
    "update_time": "2025-05-21T15:24:02.004106+08:00",
    "name": "AI数字人",
    "desc": "用于职中AI数字人问答",
    "prologue": "您好，我是江门一职校园助手，您可以向我提出江门一职相关问题。\n- 江门一职的全称是什么？\n- 宿舍有空调吗？电费怎么算？\n- 宿舍的电费和水费怎么算？",
    "dialogue_number": 1,
    "dataset_setting": {
      "search_mode": "blend",
      "no_references_setting": {"value": "{question}", "status": "ai_questioning"},
      "top_n": 3,
      "similarity": 0.6,
      "max_paragraph_char_number": 5000
    },
    "model_setting": {
      "prompt": "已知信息：{data}\n用户问题：{question}\n回答要求：\n - 请使用中文回答用户问题",
      "system": "你是AI小助手",
      "no_references_prompt": "{question}"
    },
    "icon": "/ui/favicon.ico",
    "type": "SIMPLE",
    "problem_optimization": false,
    "tts_model_enable": false,
    "stt_model_enable": false,
    "tts_type": "BROWSER",
    "tts_autoplay": false,
    "stt_autosend": false,
    "clean_time": 180,
    "file_upload_enable": false,
    "multiple_rounds_dialogue": true,
    "dataset_id_list": ["59b30dba-2a23-11f0-865c-0242ac130003"]
  }
}
</pre>

<h2>❌ 失败响应示例（例如 Status Code: 400）</h2>
<pre>
{
  "code": 400,
  "message": "请求无效",
  "data": null
}
</pre>

<h2>⚠️ 注意事项</h2>
<ul>
    <li>确保替换 <code>AUTHORIZATION</code> 的值为你的实际 API Key。</li>
  
</ul>


</body>
</html>