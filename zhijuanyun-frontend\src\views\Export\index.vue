<template>
  <div class="export">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>试卷导出</span>
          <el-button-group>
            <el-button @click="handleBack">返回</el-button>
            <el-button type="primary" @click="handleExport" :loading="exporting">
              立即导出
            </el-button>
          </el-button-group>
        </div>
      </template>
      
      <div class="export-content">
        <el-row :gutter="24">
          <!-- Left: Export Settings -->
          <el-col :span="8">
            <el-card shadow="never">
              <template #header>
                <span>导出设置</span>
              </template>
              
              <el-form :model="exportForm" label-width="100px">
                <el-form-item label="导出格式" required>
                  <el-radio-group v-model="exportForm.format" @change="handleFormatChange">
                    <el-radio value="pdf">PDF 格式</el-radio>
                    <el-radio value="word">Word 格式</el-radio>
                  </el-radio-group>
                </el-form-item>
                
                <el-form-item label="文件名称" required>
                  <el-input 
                    v-model="exportForm.filename" 
                    placeholder="请输入文件名"
                  />
                </el-form-item>
                
                <el-form-item label="试卷标题">
                  <el-input 
                    v-model="exportForm.title" 
                    placeholder="请输入试卷标题"
                  />
                </el-form-item>
                
                <el-form-item label="考试时间">
                  <el-input-number
                    v-model="exportForm.duration"
                    :min="30"
                    :max="300"
                    :step="15"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：分钟</div>
                </el-form-item>
                
                <el-form-item label="包含内容">
                  <el-checkbox-group v-model="exportForm.includes">
                    <el-checkbox value="questions" disabled checked>题目内容</el-checkbox>
                    <el-checkbox value="answers">参考答案</el-checkbox>
                    <el-checkbox value="explanations">答案解析</el-checkbox>
                    <el-checkbox value="score_breakdown">分值分布</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                
                <el-form-item label="答题卡模式">
                  <el-switch
                    v-model="exportForm.answerSheet"
                    active-text="生成答题卡"
                    inactive-text="答案在试卷上"
                  />
                </el-form-item>
                
                <el-form-item label="水印设置">
                  <el-switch
                    v-model="exportForm.watermark.enabled"
                    active-text="添加水印"
                    inactive-text="无水印"
                  />
                  <el-input
                    v-if="exportForm.watermark.enabled"
                    v-model="exportForm.watermark.text"
                    placeholder="水印文字"
                    style="margin-top: 8px"
                  />
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
          
          <!-- Right: Preview and Export History -->
          <el-col :span="16">
            <el-tabs v-model="activeTab">
              <el-tab-pane label="导出预览" name="preview">
                <div class="preview-container">
                  <div class="preview-toolbar">
                    <div class="preview-info">
                      <el-tag type="info">{{ questions.length }} 道题目</el-tag>
                      <el-tag type="success">总分 {{ totalScore }} 分</el-tag>
                      <el-tag>{{ exportForm.format.toUpperCase() }} 格式</el-tag>
                      <el-tag v-if="estimatedSize" type="warning">{{ estimatedSize }}</el-tag>
                    </div>
                    
                    <div class="preview-actions">
                      <el-button-group size="small">
                        <el-button @click="previewZoom = Math.max(0.5, previewZoom - 0.1)">
                          <el-icon><ZoomOut /></el-icon>
                        </el-button>
                        <el-button @click="previewZoom = 1">
                          {{ (previewZoom * 100).toFixed(0) }}%
                        </el-button>
                        <el-button @click="previewZoom = Math.min(2, previewZoom + 0.1)">
                          <el-icon><ZoomIn /></el-icon>
                        </el-button>
                      </el-button-group>
                    </div>
                  </div>
                  
                  <div class="preview-content">
                    <div 
                      class="paper-preview"
                      :style="{ transform: `scale(${previewZoom})` }"
                    >
                      <ExportPreview
                        :questions="questions"
                        :settings="exportForm"
                        :total-score="totalScore"
                      />
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              
              <el-tab-pane label="导出历史" name="history">
                <div class="export-history">
                  <el-empty v-if="exportHistory.length === 0" description="暂无导出记录" />
                  
                  <div v-else class="history-list">
                    <div 
                      v-for="record in exportHistory" 
                      :key="record.id"
                      class="history-item"
                    >
                      <div class="history-info">
                        <div class="history-title">{{ record.filename }}</div>
                        <div class="history-meta">
                          <span>{{ record.format.toUpperCase() }}</span>
                          <span>{{ record.questionCount }} 题</span>
                          <span v-if="record.fileSize">{{ formatFileSize(record.fileSize) }}</span>
                          <span>{{ formatDateTime(record.exportTime) }}</span>
                        </div>
                      </div>
                      
                      <div class="history-actions">
                        <el-button size="small" @click="handleDownload(record)">
                          <el-icon><Download /></el-icon>
                          下载
                        </el-button>
                        <el-button 
                          size="small" 
                          type="danger" 
                          text 
                          @click="handleDeleteHistory(record.id)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-col>
        </el-row>
      </div>
    </el-card>
    
    <!-- Export Progress Dialog -->
    <el-dialog
      v-model="exportProgressVisible"
      title="正在导出..."
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="export-progress">
        <el-progress 
          :percentage="exportProgress" 
          :status="exportStatus"
          :stroke-width="8"
        />
        <div class="progress-text">{{ exportProgressText }}</div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ZoomIn, ZoomOut, Download } from '@element-plus/icons-vue'
import { useFilterStore } from '@/stores/filter'
import type { Question, ExportSettings } from '@/types'

// Import components
import ExportPreview from '@/components/ui/ExportPreview.vue'

const filterStore = useFilterStore()
const router = useRouter()

// State
const questions = ref<Question[]>([])
const activeTab = ref('preview')
const exporting = ref(false)
const exportProgressVisible = ref(false)
const exportProgress = ref(0)
const exportStatus = ref<'success' | 'exception' | 'warning' | ''>('')
const exportProgressText = ref('')
const previewZoom = ref(0.8)

// Export form
const exportForm = ref<ExportSettings>({
  format: 'pdf',
  filename: '试卷',
  title: '期末考试试卷',
  duration: 120,
  includes: ['questions'],
  answerSheet: false,
  watermark: {
    enabled: false,
    text: '内部资料 请勿外传'
  }
})

// Export history
const exportHistory = ref<Array<{
  id: string
  filename: string
  format: string
  questionCount: number
  exportTime: string
  downloadUrl?: string
  fileSize?: number
}>>([
  {
    id: '1',
    filename: '数学期末试卷_2024-01-15',
    format: 'pdf',
    questionCount: 25,
    exportTime: '2024-01-15T10:30:00Z'
  },
  {
    id: '2',
    filename: '语文模拟试卷_2024-01-10',
    format: 'word',
    questionCount: 20,
    exportTime: '2024-01-10T14:20:00Z'
  }
])

// Computed
const totalScore = computed(() => {
  return questions.value.reduce((total, question) => total + question.score, 0)
})

const estimatedSize = computed(() => {
  if (questions.value.length === 0) return null
  
  const baseSize = exportForm.value.format === 'pdf' ? 50 : 100 // KB
  const questionSize = exportForm.value.format === 'pdf' ? 2 : 3 // KB per question
  const totalSize = baseSize + (questions.value.length * questionSize)
  
  if (totalSize < 1024) {
    return `${totalSize}KB`
  } else {
    return `${(totalSize / 1024).toFixed(1)}MB`
  }
})

// Methods
const handleBack = () => {
  // Navigate back to layout page
  router.push('/layout')
}

const handleFormatChange = () => {
  // Update default filename based on format
  const today = new Date().toISOString().slice(0, 10)
  const baseFilename = exportForm.value.filename.replace(/\.pdf$|\.docx$/, '')
  exportForm.value.filename = `${baseFilename}_${today}`
}

const handleExport = async () => {
  if (questions.value.length === 0) {
    ElMessage.warning('没有题目可以导出')
    return
  }
  
  if (!exportForm.value.filename.trim()) {
    ElMessage.warning('请输入文件名')
    return
  }
  
  try {
    exporting.value = true
    exportProgressVisible.value = true
    exportProgress.value = 0
    exportStatus.value = ''
    
    // Import ExportService dynamically
    const { ExportService } = await import('@/services/export')
    
    // Validate export size before processing
    const format = exportForm.value.format === 'word' ? 'docx' : 'pdf'
    const sizeValidation = ExportService.validateExportSize(questions.value, format)
    if (!sizeValidation.valid) {
      ElMessage.warning(sizeValidation.warning)
      return
    }
    
    if (sizeValidation.warning) {
      ElMessage.info(sizeValidation.warning)
    }
    
    exportProgress.value = 5
    exportProgressText.value = '正在验证导出参数...'
    
    // Prepare export options
    const exportOptions = {
      questions: questions.value,
      settings: exportForm.value,
      totalScore: totalScore.value,
      onProgress: (progress: number) => {
        exportProgress.value = progress
        exportProgressText.value = getExportProgressText(progress, exportForm.value.format)
      }
    }
    
    let blob: Blob
    let filename: string
    
    if (exportForm.value.format === 'pdf') {
      exportProgressText.value = '正在生成PDF文件...'
      blob = await ExportService.exportToPDF(exportOptions)
      filename = `${exportForm.value.filename}.pdf`
    } else {
      exportProgressText.value = '正在生成Word文件...'
      blob = await ExportService.exportToDOCX(exportOptions)
      filename = `${exportForm.value.filename}.docx`
    }
    
    exportStatus.value = 'success'
    exportProgressText.value = '导出完成'
    
    // Download the file
    downloadFile(blob, filename)
    
    // Add to export history
    const newRecord = {
      id: Date.now().toString(),
      filename: filename,
      format: exportForm.value.format,
      questionCount: questions.value.length,
      exportTime: new Date().toISOString(),
      downloadUrl: URL.createObjectURL(blob),
      fileSize: blob.size
    }
    
    exportHistory.value.unshift(newRecord)
    
    // Save to localStorage for persistence
    saveExportHistory(newRecord)
    
    ElMessage.success(`${exportForm.value.format.toUpperCase()} 文件导出成功`)
    
    // Switch to history tab
    activeTab.value = 'history'
    
  } catch (error) {
    console.error('Export failed:', error)
    exportStatus.value = 'exception'
    exportProgressText.value = '导出失败'
    
    // Enhanced error handling
    let errorMessage = '导出失败，请重试'
    if (error instanceof Error) {
      if (error.message.includes('memory') || error.message.includes('内存')) {
        errorMessage = '内存不足，请减少题目数量后重试'
      } else if (error.message.includes('timeout') || error.message.includes('超时')) {
        errorMessage = '导出超时，请减少题目数量后重试'
      } else if (error.message.includes('format') || error.message.includes('格式')) {
        errorMessage = '文件格式错误，请检查题目内容后重试'
      } else if (error.message.includes('没有题目')) {
        errorMessage = '没有题目可以导出，请先生成题目'
      }
    }
    
    ElMessage.error(errorMessage)
  } finally {
    setTimeout(() => {
      exporting.value = false
      exportProgressVisible.value = false
      exportProgress.value = 0
      exportStatus.value = ''
      exportProgressText.value = ''
    }, 1000)
  }
}

const handleDownload = async (record: any) => {
  try {
    if (record.downloadUrl) {
      // If we have a blob URL, download it directly
      downloadFileFromUrl(record.downloadUrl, record.filename)
    } else {
      // Try to regenerate the file
      ElMessage.info('正在重新生成文件...')
      
      const { ExportService } = await import('@/services/export')
      
      const exportOptions = {
        questions: questions.value,
        settings: exportForm.value,
        totalScore: totalScore.value
      }
      
      let blob: Blob
      if (record.format === 'pdf') {
        blob = await ExportService.exportToPDF(exportOptions)
      } else {
        blob = await ExportService.exportToDOCX(exportOptions)
      }
      
      downloadFile(blob, record.filename)
      ElMessage.success(`文件 ${record.filename} 下载成功`)
    }
  } catch (error) {
    console.error('Download failed:', error)
    ElMessage.error('文件下载失败，请重新导出')
  }
}

const handleDeleteHistory = async (id: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条导出记录吗？',
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    exportHistory.value = exportHistory.value.filter(record => record.id !== id)
    ElMessage.success('删除成功')
  } catch {
    // User cancelled
  }
}

const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) {
    return `${bytes}B`
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(1)}KB`
  } else {
    return `${(bytes / 1024 / 1024).toFixed(1)}MB`
  }
}

// File download helper
const downloadFile = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// Download from URL helper
const downloadFileFromUrl = (url: string, filename: string) => {
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

// Progress text helper
const getExportProgressText = (progress: number, format: string): string => {
  if (progress < 20) {
    return '准备导出数据...'
  } else if (progress < 40) {
    return `正在生成${format.toUpperCase()}内容...`
  } else if (progress < 60) {
    return '应用排版设置...'
  } else if (progress < 80) {
    return '生成文件格式...'
  } else if (progress < 100) {
    return '完成导出...'
  } else {
    return '导出完成'
  }
}

// Save export history to localStorage
const saveExportHistory = (record: any) => {
  try {
    let history = JSON.parse(localStorage.getItem('exportHistory') || '[]')
    
    // Remove existing records with same filename to avoid duplicates
    history = history.filter((item: any) => item.filename !== record.filename)
    
    // Add new record
    history.unshift(record)
    
    // Keep only last 20 records
    if (history.length > 20) {
      history = history.slice(0, 20)
    }
    
    localStorage.setItem('exportHistory', JSON.stringify(history))
  } catch (error) {
    console.warn('Failed to save export history:', error)
  }
}

// Load export history from localStorage
const loadExportHistory = () => {
  try {
    const history = JSON.parse(localStorage.getItem('exportHistory') || '[]')
    
    // Filter out records older than 30 days
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const validHistory = history.filter((record: any) => {
      const exportTime = new Date(record.exportTime)
      return exportTime > thirtyDaysAgo
    })
    
    exportHistory.value = validHistory
    
    // Update localStorage if we filtered out old records
    if (validHistory.length !== history.length) {
      localStorage.setItem('exportHistory', JSON.stringify(validHistory))
    }
  } catch (error) {
    console.warn('Failed to load export history:', error)
    exportHistory.value = []
  }
}

// Clean up blob URLs to prevent memory leaks
const cleanupBlobUrls = () => {
  exportHistory.value.forEach(record => {
    if (record.downloadUrl) {
      URL.revokeObjectURL(record.downloadUrl)
      record.downloadUrl = undefined
    }
  })
}

// Initialize with questions from filter store
onMounted(() => {
  if (filterStore.questions.length > 0) {
    questions.value = [...filterStore.questions]
  }
  
  // Set default filename based on current date
  const today = new Date().toISOString().slice(0, 10)
  exportForm.value.filename = `试卷_${today}`
  
  // Load export history from localStorage
  loadExportHistory()
})

// Clean up resources when component is unmounted
onUnmounted(() => {
  cleanupBlobUrls()
})
</script>

<style scoped>
.export {
  height: 100vh;
  padding: 16px;
  background-color: var(--el-fill-color-extra-light);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.export-content {
  padding: 24px;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

.preview-container {
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--el-border-color-light);
  margin-bottom: 16px;
}

.preview-info {
  display: flex;
  gap: 8px;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  flex: 1;
  overflow: auto;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 6px;
}

.paper-preview {
  margin: 0 auto;
  transform-origin: top center;
  transition: transform 0.2s;
}

.export-history {
  height: 70vh;
  overflow-y: auto;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: var(--el-fill-color-blank);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  transition: all 0.2s;
}

.history-item:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.history-info {
  flex: 1;
}

.history-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.history-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.history-actions {
  display: flex;
  gap: 8px;
}

.export-progress {
  text-align: center;
  padding: 20px 0;
}

.progress-text {
  margin-top: 16px;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

/* Responsive design */
@media (max-width: 1200px) {
  .export-content .el-row {
    flex-direction: column;
  }
  
  .export-content .el-col {
    width: 100%;
    margin-bottom: 16px;
  }
}
</style>