import axios, { type AxiosInstance } from 'axios'

const CHAT_BASE_URL = 'http://ai.procaas.com:3000/api'
const API_KEY = 'application-7247ea1473ea4c43b783e1c7daa97abc'
const APP_ID = '9a4eef92-6e84-11f0-bbb8-00163e4f3d7b'

// Configuration constants
const DEFAULT_TIMEOUT = 90000 // 90 seconds default timeout
const MAX_RETRIES = 3
const RETRY_DELAY_BASE = 1000 // 1 second base delay for exponential backoff

const chatClient: AxiosInstance = axios.create({
  baseURL: CHAT_BASE_URL,
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json',
    'accept': 'application/json',
    'AUTHORIZATION': API_KEY
  }
})

export interface ChatMessage {
  message: string
  re_chat?: boolean
  form_data?: Record<string, any>
  image_list?: Array<{
    name: string
    url: string
    file_id: string
  }>
  document_list?: Array<any>
  audio_list?: Array<any>
}

export interface ChatResponse {
  code: number
  message: string
  data: any
}

export class ChatService {
  private static profileId: string | null = null
  private static chatId: string | null = null

  static async getProfileId(): Promise<string> {
    try {
      const response = await chatClient.get(`/application/${APP_ID}`)
      if (response.data.code === 200) {
        this.profileId = response.data.data.id
        return this.profileId!
      }
      throw new Error('Failed to get profile ID')
    } catch (error) {
      console.error('Error getting profile ID:', error)
      throw error
    }
  }

  static async getChatId(): Promise<string> {
    if (!this.profileId) {
      this.profileId = await this.getProfileId()
    }
    
    try {
      const response = await chatClient.get(`/application/${this.profileId!}/chat/open`)
      if (response.data.code === 200) {
        this.chatId = response.data.data
        return this.chatId!
      }
      throw new Error('Failed to get chat ID')
    } catch (error) {
      console.error('Error getting chat ID:', error)
      throw error
    }
  }

  static async sendMessage(message: string, options: {
    re_chat?: boolean
    form_data?: Record<string, any>
    image_list?: Array<{
      name: string
      url: string
      file_id: string
    }>
    onProgress?: (progress: number) => void
    maxRetries?: number
    timeout?: number
  } = {}): Promise<string> {
    const maxRetries = Math.min(options.maxRetries || MAX_RETRIES, MAX_RETRIES)
    const timeout = options.timeout || DEFAULT_TIMEOUT
    let lastError: any = null
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (options.onProgress) {
          options.onProgress((attempt - 1) * (100 / maxRetries))
        }
        
        if (!this.chatId) {
          console.log('Getting new chat ID...')
          this.chatId = await this.getChatId()
          console.log('Chat ID obtained:', this.chatId)
        }

        const payload: ChatMessage = {
          message,
          re_chat: options.re_chat || false,
          form_data: options.form_data || {},
          image_list: options.image_list || [],
          document_list: [],
          audio_list: []
        }

        console.log('Sending message to AI:', {
          chatId: this.chatId,
          message: payload.message,
          hasFormData: Object.keys(payload.form_data || {}).length > 0,
          formData: payload.form_data,
          attempt: `${attempt}/${maxRetries}`
        })
        
        const response = await chatClient.post(`/application/chat_message/${this.chatId!}`, payload, {
          timeout: timeout,
          responseType: 'text' // Get raw text response to handle SSE
        })
        
        if (options.onProgress) {
          options.onProgress(50)
        }
        
        console.log('AI Response status:', response.status)
        console.log('AI Response data type:', typeof response.data)
      
        // Handle streaming response (SSE format)
        const responseText = response.data
        console.log('Raw response length:', responseText.length)
      
        // Parse SSE format - extract all data chunks
        const dataChunks = responseText.split('\n').filter((line: string) => line.startsWith('data: '))
        console.log('Found data chunks:', dataChunks.length)
      
        let fullContent = ''
        let formFound = false
      
        for (const chunk of dataChunks) {
          try {
            const jsonData = JSON.parse(chunk.substring(6)) // Remove 'data: ' prefix

            if (jsonData.content) {
              fullContent += jsonData.content

              // Check if content contains form
              if (jsonData.content.includes('form_rander')) {
                formFound = true
              }

              // Check for error indicators in content
              if (jsonData.content.includes('Exception:') ||
                  jsonData.content.includes('Error:') ||
                  jsonData.content.includes('undefined')) {
                console.warn('AI response contains error information:', jsonData.content)
              }
            }
          } catch (parseError) {
            console.warn('Failed to parse JSON chunk:', chunk.substring(6))
          }
        }
      
        console.log('Total content length:', fullContent.length)
        console.log('Form found:', formFound)

        // Check if response contains error information
        if (fullContent.includes('Exception:') ||
            fullContent.includes('Error:') ||
            fullContent.includes('undefined')) {
          console.error('AI response contains error information:', fullContent.substring(0, 500))
          throw new Error('AI服务返回错误信息，请检查参数设置或稍后重试')
        }

        // If form is found, extract and submit it
        if (formFound) {
          console.log('Detected form response, extracting form data...')
          const formMatch = fullContent.match(/<form_rander>(.*?)<\/form_rander>/s)
          if (formMatch) {
            try {
              const formData = JSON.parse(formMatch[1])
              console.log('Extracted form data:', formData)

              // Submit the form with our parameters
              const submitResponse = await this.submitForm(formData, options.form_data || {})
              if (options.onProgress) {
                options.onProgress(100)
              }
              return submitResponse
            } catch (formParseError) {
              console.error('Failed to parse form data:', formParseError)
              throw new Error('AI返回了表单配置但解析失败，请重试')
            }
          } else {
            console.warn('Form detected but could not extract form data')
            throw new Error('AI返回了表单配置但格式不正确，请重试')
          }
        }

        // Return the full content
        if (fullContent) {
          console.log('Returning full content:', fullContent.substring(0, 100) + '...')
          if (options.onProgress) {
            options.onProgress(100)
          }
          return fullContent
        }
        
        // Fallback to original response handling
        if (typeof responseText === 'string') {
          console.log('Returning raw response as fallback')
          if (options.onProgress) {
            options.onProgress(100)
          }
          return responseText
        }
        
        throw new Error('No valid response content found')
        // Success - break out of retry loop
        break
      } catch (error: any) {
        lastError = error
        console.error(`Attempt ${attempt}/${maxRetries} failed:`, error)
        console.error('Error response:', error.response?.data)
        console.error('Error status:', error.response?.status)
        
        if (attempt === maxRetries) {
          console.error('All retry attempts exhausted')
          throw new Error(
            error.response?.data?.message || 
            error.message || 
            `AI通信失败，已重试${maxRetries}次`
          )
        }
        
        // Wait before retry (exponential backoff)
        const waitTime = Math.min(RETRY_DELAY_BASE * Math.pow(2, attempt - 1), RETRY_DELAY_BASE * 5)
        console.log(`Waiting ${waitTime}ms before retry...`)
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }
    
    // This should never be reached, but just in case
    throw lastError || new Error('Unknown error occurred')
  }

  // Submit form data to AI
  private static async submitForm(_formConfig: any, formData: Record<string, any>): Promise<string> {
    try {
      console.log('Submitting form with data:', formData)
      
      // Send the form submission message
      const response = await chatClient.post(`/application/chat_message/${this.chatId!}`, {
        message: '提交表单',
        re_chat: false,
        form_data: formData,
        image_list: [],
        document_list: [],
        audio_list: []
      }, {
        timeout: 120000,
        responseType: 'text' // Get raw text response to handle SSE
      })
      
      console.log('Form submission response status:', response.status)
      console.log('Form submission response type:', typeof response.data)
      
      // Handle streaming response (SSE format)
      const responseText = response.data
      console.log('Form submission raw response length:', responseText.length)
      
      // Parse SSE format - extract all data chunks
      const dataChunks = responseText.split('\n').filter((line: string) => line.startsWith('data: '))
      console.log('Form submission data chunks:', dataChunks.length)
      
      let fullContent = ''
      
      for (const chunk of dataChunks) {
        try {
          const jsonData = JSON.parse(chunk.substring(6)) // Remove 'data: ' prefix
          
          if (jsonData.content) {
            fullContent += jsonData.content
          }
        } catch (parseError) {
          console.warn('Failed to parse JSON chunk in form submission:', chunk.substring(6))
        }
      }
      
      console.log('Form submission total content length:', fullContent.length)

      // Check if form submission response contains error information
      if (fullContent.includes('Exception:') ||
          fullContent.includes('Error:') ||
          fullContent.includes('undefined')) {
        console.error('Form submission response contains error:', fullContent.substring(0, 500))
        throw new Error('表单提交失败，AI服务返回错误信息')
      }

      if (fullContent) {
        console.log('Form submission content preview:', fullContent.substring(0, 100) + '...')
        return fullContent
      }

      // Fallback to raw response
      if (typeof responseText === 'string') {
        // Check raw response for errors too
        if (responseText.includes('Exception:') ||
            responseText.includes('Error:') ||
            responseText.includes('undefined')) {
          throw new Error('表单提交失败，服务返回错误信息')
        }
        return responseText
      }

      throw new Error('No valid form submission response found')
    } catch (error: any) {
      console.error('Error submitting form:', error)
      console.error('Error response:', error.response?.data)
      console.error('Error status:', error.response?.status)
      console.error('Error config:', error.config)
      throw new Error(error.response?.data?.message || error.message || 'Failed to submit form')
    }
  }

  static async resetChat(): Promise<void> {
    this.profileId = null
    this.chatId = null
  }
}

export default ChatService