var R=Object.defineProperty;var D=(c,e,t)=>e in c?R(c,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):c[e]=t;var E=(c,e,t)=>D(c,typeof e!="symbol"?e+"":e,t);var I=(c,e,t)=>new Promise((w,a)=>{var f=s=>{try{o(t.next(s))}catch(i){a(i)}},d=s=>{try{o(t.throw(s))}catch(i){a(i)}},o=s=>s.done?w(s.value):Promise.resolve(s.value).then(f,d);o((t=t.apply(c,e)).next())});import{a as k}from"./utils-DxgFcSvi.js";const x="http://ai.procaas.com:3000/api",C="application-309a58f2282306bc0accf8dd6115a852",$="dbd9b112-66e2-11f0-bbb8-00163e4f3d7b",O=9e4,F=3,A=1e3,b=k.create({baseURL:x,timeout:6e4,headers:{"Content-Type":"application/json",accept:"application/json",AUTHORIZATION:C}});class P{static getProfileId(){return I(this,null,function*(){try{const e=yield b.get(`/application/${$}`);if(e.data.code===200)return this.profileId=e.data.data.id,this.profileId;throw new Error("Failed to get profile ID")}catch(e){throw console.error("Error getting profile ID:",e),e}})}static getChatId(){return I(this,null,function*(){this.profileId||(this.profileId=yield this.getProfileId());try{const e=yield b.get(`/application/${this.profileId}/chat/open`);if(e.data.code===200)return this.chatId=e.data.data,this.chatId;throw new Error("Failed to get chat ID")}catch(e){throw console.error("Error getting chat ID:",e),e}})}static sendMessage(w){return I(this,arguments,function*(e,t={}){var o,s,i,l;const a=Math.min(t.maxRetries||F,F),f=t.timeout||O;let d=null;for(let n=1;n<=a;n++)try{t.onProgress&&t.onProgress((n-1)*(100/a)),this.chatId||(console.log("Getting new chat ID..."),this.chatId=yield this.getChatId(),console.log("Chat ID obtained:",this.chatId));const r={message:e,re_chat:t.re_chat||!1,form_data:t.form_data||{},image_list:t.image_list||[],document_list:[],audio_list:[]};console.log("Sending message to AI:",{chatId:this.chatId,message:r.message,hasFormData:Object.keys(r.form_data||{}).length>0,formData:r.form_data,attempt:`${n}/${a}`});const m=yield b.post(`/application/chat_message/${this.chatId}`,r,{timeout:f,responseType:"text"});t.onProgress&&t.onProgress(50),console.log("AI Response status:",m.status),console.log("AI Response data type:",typeof m.data);const u=m.data;console.log("Raw response length:",u.length);const y=u.split(`
`).filter(h=>h.startsWith("data: "));console.log("Found data chunks:",y.length);let p="",_=!1;for(const h of y)try{const g=JSON.parse(h.substring(6));g.content&&(p+=g.content,g.content.includes("form_rander")&&(_=!0))}catch(g){console.warn("Failed to parse JSON chunk:",h.substring(6))}if(console.log("Total content length:",p.length),console.log("Form found:",_),_){console.log("Detected form response, extracting form data...");const h=p.match(new RegExp("<form_rander>(.*?)<\\/form_rander>","s"));if(h){const g=JSON.parse(h[1]);console.log("Extracted form data:",g);const T=yield this.submitForm(g,t.form_data||{});return t.onProgress&&t.onProgress(100),T}}if(p)return console.log("Returning full content:",p.substring(0,100)+"..."),t.onProgress&&t.onProgress(100),p;if(typeof u=="string")return console.log("Returning raw response as fallback"),t.onProgress&&t.onProgress(100),u;throw new Error("No valid response content found")}catch(r){if(d=r,console.error(`Attempt ${n}/${a} failed:`,r),console.error("Error response:",(o=r.response)==null?void 0:o.data),console.error("Error status:",(s=r.response)==null?void 0:s.status),n===a)throw console.error("All retry attempts exhausted"),new Error(((l=(i=r.response)==null?void 0:i.data)==null?void 0:l.message)||r.message||`AI通信失败，已重试${a}次`);const m=Math.min(A*Math.pow(2,n-1),A*5);console.log(`Waiting ${m}ms before retry...`),yield new Promise(u=>setTimeout(u,m))}throw d||new Error("Unknown error occurred")})}static submitForm(e,t){return I(this,null,function*(){var w,a,f,d;try{console.log("Submitting form with data:",t);const o=yield b.post(`/application/chat_message/${this.chatId}`,{message:"提交表单",re_chat:!1,form_data:t,image_list:[],document_list:[],audio_list:[]},{timeout:12e4,responseType:"text"});console.log("Form submission response status:",o.status),console.log("Form submission response type:",typeof o.data);const s=o.data;console.log("Form submission raw response length:",s.length);const i=s.split(`
`).filter(n=>n.startsWith("data: "));console.log("Form submission data chunks:",i.length);let l="";for(const n of i)try{const r=JSON.parse(n.substring(6));r.content&&(l+=r.content)}catch(r){console.warn("Failed to parse JSON chunk in form submission:",n.substring(6))}if(console.log("Form submission total content length:",l.length),l)return console.log("Form submission content preview:",l.substring(0,100)+"..."),l;if(typeof s=="string")return s;throw new Error("No valid form submission response found")}catch(o){throw console.error("Error submitting form:",o),console.error("Error response:",(w=o.response)==null?void 0:w.data),console.error("Error status:",(a=o.response)==null?void 0:a.status),console.error("Error config:",o.config),new Error(((d=(f=o.response)==null?void 0:f.data)==null?void 0:d.message)||o.message||"Failed to submit form")}})}static resetChat(){return I(this,null,function*(){this.profileId=null,this.chatId=null})}}E(P,"profileId",null),E(P,"chatId",null);export{P as ChatService,P as default};
