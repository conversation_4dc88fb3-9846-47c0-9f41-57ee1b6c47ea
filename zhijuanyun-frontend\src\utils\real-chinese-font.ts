// 真实的中文字体支持
// 使用在线字体服务或本地字体文件

export class RealChineseFontSupport {
  private static fontLoaded = false
  private static fontName = 'SimSun'
  
  // 加载真实的中文字体
  static async loadChineseFont(pdf: any): Promise<boolean> {
    if (this.fontLoaded) return true
    
    try {
      // 方案1: 尝试从CDN加载思源黑体
      const fontUrl = 'https://fonts.gstatic.com/s/notosanssc/v36/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnYxNbPzS5HE.woff2'
      
      const response = await fetch(fontUrl)
      if (response.ok) {
        const fontArrayBuffer = await response.arrayBuffer()
        const fontBase64 = this.arrayBufferToBase64(fontArrayBuffer)
        
        // 添加字体到jsPDF
        pdf.addFileToVFS('NotoSansSC.woff2', fontBase64)
        pdf.addFont('NotoSansSC.woff2', 'NotoSansSC', 'normal')
        
        this.fontName = 'NotoSansSC'
        this.fontLoaded = true
        console.log('Chinese font loaded from CDN successfully')
        return true
      }
    } catch (error) {
      console.warn('Failed to load font from CDN:', error)
    }
    
    // 方案2: 使用系统字体（如果可用）
    try {
      // 检查系统是否有中文字体
      if (this.isChineseFontAvailable()) {
        // 使用系统默认中文字体
        this.fontName = 'SimSun'
        this.fontLoaded = true
        console.log('Using system Chinese font')
        return true
      }
    } catch (error) {
      console.warn('System Chinese font not available:', error)
    }
    
    return false
  }
  
  // 检查系统是否有中文字体
  private static isChineseFontAvailable(): boolean {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    if (!ctx) return false
    
    // 测试中文字符渲染
    ctx.font = '16px SimSun'
    const width1 = ctx.measureText('测试').width
    
    ctx.font = '16px Arial'
    const width2 = ctx.measureText('测试').width
    
    // 如果宽度不同，说明有中文字体
    return Math.abs(width1 - width2) > 1
  }
  
  // 将ArrayBuffer转换为Base64
  private static arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }
  
  // 渲染中文文本
  static renderChineseText(pdf: any, text: string, x: number, y: number, options: any = {}): void {
    try {
      if (this.fontLoaded && this.containsChinese(text)) {
        pdf.setFont(this.fontName)
        pdf.text(text, x, y, options)
      } else {
        // Fallback到默认字体
        pdf.setFont('helvetica')
        pdf.text(text, x, y, options)
      }
    } catch (error) {
      console.warn('Failed to render Chinese text:', error)
      // 最终fallback
      pdf.setFont('helvetica')
      pdf.text(text, x, y, options)
    }
  }
  
  // 检查文本是否包含中文
  private static containsChinese(text: string): boolean {
    return /[\u4e00-\u9fff]/.test(text)
  }
  
  static getFontName(): string {
    return this.fontName
  }
  
  static isFontLoaded(): boolean {
    return this.fontLoaded
  }
  
  static reset(): void {
    this.fontLoaded = false
  }
}

// 简单但有效的中文PDF导出解决方案
export class SimplifiedChineseExport {
  // 创建支持中文的PDF
  static async createChinesePDF(content: any): Promise<any> {
    const jsPDF = (await import('jspdf')).default
    const pdf = new jsPDF('p', 'mm', 'a4')
    
    // 尝试加载中文字体
    await RealChineseFontSupport.loadChineseFont(pdf)
    
    return pdf
  }
  
  // 添加中文文本到PDF
  static addChineseText(pdf: any, text: string, x: number, y: number, options: any = {}): void {
    RealChineseFontSupport.renderChineseText(pdf, text, x, y, options)
  }
  
  // 处理长文本的换行
  static addChineseTextWithWrap(pdf: any, text: string, x: number, y: number, maxWidth: number, lineHeight: number = 6): number {
    const lines = this.wrapChineseText(pdf, text, maxWidth)
    let currentY = y
    
    lines.forEach(line => {
      this.addChineseText(pdf, line, x, currentY)
      currentY += lineHeight
    })
    
    return currentY
  }
  
  // 中文文本换行处理
  private static wrapChineseText(pdf: any, text: string, maxWidth: number): string[] {
    const lines: string[] = []
    let currentLine = ''
    
    // 按字符分割（对中文更友好）
    for (const char of text) {
      const testLine = currentLine + char
      const testWidth = pdf.getTextWidth(testLine)
      
      if (testWidth > maxWidth && currentLine !== '') {
        lines.push(currentLine)
        currentLine = char
      } else {
        currentLine = testLine
      }
    }
    
    if (currentLine) {
      lines.push(currentLine)
    }
    
    return lines
  }
}

// 导出工具函数
export const chineseExportUtils = {
  // 快速创建中文PDF
  async createPDF(): Promise<any> {
    return await SimplifiedChineseExport.createChinesePDF({})
  },
  
  // 添加文本
  addText(pdf: any, text: string, x: number, y: number, options?: any): void {
    SimplifiedChineseExport.addChineseText(pdf, text, x, y, options)
  },
  
  // 添加带换行的文本
  addTextWithWrap(pdf: any, text: string, x: number, y: number, maxWidth: number, lineHeight?: number): number {
    return SimplifiedChineseExport.addChineseTextWithWrap(pdf, text, x, y, maxWidth, lineHeight)
  },
  
  // 检查字体是否已加载
  isFontReady(): boolean {
    return RealChineseFontSupport.isFontLoaded()
  }
}
