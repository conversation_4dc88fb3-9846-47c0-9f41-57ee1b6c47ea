// API client configuration and base service

import axios, { type AxiosInstance, type AxiosResponse } from 'axios'
import type { ApiResponse, AppError } from '@/types'
import { ElMessage } from 'element-plus'

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://ai.procaas.com:3000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'AUTHORIZATION': 'application-fbfca452daccc7a3367a91ea4af22d29'
  }
})

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // For development, return mock data structure
    if (response.config.url?.includes('/questions/search')) {
      // Return response.data directly since our API client expects it
      return response
    }
    
    const { data } = response
    if (data && data.code === 200) {
      // Return the actual data, not the wrapped response
      response.data = data.data
      return response
    } else if (!data.code) {
      // Direct data without wrapper (development mode)
      return response
    } else {
      const error: AppError = {
        type: 'NETWORK_ERROR',
        message: data.message || 'API Error',
        details: data
      }
      ElMessage.error(error.message)
      return Promise.reject(error)
    }
  },
  (error) => {
    let appError: AppError
    
    if (error.response) {
      // Handle HTTP errors
      const status = error.response.status
      switch (status) {
        case 401:
          localStorage.removeItem('token')
          window.location.href = '/login'
          appError = {
            type: 'NETWORK_ERROR',
            message: '请先登录',
            details: error
          }
          break
        case 403:
          appError = {
            type: 'NETWORK_ERROR',
            message: '权限不足',
            details: error
          }
          break
        case 404:
          appError = {
            type: 'NETWORK_ERROR',
            message: '请求的资源不存在',
            details: error
          }
          break
        case 500:
          appError = {
            type: 'NETWORK_ERROR',
            message: '服务器内部错误',
            details: error
          }
          break
        default:
          appError = {
            type: 'NETWORK_ERROR',
            message: `请求失败 (${status})`,
            details: error
          }
      }
    } else if (error.request) {
      appError = {
        type: 'NETWORK_ERROR',
        message: '网络连接失败，请检查网络连接',
        details: error
      }
    } else {
      appError = {
        type: 'NETWORK_ERROR',
        message: '请求配置错误',
        details: error
      }
    }
    
    ElMessage.error(appError.message)
    return Promise.reject(appError)
  }
)

// Upload helper for file uploads
export const uploadFile = (file: File, onProgress?: (progress: number) => void) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return apiClient.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (progressEvent.total && onProgress) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

export default apiClient