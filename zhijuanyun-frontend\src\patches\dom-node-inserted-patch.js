// Patch for DOMNodeInserted deprecation warning in Quill
// This silences the warning by intercepting the deprecated event

(function() {
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    if (type === 'DOMNodeInserted') {
      // Silently ignore DOMNodeInserted events to prevent deprecation warnings
      // Quill uses this for DOM change detection, but it's not critical for functionality
      return;
    }
    
    return originalAddEventListener.call(this, type, listener, options);
  };
  
  // Also patch removeEventListener to handle DOMNodeInserted
  const originalRemoveEventListener = EventTarget.prototype.removeEventListener;
  
  EventTarget.prototype.removeEventListener = function(type, listener, options) {
    if (type === 'DOMNodeInserted') {
      return;
    }
    
    return originalRemoveEventListener.call(this, type, listener, options);
  };
})();