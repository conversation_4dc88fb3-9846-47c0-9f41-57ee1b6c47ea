# 答题卡导出功能说明

## 🎯 功能概述

答题卡导出功能已成功实现，支持将试卷导出为标准的答题卡格式，完美支持中文字符显示。

## ✨ 主要特性

### 📋 答题卡布局
- **标准答题卡格式**：符合考试规范的专业答题卡布局
- **考生信息区**：包含姓名、学号、班级、考场等信息填写框
- **选择题答题区**：标准涂卡圆圈，支持A、B、C、D等选项
- **非选择题答题区**：根据题型和分值自动生成答题线条

### 🔤 中文支持
- **完美中文显示**：所有中文字符正确显示，无乱码问题
- **多种题型支持**：单选题、多选题、判断题、填空题、简答题、计算题等
- **智能布局**：自动识别题型并分配合适的答题空间

### 🎨 自动化功能
- **智能题型分组**：自动将选择题和非选择题分别处理
- **动态答题空间**：根据题目分值自动调整答题行数
- **标准化格式**：统一的答题卡样式和规范

## 🚀 使用方法

### 1. 在导出设置中启用答题卡模式
```typescript
const exportSettings = {
  title: '期末考试试卷',
  answerSheet: true,  // 启用答题卡模式
  duration: 120,
  includes: ['questions']
}
```

### 2. 调用导出服务
```typescript
import { ExportService } from '@/services/export'

const exportOptions = {
  questions: questions,
  settings: exportSettings,
  totalScore: totalScore
}

const blob = await ExportService.exportToPDF(exportOptions)
```

### 3. 在Vue组件中使用
```vue
<template>
  <el-switch
    v-model="exportForm.answerSheet"
    active-text="生成答题卡"
    inactive-text="答案在试卷上"
  />
</template>
```

## 🧪 测试方法

### 方法1：独立测试页面
打开 `test-answer-sheet-export.html`，点击测试按钮：
- **"测试答题卡导出"** - 基础答题卡测试
- **"测试混合题型答题卡"** - 多种题型答题卡测试
- **"对比两种模式"** - 普通试卷vs答题卡对比

### 方法2：Vue测试页面
访问 `/test-export` 路由，使用以下功能：
- **"测试答题卡导出"** - Vue组件中的答题卡测试
- **"混合题型答题卡"** - 包含选择题、填空题、简答题等
- **"对比两种模式"** - 同时生成两种格式进行对比

### 方法3：在导出页面中测试
1. 访问 `/export` 页面
2. 导入或创建试卷
3. 在导出设置中开启"答题卡模式"
4. 点击导出PDF

## 📊 支持的题型

### 选择题类型（涂卡区域）
- **单选题** (`single_choice`) - 标准A、B、C、D选项圆圈
- **多选题** (`multiple_choice`) - 支持多个选项的圆圈
- **判断题** (`true_false`) - 正确/错误选项圆圈

### 非选择题类型（答题线条）
- **填空题** (`fill_blank`) - 2-4行答题空间
- **简答题** (`short_answer`) - 4-8行答题空间
- **论述题** (`essay`) - 8-15行答题空间
- **计算题** (`calculation`) - 6-12行答题空间
- **证明题** (`proof`) - 8-15行答题空间
- **分析题** (`analysis`) - 6-10行答题空间

## 🎨 答题卡样式特点

### 头部区域
- 试卷标题居中显示
- 考试信息（总分、时间、题目数）
- 专业的答题卡标识

### 考生信息区
- 姓名填写框（6个字符位）
- 学号填写框（10个字符位）
- 班级填写框（8个字符位）
- 考场填写框（6个字符位）

### 填涂说明
- 详细的填涂规范说明
- 正确和错误填涂示例
- 考试注意事项

### 答题区域
- **选择题区域**：网格布局，每行5题，清晰的题号和选项圆圈
- **非选择题区域**：按题目分组，包含题号、分值、题型标识和答题线条

## 🔧 技术实现

### 核心技术栈
- **HTML渲染**：使用HTML模板生成答题卡布局
- **html2canvas**：将HTML转换为高质量图片
- **jsPDF**：将图片嵌入PDF文档
- **CSS Grid/Flexbox**：实现响应式答题卡布局

### 关键算法
- **题型识别**：自动区分选择题和非选择题
- **空间分配**：根据题目分值计算答题行数
- **布局优化**：确保答题卡在A4纸张上的最佳显示效果

## 📝 配置选项

### 答题卡专用设置
```typescript
interface AnswerSheetOptions {
  answerSheet: boolean        // 启用答题卡模式
  title: string              // 试卷标题
  duration: number           // 考试时长
  watermark?: {              // 水印设置
    enabled: boolean
    text: string
  }
}
```

### 题型配置
```typescript
interface QuestionType {
  questionType: 'single_choice' | 'multiple_choice' | 'true_false' | 
                'fill_blank' | 'short_answer' | 'essay' | 
                'calculation' | 'proof' | 'analysis'
  score: number              // 题目分值（影响答题空间大小）
}
```

## 🎯 预期效果

### 成功导出的答题卡应包含：
1. ✅ 清晰的中文标题和说明
2. ✅ 标准的考生信息填写区域
3. ✅ 规整的选择题涂卡圆圈
4. ✅ 充足的非选择题答题线条
5. ✅ 专业的答题卡格式和布局
6. ✅ 所有中文字符正确显示

### 与普通试卷的区别：
- **普通试卷**：题目和答案在同一页面，适合练习和复习
- **答题卡**：只有答题区域，需要配合单独的试题册使用，适合正式考试

## 🚨 注意事项

1. **浏览器兼容性**：需要支持html2canvas的现代浏览器
2. **题目数量**：建议单页答题卡不超过50道选择题
3. **答题空间**：非选择题会根据分值自动调整行数，高分题目占用更多空间
4. **打印设置**：建议使用A4纸张，100%缩放比例打印

## 🎉 总结

答题卡导出功能现已完全实现，支持：
- ✅ 完美的中文字符显示
- ✅ 标准的答题卡格式
- ✅ 多种题型的智能处理
- ✅ 专业的考试规范布局
- ✅ 灵活的配置选项

用户现在可以轻松生成符合考试标准的中文答题卡，解决了之前无法导出答题卡的问题！
