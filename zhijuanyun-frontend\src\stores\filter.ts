// Filter store for question filtering functionality

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { FilterParams, Question } from '@/types'
import { QuestionService } from '@/services/question'

export const useFilterStore = defineStore('filter', () => {
  // State
  const filterParams = ref<FilterParams>({
    grade: '',
    subject: '',
    knowledgePoints: [],
    questionTypes: [],
    difficulty: [],
    region: '',
    questionCount: 10,
    sampleFile: undefined
  })

  const questions = ref<Question[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const progress = ref(0)

  // Getters
  const hasQuestions = computed(() => questions.value.length > 0)
  const questionCount = computed(() => questions.value.length)

  // Actions
  const updateFilterParams = (params: Partial<FilterParams>) => {
    filterParams.value = { ...filterParams.value, ...params }
  }

  const fetchQuestions = async (params?: FilterParams) => {
    try {
      loading.value = true
      error.value = null
      
      const searchParams = params || filterParams.value
      const result = await QuestionService.searchQuestions(searchParams)
      
      questions.value = result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch questions'
      questions.value = []
    } finally {
      loading.value = false
    }
  }

  // AI智能组卷功能
  const generatePaperWithAI = async (params?: FilterParams) => {
    try {
      loading.value = true
      error.value = null
      progress.value = 0
      
      const generateParams = params || filterParams.value
      const result = await QuestionService.generatePaper({
        filterParams: generateParams,
        count: generateParams.questionCount
      }, (progressValue) => {
        progress.value = progressValue
      })
      
      questions.value = result
      progress.value = 100
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'AI组卷失败'
      questions.value = []
    } finally {
      loading.value = false
      setTimeout(() => {
        progress.value = 0
      }, 1000)
    }
  }

  const clearQuestions = () => {
    questions.value = []
    error.value = null
  }

  const resetFilter = () => {
    filterParams.value = {
      grade: '',
      subject: '',
      knowledgePoints: [],
      questionTypes: [],
      difficulty: [],
      region: '',
      questionCount: 10,
      sampleFile: undefined
    }
    clearQuestions()
  }

  return {
    // State
    filterParams,
    questions,
    loading,
    error,
    progress,
    
    // Getters
    hasQuestions,
    questionCount,
    
    // Actions
    updateFilterParams,
    fetchQuestions,
    generatePaperWithAI,
    clearQuestions,
    resetFilter
  }
})