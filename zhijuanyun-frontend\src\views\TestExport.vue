<template>
  <div class="test-export">
    <h1>中文导出测试页面</h1>
    
    <div class="test-controls">
      <el-button type="primary" @click="testPDFExport" :loading="exporting">
        测试PDF导出
      </el-button>

      <el-button type="success" @click="testDOCXExport" :loading="exporting">
        测试DOCX导出
      </el-button>

      <el-button @click="testQuickPDF" :loading="exporting">
        快速PDF测试
      </el-button>

      <el-button type="warning" @click="testAnswerSheet" :loading="exporting">
        测试答题卡导出
      </el-button>

      <el-button type="info" @click="testMixedAnswerSheet" :loading="exporting">
        混合题型答题卡
      </el-button>

      <el-button @click="compareExportModes" :loading="exporting">
        对比两种模式
      </el-button>
    </div>
    
    <div class="test-results" v-if="testResults.length > 0">
      <h3>测试结果：</h3>
      <ul>
        <li v-for="(result, index) in testResults" :key="index" :class="result.type">
          {{ result.message }}
        </li>
      </ul>
    </div>
    
    <div class="test-data">
      <h3>测试数据预览：</h3>
      <pre>{{ JSON.stringify(testData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { ExportService } from '@/services/export'
import { chineseExportUtils } from '@/utils/real-chinese-font'
import type { Question, ExportSettings } from '@/types'

const exporting = ref(false)
const testResults = ref<Array<{ type: string; message: string }>>([])

// 测试数据
const testData = {
  questions: [
    {
      id: '1',
      content: {
        stem: '下列关于计算机网络的描述，正确的是？',
        options: [
          '计算机网络只能连接同一地区的计算机',
          '互联网是世界上最大的计算机网络',
          '局域网不能连接到互联网',
          '计算机网络只能传输文字信息'
        ],
        answer: 'B',
        explanation: '互联网（Internet）是全球最大的计算机网络，连接了世界各地的计算机和网络设备。'
      },
      tags: {
        subject: '计算机基础',
        chapter: '计算机网络',
        difficulty: 'easy',
        questionType: 'single-choice'
      },
      score: 5,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: '2',
      content: {
        stem: '请简述操作系统的主要功能。',
        answer: '操作系统的主要功能包括：1. 进程管理；2. 内存管理；3. 文件系统管理；4. 设备管理；5. 用户界面管理。',
        explanation: '操作系统是计算机系统的核心软件，负责管理和协调计算机硬件与软件资源。'
      },
      tags: {
        subject: '计算机基础',
        chapter: '操作系统',
        difficulty: 'medium',
        questionType: 'short-answer'
      },
      score: 10,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ] as Question[],
  
  settings: {
    title: '计算机基础知识测试卷',
    filename: '中文测试',
    format: 'pdf',
    includes: ['answers', 'explanations'],
    duration: 120,
    watermark: {
      enabled: true,
      text: '智卷云测试'
    }
  } as ExportSettings,
  
  totalScore: 15
}

// 添加测试结果
const addResult = (type: string, message: string) => {
  testResults.value.push({ type, message })
  console.log(`[${type}] ${message}`)
}

// 测试PDF导出
const testPDFExport = async () => {
  exporting.value = true
  addResult('info', '开始测试PDF导出...')
  
  try {
    const blob = await ExportService.exportToPDF({
      questions: testData.questions,
      settings: testData.settings,
      totalScore: testData.totalScore,
      onProgress: (progress) => {
        addResult('progress', `导出进度: ${progress}%`)
      }
    })
    
    addResult('success', `PDF导出成功，文件大小: ${blob.size} bytes`)
    
    // 下载文件
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = '中文测试.pdf'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('PDF导出成功')
    
  } catch (error) {
    addResult('error', `PDF导出失败: ${error}`)
    ElMessage.error('PDF导出失败')
  } finally {
    exporting.value = false
  }
}

// 测试DOCX导出
const testDOCXExport = async () => {
  exporting.value = true
  addResult('info', '开始测试DOCX导出...')
  
  try {
    const settings = { ...testData.settings, format: 'word' as const }
    
    const blob = await ExportService.exportToDOCX({
      questions: testData.questions,
      settings,
      totalScore: testData.totalScore,
      onProgress: (progress) => {
        addResult('progress', `导出进度: ${progress}%`)
      }
    })
    
    addResult('success', `DOCX导出成功，文件大小: ${blob.size} bytes`)
    
    // 下载文件
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = '中文测试.docx'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('DOCX导出成功')
    
  } catch (error) {
    addResult('error', `DOCX导出失败: ${error}`)
    ElMessage.error('DOCX导出失败')
  } finally {
    exporting.value = false
  }
}

// 快速PDF测试
const testQuickPDF = async () => {
  exporting.value = true
  addResult('info', '开始快速PDF测试...')
  
  try {
    // 使用新的中文字体工具
    const pdf = await chineseExportUtils.createPDF()
    
    // 添加测试文本
    chineseExportUtils.addText(pdf, '这是一个中文测试', 20, 20)
    chineseExportUtils.addText(pdf, '测试题目：计算机基础知识', 20, 40)
    chineseExportUtils.addText(pdf, '答案：正确', 20, 60)
    chineseExportUtils.addText(pdf, '解析：这是一个测试解析', 20, 80)
    
    const blob = pdf.output('blob')
    addResult('success', `快速PDF测试成功，文件大小: ${blob.size} bytes`)
    
    // 下载文件
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = '快速中文测试.pdf'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('快速PDF测试成功')
    
  } catch (error) {
    addResult('error', `快速PDF测试失败: ${error}`)
    ElMessage.error('快速PDF测试失败')
  } finally {
    exporting.value = false
  }
}

// 测试答题卡导出
const testAnswerSheet = async () => {
  exporting.value = true
  addResult('info', '开始答题卡导出测试...')

  try {
    // 创建答题卡设置
    const answerSheetSettings = {
      ...testData.settings,
      answerSheet: true,
      filename: '中文答题卡测试'
    }

    const totalScore = testData.questions.reduce((sum, q) => sum + q.score, 0)

    const exportOptions = {
      questions: testData.questions,
      settings: answerSheetSettings,
      totalScore: totalScore,
      onProgress: (progress: number) => {
        addResult('progress', `答题卡导出进度: ${progress}%`)
      }
    }

    const blob = await ExportService.exportToPDF(exportOptions)
    addResult('success', `答题卡导出成功，文件大小: ${blob.size} bytes`)

    // 下载文件
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = '中文答题卡测试.pdf'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('答题卡导出成功！请检查PDF中的中文字符显示')

  } catch (error) {
    addResult('error', `答题卡导出失败: ${error}`)
    ElMessage.error('答题卡导出失败')
  } finally {
    exporting.value = false
  }
}

// 测试混合题型答题卡
const testMixedAnswerSheet = async () => {
  exporting.value = true
  addResult('info', '开始混合题型答题卡测试...')

  try {
    // 创建包含多种题型的测试数据
    const mixedQuestions = [
      ...testData.questions,
      {
        id: '3',
        content: {
          stem: '判断：计算机只能处理数字信息。',
          answer: '错误',
          explanation: '计算机可以处理文字、图像、声音等多种类型的信息。'
        },
        tags: {
          subject: '计算机基础',
          questionType: 'true_false',
          difficulty: 'easy'
        },
        score: 3
      },
      {
        id: '4',
        content: {
          stem: '请填空：CPU的中文全称是_____。',
          answer: '中央处理器',
          explanation: 'CPU是Central Processing Unit的缩写，中文名称是中央处理器。'
        },
        tags: {
          subject: '计算机基础',
          questionType: 'fill_blank',
          difficulty: 'easy'
        },
        score: 4
      },
      {
        id: '5',
        content: {
          stem: '计算题：如果一个硬盘的容量是1TB，那么它等于多少GB？',
          answer: '1024GB',
          explanation: '1TB = 1024GB，这是计算机存储单位的换算。'
        },
        tags: {
          subject: '计算机基础',
          questionType: 'calculation',
          difficulty: 'medium'
        },
        score: 8
      }
    ]

    const answerSheetSettings = {
      ...testData.settings,
      answerSheet: true,
      title: '混合题型测试答题卡',
      filename: '混合题型答题卡测试'
    }

    const totalScore = mixedQuestions.reduce((sum, q) => sum + q.score, 0)

    const exportOptions = {
      questions: mixedQuestions,
      settings: answerSheetSettings,
      totalScore: totalScore,
      onProgress: (progress: number) => {
        addResult('progress', `混合题型答题卡导出进度: ${progress}%`)
      }
    }

    const blob = await ExportService.exportToPDF(exportOptions)
    addResult('success', `混合题型答题卡导出成功，包含${mixedQuestions.length}道题，文件大小: ${blob.size} bytes`)

    // 下载文件
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = '混合题型答题卡测试.pdf'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('混合题型答题卡导出成功！包含选择题、判断题、填空题、计算题等')

  } catch (error) {
    addResult('error', `混合题型答题卡导出失败: ${error}`)
    ElMessage.error('混合题型答题卡导出失败')
  } finally {
    exporting.value = false
  }
}

// 对比两种导出模式
const compareExportModes = async () => {
  exporting.value = true
  addResult('info', '开始对比两种导出模式...')

  try {
    const totalScore = testData.questions.reduce((sum, q) => sum + q.score, 0)

    // 导出普通试卷
    addResult('info', '正在生成普通试卷...')
    const normalSettings = {
      ...testData.settings,
      answerSheet: false,
      filename: '对比测试-普通试卷'
    }

    const normalOptions = {
      questions: testData.questions,
      settings: normalSettings,
      totalScore: totalScore
    }

    const normalBlob = await ExportService.exportToPDF(normalOptions)

    // 下载普通试卷
    const normalUrl = URL.createObjectURL(normalBlob)
    const normalA = document.createElement('a')
    normalA.href = normalUrl
    normalA.download = '对比测试-普通试卷.pdf'
    document.body.appendChild(normalA)
    normalA.click()
    document.body.removeChild(normalA)
    URL.revokeObjectURL(normalUrl)

    addResult('success', `普通试卷导出成功，文件大小: ${normalBlob.size} bytes`)

    // 导出答题卡
    addResult('info', '正在生成答题卡...')
    const answerSheetSettings = {
      ...testData.settings,
      answerSheet: true,
      filename: '对比测试-答题卡'
    }

    const answerSheetOptions = {
      questions: testData.questions,
      settings: answerSheetSettings,
      totalScore: totalScore
    }

    const answerSheetBlob = await ExportService.exportToPDF(answerSheetOptions)

    // 下载答题卡
    const answerSheetUrl = URL.createObjectURL(answerSheetBlob)
    const answerSheetA = document.createElement('a')
    answerSheetA.href = answerSheetUrl
    answerSheetA.download = '对比测试-答题卡.pdf'
    document.body.appendChild(answerSheetA)
    answerSheetA.click()
    document.body.removeChild(answerSheetA)
    URL.revokeObjectURL(answerSheetUrl)

    addResult('success', `答题卡导出成功，文件大小: ${answerSheetBlob.size} bytes`)
    addResult('success', '两种模式对比测试完成！请查看下载的两个PDF文件的区别')

    ElMessage.success('对比测试完成！已下载普通试卷和答题卡两个PDF文件')

  } catch (error) {
    addResult('error', `对比测试失败: ${error}`)
    ElMessage.error('对比测试失败')
  } finally {
    exporting.value = false
  }
}
</script>

<style scoped>
.test-export {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-results {
  margin: 20px 0;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 5px;
}

.test-results ul {
  list-style: none;
  padding: 0;
}

.test-results li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.test-results li.success {
  color: #67c23a;
}

.test-results li.error {
  color: #f56c6c;
}

.test-results li.info {
  color: #409eff;
}

.test-results li.progress {
  color: #e6a23c;
}

.test-data {
  margin: 20px 0;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 5px;
}

.test-data pre {
  max-height: 300px;
  overflow-y: auto;
  font-size: 12px;
}
</style>
