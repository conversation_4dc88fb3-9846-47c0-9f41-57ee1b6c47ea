<template>
  <div class="subject-select">
    <el-form-item label="学科" required>
      <el-cascader
        v-model="modelValue"
        :options="subjectOptions"
        :props="cascaderProps"
        placeholder="请选择学科"
        clearable
        style="width: 100%"
        @change="handleChange"
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { CascaderOption, CascaderProps } from 'element-plus'

interface Props {
  modelValue?: string
  grade?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const cascaderProps: CascaderProps = {
  expandTrigger: 'hover',
  value: 'value',
  label: 'label',
  children: 'children'
}

const subjectOptions = ref<CascaderOption[]>([
  {
    label: '语文',
    value: 'chinese',
    children: [
      { label: '基础知识', value: 'chinese_basic' },
      { label: '阅读理解', value: 'chinese_reading' },
      { label: '作文', value: 'chinese_writing' }
    ]
  },
  {
    label: '数学',
    value: 'math',
    children: [
      { label: '代数', value: 'math_algebra' },
      { label: '几何', value: 'math_geometry' },
      { label: '概率统计', value: 'math_statistics' },
      { label: '函数', value: 'math_function' }
    ]
  },
  {
    label: '英语',
    value: 'english',
    children: [
      { label: '语法', value: 'english_grammar' },
      { label: '词汇', value: 'english_vocabulary' },
      { label: '阅读', value: 'english_reading' },
      { label: '写作', value: 'english_writing' }
    ]
  },
  {
    label: '物理',
    value: 'physics',
    children: [
      { label: '力学', value: 'physics_mechanics' },
      { label: '电学', value: 'physics_electricity' },
      { label: '光学', value: 'physics_optics' },
      { label: '热学', value: 'physics_thermodynamics' }
    ]
  },
  {
    label: '化学',
    value: 'chemistry',
    children: [
      { label: '无机化学', value: 'chemistry_inorganic' },
      { label: '有机化学', value: 'chemistry_organic' },
      { label: '物理化学', value: 'chemistry_physical' },
      { label: '分析化学', value: 'chemistry_analytical' }
    ]
  },
  {
    label: '生物',
    value: 'biology',
    children: [
      { label: '细胞生物学', value: 'biology_cell' },
      { label: '遗传学', value: 'biology_genetics' },
      { label: '生态学', value: 'biology_ecology' },
      { label: '分子生物学', value: 'biology_molecular' }
    ]
  }
])

const modelValue = computed({
  get: () => props.modelValue || '',
  set: (value: string) => emit('update:modelValue', value)
})

const handleChange = (value: string) => {
  emit('change', value)
  
  // Add haptic feedback on mobile
  if ('vibrate' in navigator) {
    navigator.vibrate(50)
  }
}
</script>

<style scoped>
.subject-select {
  margin-bottom: 16px;
}
</style>