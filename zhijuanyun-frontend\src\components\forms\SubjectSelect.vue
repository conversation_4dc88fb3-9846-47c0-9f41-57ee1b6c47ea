<template>
  <div class="subject-select">
    <el-form-item label="学科" required>
      <el-select
        v-model="modelValue"
        placeholder="请选择学科"
        clearable
        style="width: 100%"
        @change="handleChange"
      >
        <el-option
          v-for="subject in subjectOptions"
          :key="subject.value"
          :label="subject.label"
          :value="subject.value"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Option {
  label: string
  value: string
}

interface Props {
  modelValue?: string
  grade?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const subjectOptions = ref<Option[]>([
  { label: '语文', value: 'chinese' },
  { label: '数学', value: 'math' },
  { label: '英语', value: 'english' },
  { label: '物理', value: 'physics' },
  { label: '化学', value: 'chemistry' },
  { label: '生物', value: 'biology' },
  { label: '历史', value: 'history' },
  { label: '地理', value: 'geography' },
  { label: '政治', value: 'politics' }
])

const modelValue = computed({
  get: () => props.modelValue || '',
  set: (value: string) => emit('update:modelValue', value)
})

const handleChange = (value: string) => {
  emit('change', value)
  
  // Add haptic feedback on mobile
  if ('vibrate' in navigator) {
    navigator.vibrate(50)
  }
}
</script>

<style scoped>
.subject-select {
  margin-bottom: 16px;
}
</style>