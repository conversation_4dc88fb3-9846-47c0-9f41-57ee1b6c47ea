<template>
  <div class="question-editor">
    <el-form :model="questionData" ref="formRef" label-width="100px">
      <!-- Question Type Selection -->
      <el-form-item label="题目类型" required>
        <el-select 
          v-model="questionData.tags.questionType" 
          @change="handleTypeChange"
          style="width: 200px"
        >
          <el-option
            v-for="type in questionTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>
      </el-form-item>

      <!-- Question Stem -->
      <el-form-item label="题目内容" required>
        <div class="rich-editor">
          <QuillEditor
            v-model:content="questionData.content.stem"
            contentType="html"
            :options="quillOptions"
            style="min-height: 200px"
          />
        </div>
      </el-form-item>

      <!-- Options (for choice questions) -->
      <el-form-item 
        v-if="isChoiceQuestion" 
        label="选项设置" 
        required
      >
        <div class="options-editor">
          <div 
            v-for="(_, index) in questionData.content.options" 
            :key="index"
            class="option-item"
          >
            <div class="option-header">
              <span class="option-label">选项 {{ String.fromCharCode(65 + index) }}</span>
              <el-button 
                size="small" 
                type="danger" 
                text 
                @click="removeOption(index)"
                :disabled="questionData.content.options!.length <= 2"
              >
                删除
              </el-button>
            </div>
            <el-input
              v-model="questionData.content.options![index]"
              placeholder="请输入选项内容"
              type="textarea"
              :rows="2"
            />
          </div>
          
          <el-button 
            @click="addOption" 
            type="primary" 
            text
            :disabled="questionData.content.options!.length >= 6"
          >
            <el-icon><Plus /></el-icon>
            添加选项
          </el-button>
        </div>
      </el-form-item>

      <!-- Answer -->
      <el-form-item label="正确答案" required>
        <!-- Choice Question Answer -->
        <div v-if="isChoiceQuestion" class="choice-answer">
          <el-checkbox-group 
            v-if="questionData.tags.questionType === 'multiple_choice'"
            v-model="multipleAnswer"
            @change="handleMultipleAnswerChange"
          >
            <el-checkbox
              v-for="(_, index) in questionData.content.options"
              :key="index"
              :value="String.fromCharCode(65 + index)"
            >
              {{ String.fromCharCode(65 + index) }}
            </el-checkbox>
          </el-checkbox-group>
          
          <el-radio-group 
            v-else
            v-model="questionData.content.answer"
          >
            <el-radio
              v-for="(_, index) in questionData.content.options"
              :key="index"
              :value="String.fromCharCode(65 + index)"
            >
              {{ String.fromCharCode(65 + index) }}
            </el-radio>
          </el-radio-group>
        </div>
        
        <!-- Other Question Types Answer -->
        <el-input
          v-else
          v-model="questionData.content.answer"
          placeholder="请输入正确答案"
          type="textarea"
          :rows="3"
        />
      </el-form-item>

      <!-- Explanation -->
      <el-form-item label="答案解析">
        <div class="rich-editor">
          <QuillEditor
            v-model:content="questionData.content.explanation"
            contentType="html"
            :options="quillOptions"
            style="min-height: 150px"
            @update:content="debouncedEmitUpdate"
          />
        </div>
      </el-form-item>

      <!-- Question Settings -->
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="难度">
            <el-select v-model="questionData.tags.difficulty" style="width: 100%">
              <el-option label="容易" value="easy" />
              <el-option label="中等" value="medium" />
              <el-option label="困难" value="hard" />
              <el-option label="很难" value="very_hard" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分值">
            <el-input-number
              v-model="questionData.score"
              :min="1"
              :max="100"
              :step="1"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="学科">
            <el-input v-model="questionData.tags.subject" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- Knowledge Points -->
      <el-form-item label="知识点">
        <el-tag
          v-for="(point, index) in questionData.tags.knowledgePoint"
          :key="index"
          closable
          @close="removeKnowledgePoint(index)"
          style="margin-right: 8px; margin-bottom: 8px;"
        >
          {{ point }}
        </el-tag>
        
        <el-input
          v-if="knowledgePointInputVisible"
          ref="knowledgePointInputRef"
          v-model="knowledgePointInputValue"
          size="small"
          style="width: 150px"
          @keyup.enter="handleKnowledgePointConfirm"
          @blur="handleKnowledgePointConfirm"
        />
        
        <el-button
          v-else
          size="small"
          text
          @click="showKnowledgePointInput"
        >
          <el-icon><Plus /></el-icon>
          添加知识点
        </el-button>
      </el-form-item>

      <!-- Action Buttons -->
    </el-form>
    
    <!-- Action Buttons -->
    <div class="editor-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handlePreview">预览</el-button>
      <el-button type="danger" @click="handleDelete">删除</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        保存题目
      </el-button>
    </div>

    <!-- Preview Dialog -->
    <el-dialog
      v-model="previewVisible"
      title="题目预览"
      width="60%"
    >
      <div class="question-preview">
        <div class="preview-header">
          <h3>{{ getQuestionTypeLabel(questionData.tags.questionType) }} ({{ questionData.score }}分)</h3>
        </div>
        
        <div class="preview-content">
          <div class="preview-stem" v-html="questionData.content.stem"></div>
          
          <div v-if="questionData.content.options && questionData.content.options.length > 0" class="preview-options">
            <div 
              v-for="(option, index) in questionData.content.options" 
              :key="index"
              class="preview-option"
            >
              {{ String.fromCharCode(65 + index) }}. {{ option }}
            </div>
          </div>
          
          <div class="preview-answer">
            <strong>答案：</strong>{{ questionData.content.answer }}
          </div>
          
          <div v-if="questionData.content.explanation" class="preview-explanation">
            <strong>解析：</strong>
            <div v-html="questionData.content.explanation"></div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, ElInput } from 'element-plus'
import type { Question } from '@/types'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'

interface Props {
  question: Question
}

interface Emits {
  (e: 'update:question', question: Question): void
  (e: 'save'): void
  (e: 'cancel'): void
  (e: 'delete'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Refs
const formRef = ref<FormInstance>()
const knowledgePointInputRef = ref<InstanceType<typeof ElInput>>()

// State
const questionData = ref<Question>({ ...props.question })
const saving = ref(false)
const previewVisible = ref(false)
const knowledgePointInputVisible = ref(false)
const knowledgePointInputValue = ref('')
const multipleAnswer = ref<string[]>([])

// Question types
const questionTypes = [
  { label: '单选题', value: 'single_choice' },
  { label: '多选题', value: 'multiple_choice' },
  { label: '判断题', value: 'true_false' },
  { label: '填空题', value: 'fill_blank' },
  { label: '简答题', value: 'short_answer' },
  { label: '解答题', value: 'essay' },
  { label: '计算题', value: 'calculation' },
  { label: '应用题', value: 'application' },
  { label: '分析题', value: 'analysis' },
  { label: '综合题', value: 'comprehensive' }
]

const questionTypeLabels: Record<string, string> = {
  'single_choice': '单选题',
  'multiple_choice': '多选题',
  'true_false': '判断题',
  'fill_blank': '填空题',
  'short_answer': '简答题',
  'essay': '解答题',
  'calculation': '计算题',
  'application': '应用题',
  'analysis': '分析题',
  'comprehensive': '综合题'
}

// Quill editor options
const quillOptions = {
  theme: 'snow',
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'script': 'sub' }, { 'script': 'super' }],
      [{ 'header': [1, 2, 3, false] }],
      ['blockquote', 'code-block'],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],
      ['link', 'image', 'formula'],
      ['clean']
    ]
  },
  placeholder: '请输入内容...'
}

// Computed
const isChoiceQuestion = computed(() => {
  return ['single_choice', 'multiple_choice', 'true_false'].includes(questionData.value.tags.questionType)
})

const getQuestionTypeLabel = (type: string) => questionTypeLabels[type] || type

// Methods
const handleTypeChange = (newType: string) => {
  const oldType = questionData.value.tags.questionType
  questionData.value.tags.questionType = newType
  
  // Handle type-specific changes
  if (newType === 'true_false') {
    questionData.value.content.options = ['正确', '错误']
  } else if (['single_choice', 'multiple_choice'].includes(newType) && !questionData.value.content.options) {
    questionData.value.content.options = ['选项A', '选项B', '选项C', '选项D']
  } else if (!['single_choice', 'multiple_choice', 'true_false'].includes(newType)) {
    questionData.value.content.options = undefined
  }
  
  // Reset answer when changing types
  if (oldType !== newType) {
    questionData.value.content.answer = ''
    multipleAnswer.value = []
  }
  
  debouncedEmitUpdate()
}

const addOption = () => {
  if (!questionData.value.content.options) {
    questionData.value.content.options = []
  }
  const nextIndex = questionData.value.content.options.length
  questionData.value.content.options.push(`选项${String.fromCharCode(65 + nextIndex)}`)
  debouncedEmitUpdate()
}

const removeOption = (index: number) => {
  if (questionData.value.content.options && questionData.value.content.options.length > 2) {
    questionData.value.content.options.splice(index, 1)
    debouncedEmitUpdate()
  }
}

const handleMultipleAnswerChange = (values: string[]) => {
  questionData.value.content.answer = values.join(',')
  debouncedEmitUpdate()
}

const removeKnowledgePoint = (index: number) => {
  questionData.value.tags.knowledgePoint.splice(index, 1)
  debouncedEmitUpdate()
}

const showKnowledgePointInput = () => {
  knowledgePointInputVisible.value = true
  nextTick(() => {
    knowledgePointInputRef.value?.focus()
  })
}

const handleKnowledgePointConfirm = () => {
  const value = knowledgePointInputValue.value.trim()
  if (value && !questionData.value.tags.knowledgePoint.includes(value)) {
    questionData.value.tags.knowledgePoint.push(value)
    debouncedEmitUpdate()
  }
  knowledgePointInputVisible.value = false
  knowledgePointInputValue.value = ''
}

const emitUpdate = () => {
  emit('update:question', { ...questionData.value })
}

// 防抖更新函数
const debouncedEmitUpdate = () => {
  if (updateTimeout) {
    clearTimeout(updateTimeout)
  }
  updateTimeout = setTimeout(() => {
    emitUpdate()
  }, 300)
}

const handleSave = () => {
  console.log('Attempting to save question:', questionData.value)
  
  // Validate form
  if (!questionData.value.content.stem.trim() || questionData.value.content.stem.trim() === '请输入题目内容...') {
    ElMessage.warning('请输入题目内容')
    return
  }
  
  if (!questionData.value.content.answer.trim()) {
    ElMessage.warning('请输入正确答案')
    return
  }
  
  if (isChoiceQuestion.value) {
    if (!questionData.value.content.options || questionData.value.content.options.length < 2) {
      ElMessage.warning('选择题至少需要2个选项')
      return
    }
    
    // 检查选项是否为空
    const emptyOptions = questionData.value.content.options.filter(option => !option.trim())
    if (emptyOptions.length > 0) {
      ElMessage.warning('请填写所有选项内容')
      return
    }
    
    // 检查答案是否有效
    const answerOptions = questionData.value.content.answer.split(',').map(a => a.trim())
    const validOptions = questionData.value.content.options.map((_, index) => String.fromCharCode(65 + index))
    const invalidAnswers = answerOptions.filter(answer => !validOptions.includes(answer))
    
    if (invalidAnswers.length > 0) {
      ElMessage.warning('请选择有效的答案选项')
      return
    }
  }
  
  console.log('Validation passed, emitting save event')
  saving.value = true  // 设置加载状态
  emit('save')
}

const handleCancel = () => {
  emit('cancel')
}

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这道题目吗？',
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    emit('delete')
  } catch {
    // User cancelled
  }
}

const handlePreview = () => {
  previewVisible.value = true
}

// Watch for prop changes
const isUpdatingFromProps = ref(false)

watch(
  () => props.question,
  (newQuestion) => {
    isUpdatingFromProps.value = true
    questionData.value = { ...newQuestion }
    
    // Handle multiple choice answer
    if (newQuestion.tags.questionType === 'multiple_choice' && newQuestion.content.answer) {
      multipleAnswer.value = newQuestion.content.answer.split(',').filter(Boolean)
    } else {
      multipleAnswer.value = []
    }
    
    // Reset saving state when question changes (after save)
    saving.value = false
    
    // 延迟重置标志，确保所有更新完成后再重置
    nextTick(() => {
      setTimeout(() => {
        isUpdatingFromProps.value = false
      }, 50)
    })
  },
  { immediate: true, deep: true }
)

// Watch for questionData changes - 使用防抖来避免频繁更新
let updateTimeout: NodeJS.Timeout | null = null
watch(
  questionData,
  () => {
    // 如果是从 props 更新的，不触发更新计数
    if (isUpdatingFromProps.value) {
      return
    }
    
    if (updateTimeout) {
      clearTimeout(updateTimeout)
    }
    updateTimeout = setTimeout(() => {
      emitUpdate()
    }, 300) // 300ms 防抖
  },
  { deep: true }
)

// 清理定时器
onUnmounted(() => {
  if (updateTimeout) {
    clearTimeout(updateTimeout)
  }
})
</script>

<style scoped>
.question-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  min-height: 600px;
}

.question-editor .el-form {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  min-height: 0;
}

.question-editor .el-form-item {
  margin-bottom: 18px;
}

.rich-editor {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  min-height: 200px;
}

.rich-editor :deep(.ql-container) {
  border: none;
  min-height: 150px;
}

.rich-editor :deep(.ql-editor) {
  min-height: 150px;
}

.rich-editor :deep(.ql-toolbar) {
  border-bottom: 1px solid var(--el-border-color-light);
}

.options-editor {
  width: 100%;
}

.option-item {
  margin-bottom: 12px;
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.option-label {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.choice-answer {
  padding: 8px 0;
}

.choice-answer :deep(.el-checkbox-group),
.choice-answer :deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.editor-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-fill-color-extra-light);
  margin-top: auto;
  flex-shrink: 0;
}

/* Preview Styles */
.question-preview {
  max-height: 70vh;
  overflow-y: auto;
}

.preview-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color);
}

.preview-header h3 {
  margin: 0;
  color: var(--el-color-primary);
}

.preview-content {
  line-height: 1.6;
}

.preview-stem {
  margin-bottom: 16px;
  font-size: 16px;
}

.preview-options {
  margin: 16px 0;
  padding-left: 20px;
}

.preview-option {
  margin: 8px 0;
  line-height: 1.5;
}

.preview-answer {
  margin: 16px 0;
  padding: 12px;
  background-color: var(--el-fill-color-extra-light);
  border-radius: 6px;
  font-weight: 500;
}

.preview-explanation {
  margin-top: 16px;
  padding: 12px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 6px;
}

.preview-explanation strong {
  display: block;
  margin-bottom: 8px;
  color: var(--el-color-primary);
}

/* Responsive improvements */
@media (max-width: 768px) {
  .question-editor {
    min-height: 500px;
  }
  
  .question-editor .el-form {
    padding: 16px;
  }
  
  .rich-editor {
    min-height: 150px;
  }
  
  .rich-editor :deep(.ql-container),
  .rich-editor :deep(.ql-editor) {
    min-height: 120px;
  }
  
  .editor-actions {
    flex-direction: column;
    gap: 8px;
    padding: 16px;
  }
  
  .editor-actions .el-button {
    width: 100%;
  }
  
  .choice-answer :deep(.el-checkbox-group),
  .choice-answer :deep(.el-radio-group) {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .question-editor {
    min-height: 400px;
  }
  
  .question-editor .el-form {
    padding: 12px;
  }
  
  .rich-editor {
    min-height: 120px;
  }
  
  .rich-editor :deep(.ql-container),
  .rich-editor :deep(.ql-editor) {
    min-height: 100px;
  }
  
  .editor-actions {
    padding: 12px;
  }
  
  .question-editor .el-form-item {
    margin-bottom: 16px;
  }
}
</style>