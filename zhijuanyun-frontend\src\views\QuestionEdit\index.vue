<template>
  <div class="question-edit">
    <div class="edit-layout">
      <!-- Left Panel: Question List -->
      <div class="left-panel">
        <QuestionList
          :questions="questions"
          :loading="loading"
          @update:questions="handleQuestionsUpdate"
          @question-select="handleQuestionSelect"
          @question-edit="handleQuestionEdit"
          @question-delete="handleQuestionDelete"
          @questions-reorder="handleQuestionsReorder"
          @score-change="handleScoreChange"
          @add-question="handleAddQuestion"
        />
      </div>

      <!-- Right Panel: Question Editor -->
      <div class="right-panel">
        <el-card>
          <template #header>
            <div class="editor-header">
              <span>题目编辑器</span>
              <div class="header-actions">
                <el-button size="small" @click="handleSaveAll" :loading="saving">
                  保存所有更改
                </el-button>
                <el-button size="small" type="primary" @click="handleExportTest">
                  导出试卷
                </el-button>
              </div>
            </div>
          </template>

          <div class="editor-content">
            <!-- No Question Selected -->
            <div v-if="!editingQuestion" class="no-selection">
              <el-empty description="请选择要编辑的题目">
                <el-button type="primary" @click="handleAddQuestion">添加新题目</el-button>
              </el-empty>
            </div>

            <!-- Question Editor -->
            <div v-else class="question-editor">
              <QuestionEditor
                :question="editingQuestion"
                @update:question="handleQuestionUpdate"
                @save="handleSaveQuestion"
                @cancel="handleCancelEdit"
                @delete="handleDeleteCurrentQuestion"
              />
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- Statistics Panel -->
    <div class="stats-panel">
      <el-card>
        <template #header>
          <span>试卷统计</span>
        </template>
        <div class="stats-content">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-statistic title="题目总数" :value="questions.length" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="总分" :value="totalScore" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="已选题目" :value="selectedQuestions.length" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="未保存更改" :value="unsavedChanges" />
            </el-col>
          </el-row>

          <!-- Question Type Distribution -->
          <div class="type-distribution">
            <h4>题型分布</h4>
            <div class="type-tags">
              <el-tag 
                v-for="(count, type) in questionTypeStats" 
                :key="type"
                :type="getTagType(type)"
                size="small"
              >
                {{ getQuestionTypeLabel(type) }}: {{ count }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useFilterStore } from '@/stores/filter'
import type { Question } from '@/types'
import { QuestionService } from '@/services/question'

// Import components
import QuestionList from '@/components/ui/QuestionList.vue'
import QuestionEditor from '@/components/ui/QuestionEditor.vue'

const filterStore = useFilterStore()
const router = useRouter()

// State
const questions = ref<Question[]>([])
const selectedQuestions = ref<string[]>([])
const editingQuestion = ref<Question | null>(null)
const loading = ref(false)
const saving = ref(false)
const unsavedChanges = ref(0)

// 防抖控制未保存更改计数
let changeTimeout: NodeJS.Timeout | null = null
const incrementUnsavedChanges = () => {
  if (changeTimeout) {
    clearTimeout(changeTimeout)
  }
  changeTimeout = setTimeout(() => {
    unsavedChanges.value++
  }, 500) // 500ms 防抖，避免频繁更新
}

// Computed properties
const totalScore = computed(() => {
  return questions.value.reduce((total, question) => total + question.score, 0)
})

const questionTypeStats = computed(() => {
  const stats: Record<string, number> = {}
  questions.value.forEach(question => {
    const type = question.tags.questionType
    stats[type] = (stats[type] || 0) + 1
  })
  return stats
})

// Question type labels
const questionTypeLabels: Record<string, string> = {
  'single_choice': '单选题',
  'multiple_choice': '多选题',
  'true_false': '判断题',
  'fill_blank': '填空题',
  'short_answer': '简答题',
  'essay': '解答题',
  'calculation': '计算题',
  'application': '应用题',
  'analysis': '分析题',
  'comprehensive': '综合题'
}

const getQuestionTypeLabel = (type: string) => questionTypeLabels[type] || type

const getTagType = (questionType: string) => {
  const typeMap: Record<string, string> = {
    'single_choice': 'primary',
    'multiple_choice': 'success',
    'true_false': 'info',
    'fill_blank': 'warning',
    'short_answer': 'danger',
    'essay': '',
    'calculation': 'primary',
    'application': 'success',
    'analysis': 'info',
    'comprehensive': 'warning'
  }
  return typeMap[questionType] || ''
}

// Event handlers
const handleQuestionsUpdate = (updatedQuestions: Question[]) => {
  // 避免直接赋值，使用展开运算符创建新数组
  questions.value = [...updatedQuestions]
  // 只有在用户主动操作时才增加未保存更改计数
  // unsavedChanges.value++
}

const handleQuestionSelect = (questionIds: string[]) => {
  selectedQuestions.value = questionIds
}

const handleQuestionEdit = (questionId: string) => {
  const question = questions.value.find(q => q.id === questionId)
  if (question) {
    editingQuestion.value = { ...question }
  }
}

const handleQuestionDelete = async (questionId: string) => {
  // 对于测试数据或新添加的题目，直接删除
  if (questionId.startsWith('test-question-') || questionId.startsWith('new-question-')) {
    questions.value = questions.value.filter(q => q.id !== questionId)
    
    // Clear editing if the deleted question was being edited
    if (editingQuestion.value?.id === questionId) {
      editingQuestion.value = null
    }
    
    incrementUnsavedChanges()
    ElMessage.success('题目删除成功')
    return
  }

  try {
    // 先从本地列表中删除，确保用户界面立即响应
    questions.value = questions.value.filter(q => q.id !== questionId)
    
    // Clear editing if the deleted question was being edited
    if (editingQuestion.value?.id === questionId) {
      editingQuestion.value = null
    }
    
    ElMessage.success('题目删除成功')
    incrementUnsavedChanges()
    
    // 尝试同步到服务器，如果失败则回滚
    try {
      await QuestionService.deleteQuestion(questionId)
    } catch (apiError) {
      console.warn('Failed to sync deletion to server, keeping local change:', apiError)
      // 不回滚，保持本地删除状态
    }
  } catch (error) {
    console.error('Failed to delete question:', error)
    ElMessage.error('删除题目失败')
  }
}

const handleQuestionsReorder = (reorderedQuestions: Question[]) => {
  questions.value = [...reorderedQuestions]
  incrementUnsavedChanges()
  ElMessage.success('题目顺序已更新')
}

const handleScoreChange = async (questionId: string, score: number) => {
  try {
    const index = questions.value.findIndex(q => q.id === questionId)
    if (index !== -1) {
      // 创建新的数组以避免直接修改
      const newQuestions = [...questions.value]
      newQuestions[index] = { ...newQuestions[index], score }
      questions.value = newQuestions
      await QuestionService.updateQuestion(questionId, { score })
      incrementUnsavedChanges()
      ElMessage.success('分值更新成功')
    }
  } catch (error) {
    console.error('Failed to update score:', error)
    ElMessage.error('分值更新失败')
  }
}

const handleAddQuestion = () => {
  const newQuestion: Question = {
    id: `new-question-${Date.now()}`,
    content: {
      stem: '请输入题目内容...',
      options: ['选项A', '选项B', '选项C', '选项D'], // 为单选题提供默认选项
      answer: 'A',
      explanation: '',
      attachments: []
    },
    tags: {
      grade: filterStore.filterParams.grade || 'grade9',
      subject: filterStore.filterParams.subject || '数学',
      questionType: 'single_choice',
      difficulty: 'medium',
      knowledgePoint: [],
      scenario: '练习',
      sourceType: '手动添加'
    },
    score: 5,
    order: questions.value.length + 1
  }

  questions.value = [...questions.value, newQuestion]
  editingQuestion.value = { ...newQuestion }
  incrementUnsavedChanges()
}

const handleQuestionUpdate = (updatedQuestion: Question) => {
  const index = questions.value.findIndex(q => q.id === updatedQuestion.id)
  if (index !== -1) {
    // 创建新的数组以避免直接修改
    const newQuestions = [...questions.value]
    newQuestions[index] = { ...updatedQuestion }
    questions.value = newQuestions
    editingQuestion.value = { ...updatedQuestion }
    
    // 使用防抖机制增加未保存更改计数
    incrementUnsavedChanges()
  }
}

const handleSaveQuestion = async () => {
  if (!editingQuestion.value) return

  console.log('Starting to save question:', editingQuestion.value)

  try {
    saving.value = true
    
    // 清除pending的更改计数
    if (changeTimeout) {
      clearTimeout(changeTimeout)
      changeTimeout = null
    }
    
    let savedQuestion: Question
    
    if (editingQuestion.value.id.startsWith('new-question-')) {
      console.log('Creating new question')
      // Create new question
      try {
        savedQuestion = await QuestionService.createQuestion(editingQuestion.value)
        editingQuestion.value.id = savedQuestion.id
        console.log('New question created successfully:', savedQuestion)
      } catch (apiError) {
        console.warn('Failed to create question via API, keeping local copy:', apiError)
        // 在开发环境中，即使API失败也继续保存本地副本
        editingQuestion.value.id = `question-${Date.now()}`
        savedQuestion = editingQuestion.value
      }
    } else {
      console.log('Updating existing question')
      // Update existing question
      try {
        savedQuestion = await QuestionService.updateQuestion(editingQuestion.value.id, editingQuestion.value)
        console.log('Question updated successfully:', savedQuestion)
      } catch (apiError) {
        console.warn('Failed to update question via API, keeping local changes:', apiError)
        // 保持本地更改
        savedQuestion = editingQuestion.value
      }
    }
    
    // Update the question in the list
    const index = questions.value.findIndex(q => q.id === editingQuestion.value!.id || q.id === savedQuestion.id)
    if (index !== -1) {
      const newQuestions = [...questions.value]
      newQuestions[index] = { ...savedQuestion }
      questions.value = newQuestions
      editingQuestion.value = { ...savedQuestion }
    }
    
    // 保存成功后减少未保存更改计数
    unsavedChanges.value = Math.max(0, unsavedChanges.value - 1)
    ElMessage.success('题目保存成功')
    console.log('Question saved successfully')
  } catch (error) {
    console.error('Failed to save question:', error)
    ElMessage.error('保存题目失败')
  } finally {
    saving.value = false
  }
}

const handleCancelEdit = () => {
  editingQuestion.value = null
}

const handleDeleteCurrentQuestion = async () => {
  if (!editingQuestion.value) return
  
  await handleQuestionDelete(editingQuestion.value.id)
  editingQuestion.value = null
}

const handleSaveAll = async () => {
  if (unsavedChanges.value === 0) {
    ElMessage.info('没有需要保存的更改')
    return
  }

  try {
    saving.value = true
    
    // 清除pending的更改计数
    if (changeTimeout) {
      clearTimeout(changeTimeout)
      changeTimeout = null
    }
    
    // 分离新题目和现有题目
    const newQuestions = questions.value.filter(q => q.id.startsWith('new-question-'))
    const existingQuestions = questions.value.filter(q => !q.id.startsWith('new-question-') && !q.id.startsWith('test-question-'))
    
    let savedCount = 0
    
    // 保存新题目
    for (const question of newQuestions) {
      try {
        const savedQuestion = await QuestionService.createQuestion(question)
        // 更新题目ID
        const index = questions.value.findIndex(q => q.id === question.id)
        if (index !== -1) {
          questions.value[index] = { ...savedQuestion }
        }
        savedCount++
      } catch (error) {
        console.warn('Failed to save new question, keeping local copy:', error)
        // 即使API失败，也更新ID以表示"已保存"
        const index = questions.value.findIndex(q => q.id === question.id)
        if (index !== -1) {
          questions.value[index].id = `question-${Date.now()}-${index}`
        }
        savedCount++
      }
    }
    
    // 批量更新现有题目
    if (existingQuestions.length > 0) {
      try {
        const updates = existingQuestions.map(question => ({
          id: question.id,
          updates: question
        }))
        
        await QuestionService.batchUpdateQuestions(updates)
        savedCount += existingQuestions.length
      } catch (error) {
        console.warn('Failed to batch update questions, keeping local changes:', error)
        // 即使API失败，也计入保存数量
        savedCount += existingQuestions.length
      }
    }
    
    unsavedChanges.value = 0
    ElMessage.success(`成功保存 ${savedCount} 道题目`)
  } catch (error) {
    console.error('Failed to save all questions:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleExportTest = () => {
  if (questions.value.length === 0) {
    ElMessage.warning('没有题目可以导出')
    return
  }
  
  // Navigate to paper layout view
  router.push('/layout')
}

// Initialize with questions from filter store
onMounted(() => {
  if (filterStore.questions.length > 0) {
    questions.value = [...filterStore.questions]
  } else {
    // Add some test data for layout verification
    questions.value = [
      {
        id: 'test-question-1',
        content: {
          stem: '已知函数 f(x) = x² + 2x + 1，求 f(x) 的最小值。',
          options: ['A. 0', 'B. 1', 'C. 2', 'D. 3'],
          answer: 'A',
          explanation: 'f(x) = x² + 2x + 1 = (x + 1)²，所以最小值为 0。',
          attachments: []
        },
        tags: {
          grade: 'grade9',
          subject: '数学',
          questionType: 'single_choice',
          difficulty: 'medium',
          knowledgePoint: ['二次函数', '配方法'],
          scenario: '练习',
          sourceType: '手动添加'
        },
        score: 5,
        order: 1
      },
      {
        id: 'test-question-2',
        content: {
          stem: '下列哪个选项是正确的？',
          options: ['A. 1 + 1 = 3', 'B. 2 + 2 = 4', 'C. 3 + 3 = 5', 'D. 4 + 4 = 9'],
          answer: 'B',
          explanation: '2 + 2 = 4 是正确的数学运算。',
          attachments: []
        },
        tags: {
          grade: 'grade9',
          subject: '数学',
          questionType: 'single_choice',
          difficulty: 'easy',
          knowledgePoint: ['基础运算'],
          scenario: '练习',
          sourceType: '手动添加'
        },
        score: 3,
        order: 2
      }
    ]
  }
})

// Watch for changes from filter store
watch(
  () => filterStore.questions,
  (newQuestions) => {
    if (newQuestions.length > 0) {
      questions.value = [...newQuestions]
    }
  },
  { deep: true, immediate: false }
)

// 清理定时器
onUnmounted(() => {
  if (changeTimeout) {
    clearTimeout(changeTimeout)
  }
})
</script>

<style scoped>
.question-edit {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--el-fill-color-extra-light);
  overflow: hidden;
}

.edit-layout {
  display: flex;
  flex: 1;
  gap: 20px;
  padding: 16px;
  height: calc(100vh - 200px);
  min-height: 600px;
}

.left-panel {
  width: 380px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.right-panel {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.right-panel .el-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.right-panel .el-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 600px;
}

.no-selection {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 400px;
}

.question-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 600px;
}

.stats-panel {
  padding: 0 16px 16px;
  flex-shrink: 0;
}

.stats-content {
  padding: 16px 0;
}

.type-distribution {
  margin-top: 24px;
}

.type-distribution h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
}

.type-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* Responsive design improvements */
@media (max-width: 1400px) {
  .left-panel {
    width: 350px;
  }
}

@media (max-width: 1200px) {
  .edit-layout {
    flex-direction: column;
    height: auto;
    min-height: calc(100vh - 140px);
  }
  
  .left-panel {
    width: 100%;
    height: 450px;
    overflow-y: auto;
    flex-shrink: 0;
  }
  
  .right-panel {
    flex: 1;
    min-height: 500px;
  }
}

@media (max-width: 768px) {
  .edit-layout {
    padding: 12px;
    gap: 16px;
  }
  
  .left-panel {
    height: 350px;
  }
  
  .stats-panel {
    padding: 0 12px 12px;
  }
  
  .stats-content .el-row {
    display: block;
  }
  
  .stats-content .el-col {
    width: 100% !important;
    margin-bottom: 12px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 4px;
  }
  
  .header-actions .el-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .edit-layout {
    padding: 8px;
    gap: 12px;
  }
  
  .left-panel {
    height: 300px;
  }
  
  .stats-panel {
    padding: 0 8px 8px;
  }
}
</style>