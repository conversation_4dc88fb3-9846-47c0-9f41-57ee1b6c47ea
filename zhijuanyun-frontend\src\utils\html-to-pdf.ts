// 使用HTML渲染然后转换为PDF的方案
// 这是处理中文字符最可靠的方法

import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

export interface HTMLToPDFOptions {
  scale?: number
  useCORS?: boolean
  allowTaint?: boolean
  backgroundColor?: string
  width?: number
  height?: number
}

export class HTMLToPDFConverter {
  // 将HTML元素转换为PDF
  static async convertElementToPDF(
    element: HTMLElement, 
    options: HTMLToPDFOptions = {}
  ): Promise<jsPDF> {
    const {
      scale = 2,
      useCORS = true,
      allowTaint = true,
      backgroundColor = '#ffffff'
    } = options
    
    try {
      // 使用html2canvas渲染HTML为图片
      const canvas = await html2canvas(element, {
        scale,
        useCORS,
        allowTaint,
        backgroundColor,
        logging: false,
        width: element.scrollWidth,
        height: element.scrollHeight
      })
      
      // 创建PDF
      const pdf = new jsPDF('p', 'mm', 'a4')
      const imgData = canvas.toDataURL('image/png')
      
      // 计算图片在PDF中的尺寸
      const pdfWidth = pdf.internal.pageSize.getWidth()
      const pdfHeight = pdf.internal.pageSize.getHeight()
      const imgWidth = canvas.width
      const imgHeight = canvas.height
      
      // 计算缩放比例
      const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight)
      const scaledWidth = imgWidth * ratio
      const scaledHeight = imgHeight * ratio
      
      // 居中显示
      const x = (pdfWidth - scaledWidth) / 2
      const y = (pdfHeight - scaledHeight) / 2
      
      pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight)
      
      return pdf
    } catch (error) {
      console.error('HTML to PDF conversion failed:', error)
      throw error
    }
  }
  
  // 从HTML字符串创建PDF
  static async convertHTMLStringToPDF(
    htmlString: string,
    options: HTMLToPDFOptions = {}
  ): Promise<jsPDF> {
    // 创建临时DOM元素
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = htmlString
    tempDiv.style.position = 'absolute'
    tempDiv.style.left = '-9999px'
    tempDiv.style.top = '-9999px'
    tempDiv.style.width = '800px'
    tempDiv.style.padding = '20px'
    tempDiv.style.fontFamily = 'Microsoft YaHei, SimSun, sans-serif'
    tempDiv.style.fontSize = '14px'
    tempDiv.style.lineHeight = '1.6'
    tempDiv.style.color = '#333'
    tempDiv.style.backgroundColor = '#fff'
    
    document.body.appendChild(tempDiv)
    
    try {
      const pdf = await this.convertElementToPDF(tempDiv, options)
      return pdf
    } finally {
      document.body.removeChild(tempDiv)
    }
  }
  
  // 创建试卷HTML
  static createExamHTML(questions: any[], settings: any, totalScore: number): string {
    // 如果是答题卡模式，创建答题卡HTML
    if (settings.answerSheet) {
      return this.createAnswerSheetHTML(questions, settings, totalScore)
    }

    // 普通试卷模式
    const html = `
      <div style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: 'Microsoft YaHei', SimSun, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="font-size: 24px; margin-bottom: 10px;">${settings.title}</h1>
          <div style="font-size: 14px; color: #666;">
            <span>本试卷共 ${questions.length} 题，总分 ${totalScore} 分</span>
            ${settings.duration ? `<span style="margin-left: 20px;">考试时间 ${settings.duration} 分钟</span>` : ''}
          </div>
        </div>

        <div style="margin-bottom: 20px; padding: 15px; background: #f9f9f9; border-radius: 5px;">
          <h3 style="margin: 0 0 10px 0; font-size: 16px;">注意事项：</h3>
          <ol style="margin: 0; padding-left: 20px; line-height: 1.8;">
            <li>请在答题前仔细阅读各题目要求</li>
            <li>所有答案必须写在答题纸上，写在试卷上无效</li>
            <li>考试结束后，将试卷和答题纸一并交回</li>
          </ol>
        </div>

        <div class="questions">
          ${questions.map((question, index) => this.createQuestionHTML(question, index + 1, settings)).join('')}
        </div>

        ${settings.watermark?.enabled ? `
          <div style="position: fixed; bottom: 20px; right: 20px; color: #ccc; font-size: 12px; transform: rotate(-45deg);">
            ${settings.watermark.text}
          </div>
        ` : ''}
      </div>
    `

    return html
  }

  // 创建答题卡HTML
  static createAnswerSheetHTML(questions: any[], settings: any, totalScore: number): string {
    const choiceQuestions = questions.filter(q =>
      ['single_choice', 'multiple_choice', 'true_false'].includes(q.tags.questionType)
    )

    const nonChoiceQuestions = questions.filter(q =>
      !['single_choice', 'multiple_choice', 'true_false'].includes(q.tags.questionType)
    )

    const html = `
      <div style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: 'Microsoft YaHei', SimSun, sans-serif; font-size: 12px;">
        <!-- 答题卡头部 -->
        <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid #000; padding-bottom: 15px;">
          <h1 style="font-size: 20px; margin: 0 0 10px 0;">${settings.title} - 答题卡</h1>
          <div style="font-size: 12px; color: #333;">
            总分：${totalScore}分 | 时间：${settings.duration}分钟 | 题目数：${questions.length}题
          </div>
        </div>

        <!-- 考生信息区 -->
        <div style="margin-bottom: 20px; border: 1px solid #000; padding: 15px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <div style="display: flex; align-items: center;">
              <span style="margin-right: 10px;">姓名：</span>
              <div style="display: inline-flex; gap: 2px;">
                ${Array(6).fill(0).map(() => '<div style="width: 20px; height: 20px; border: 1px solid #000; display: inline-block;"></div>').join('')}
              </div>
            </div>
            <div style="display: flex; align-items: center;">
              <span style="margin-right: 10px;">学号：</span>
              <div style="display: inline-flex; gap: 2px;">
                ${Array(10).fill(0).map(() => '<div style="width: 20px; height: 20px; border: 1px solid #000; display: inline-block;"></div>').join('')}
              </div>
            </div>
          </div>
          <div style="display: flex; justify-content: space-between;">
            <div style="display: flex; align-items: center;">
              <span style="margin-right: 10px;">班级：</span>
              <div style="display: inline-flex; gap: 2px;">
                ${Array(8).fill(0).map(() => '<div style="width: 20px; height: 20px; border: 1px solid #000; display: inline-block;"></div>').join('')}
              </div>
            </div>
            <div style="display: flex; align-items: center;">
              <span style="margin-right: 10px;">考场：</span>
              <div style="display: inline-flex; gap: 2px;">
                ${Array(6).fill(0).map(() => '<div style="width: 20px; height: 20px; border: 1px solid #000; display: inline-block;"></div>').join('')}
              </div>
            </div>
          </div>
        </div>

        <!-- 填涂说明 -->
        <div style="margin-bottom: 20px; padding: 10px; background: #f5f5f5; border: 1px solid #ddd;">
          <h4 style="margin: 0 0 8px 0; font-size: 14px;">填涂说明：</h4>
          <div style="font-size: 11px; line-height: 1.4;">
            <p style="margin: 2px 0;">1. 答题前，考生先将自己的姓名、学号、班级等信息填写清楚。</p>
            <p style="margin: 2px 0;">2. 选择题部分请用2B铅笔填涂答题卡，如需改动，用橡皮擦擦干净后，再选涂其他答案。</p>
            <p style="margin: 2px 0;">3. 非选择题部分请用黑色签字笔在答题区域内作答，超出答题区域的答案无效。</p>
            <p style="margin: 2px 0;">4. 保持答题卡清洁，不要折叠、不要弄破。</p>
          </div>
          <div style="margin-top: 8px; display: flex; align-items: center; gap: 15px;">
            <span style="font-size: 11px;">正确填涂：</span>
            <div style="width: 12px; height: 12px; border: 1px solid #000; background: #000; border-radius: 50%;"></div>
            <span style="font-size: 11px;">错误填涂：</span>
            <div style="width: 12px; height: 12px; border: 1px solid #000; background: linear-gradient(45deg, #000 25%, transparent 25%); border-radius: 50%;"></div>
            <div style="width: 12px; height: 12px; border: 1px solid #000; border-radius: 50%;"></div>
          </div>
        </div>

        ${choiceQuestions.length > 0 ? this.createChoiceAnswerArea(choiceQuestions) : ''}
        ${nonChoiceQuestions.length > 0 ? this.createNonChoiceAnswerArea(nonChoiceQuestions) : ''}

        ${settings.watermark?.enabled ? `
          <div style="position: fixed; bottom: 20px; right: 20px; color: #ccc; font-size: 10px; transform: rotate(-45deg);">
            ${settings.watermark.text}
          </div>
        ` : ''}
      </div>
    `

    return html
  }

  // 创建选择题答题区域
  private static createChoiceAnswerArea(choiceQuestions: any[]): string {
    const questionsPerRow = 5 // 每行5题
    const rows = Math.ceil(choiceQuestions.length / questionsPerRow)

    let html = `
      <div style="margin-bottom: 25px; border: 1px solid #000; padding: 15px;">
        <h3 style="margin: 0 0 15px 0; font-size: 14px; text-align: center; background: #f0f0f0; padding: 8px; border: 1px solid #ccc;">选择题答题区</h3>
        <div style="display: grid; grid-template-columns: repeat(${questionsPerRow}, 1fr); gap: 10px;">
    `

    choiceQuestions.forEach((question, index) => {
      const questionNum = index + 1
      const optionCount = question.content.options ? question.content.options.length : 4
      const options = ['A', 'B', 'C', 'D', 'E', 'F'].slice(0, optionCount)

      html += `
        <div style="text-align: center; padding: 8px; border: 1px solid #ddd;">
          <div style="font-weight: bold; margin-bottom: 5px; font-size: 11px;">${questionNum}</div>
          <div style="display: flex; justify-content: center; gap: 3px;">
            ${options.map(option => `
              <div style="text-align: center;">
                <div style="font-size: 9px; margin-bottom: 2px;">${option}</div>
                <div style="width: 12px; height: 12px; border: 1px solid #000; border-radius: 50%; margin: 0 auto;"></div>
              </div>
            `).join('')}
          </div>
        </div>
      `
    })

    html += `
        </div>
      </div>
    `

    return html
  }

  // 创建非选择题答题区域
  private static createNonChoiceAnswerArea(nonChoiceQuestions: any[]): string {
    let html = `
      <div style="margin-bottom: 25px; border: 1px solid #000; padding: 15px;">
        <h3 style="margin: 0 0 15px 0; font-size: 14px; text-align: center; background: #f0f0f0; padding: 8px; border: 1px solid #ccc;">非选择题答题区</h3>
    `

    nonChoiceQuestions.forEach((question, index) => {
      const questionNum = this.getQuestionNumber(question, nonChoiceQuestions)
      const answerLines = this.getAnswerLinesCount(question.tags.questionType, question.score)

      html += `
        <div style="margin-bottom: 20px; border: 1px solid #ddd; padding: 10px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding-bottom: 5px; border-bottom: 1px solid #eee;">
            <span style="font-weight: bold; font-size: 12px;">${questionNum}.</span>
            <span style="font-size: 11px; color: #666;">(${question.score}分)</span>
            <span style="font-size: 10px; color: #888;">${this.getQuestionTypeLabel(question.tags.questionType)}</span>
          </div>
          <div style="min-height: ${answerLines * 20}px;">
            ${Array(answerLines).fill(0).map((_, lineIndex) =>
              `<div style="height: 18px; border-bottom: 1px solid #ddd; margin-bottom: 2px;"></div>`
            ).join('')}
          </div>
        </div>
      `
    })

    html += `
      </div>
    `

    return html
  }

  // 获取题目编号
  private static getQuestionNumber(question: any, allQuestions: any[]): number {
    return allQuestions.findIndex(q => q.id === question.id) + 1
  }

  // 获取答题行数
  private static getAnswerLinesCount(questionType: string, score: number): number {
    const baseLines = {
      'fill_blank': 2,
      'short_answer': 4,
      'essay': 8,
      'calculation': 6,
      'proof': 8,
      'analysis': 6,
      'design': 10
    }

    const lines = baseLines[questionType] || 4
    // 根据分值调整行数
    const scoreMultiplier = Math.max(1, Math.floor(score / 5))
    return Math.min(lines * scoreMultiplier, 15) // 最多15行
  }

  // 获取题型标签
  private static getQuestionTypeLabel(questionType: string): string {
    const labels = {
      'single_choice': '单选题',
      'multiple_choice': '多选题',
      'true_false': '判断题',
      'fill_blank': '填空题',
      'short_answer': '简答题',
      'essay': '论述题',
      'calculation': '计算题',
      'proof': '证明题',
      'analysis': '分析题',
      'design': '设计题'
    }
    return labels[questionType] || '其他题型'
  }

  // 创建单个题目HTML
  private static createQuestionHTML(question: any, index: number, settings: any): string {
    const { content, score, tags } = question
    
    let html = `
      <div style="margin-bottom: 25px; page-break-inside: avoid;">
        <div style="font-weight: bold; margin-bottom: 8px;">
          ${index}. (${score}分) ${content.stem}
        </div>
    `
    
    // 选择题选项
    if (content.options && content.options.length > 0) {
      html += '<div style="margin-left: 20px; line-height: 1.8;">'
      content.options.forEach((option: string, optIndex: number) => {
        const letter = String.fromCharCode(65 + optIndex) // A, B, C, D
        html += `<div>${letter}. ${option}</div>`
      })
      html += '</div>'
    }
    
    // 显示答案
    if (settings.includes?.includes('answers') && content.answer) {
      html += `
        <div style="margin-top: 10px; padding: 8px; background: #e8f5e8; border-left: 3px solid #4caf50;">
          <strong>答案：</strong>${content.answer}
        </div>
      `
    }
    
    // 显示解析
    if (settings.includes?.includes('explanations') && content.explanation) {
      html += `
        <div style="margin-top: 10px; padding: 8px; background: #f0f8ff; border-left: 3px solid #2196f3;">
          <strong>解析：</strong>${content.explanation}
        </div>
      `
    }
    
    html += '</div>'
    return html
  }
}

// 简化的导出工具
export const simpleExportUtils = {
  // 导出试卷为PDF
  async exportExamToPDF(questions: any[], settings: any, totalScore: number): Promise<Blob> {
    try {
      const htmlString = HTMLToPDFConverter.createExamHTML(questions, settings, totalScore)
      const pdf = await HTMLToPDFConverter.convertHTMLStringToPDF(htmlString, {
        scale: 1.5,
        backgroundColor: '#ffffff'
      })
      
      return pdf.output('blob')
    } catch (error) {
      console.error('Export to PDF failed:', error)
      throw error
    }
  },
  
  // 预览HTML内容
  previewHTML(questions: any[], settings: any, totalScore: number): string {
    return HTMLToPDFConverter.createExamHTML(questions, settings, totalScore)
  },
  
  // 检查是否支持HTML2Canvas
  isSupported(): boolean {
    return typeof html2canvas !== 'undefined' && typeof document !== 'undefined'
  }
}
