<!-- Page 1 -->

场景类型
示例题目
基础巩固
解分式方程：x/2 = 3
换数/换情境
解分式方程：x/5 = 7
易-中-难递进
易：x/3 = 2；中：(x+1)/4 = 3；难：(x-2)/(x+1) = 2
生活应用
“小明分糖果，每人分到x颗，分了2人后还剩3颗，求x。”
真题变式
改编自2018年中考分式方程题，换数据或问法
情境化设问
“在一次班级活动中，老师让同学们分组，每组x人，分了3组还剩2人，求x。”
竞赛/拓展
“若x满足(x-1)/(x+2)=1/2，且x为正整数，求x的所有取值。”
图表/实验
“根据下表数据，列出分式方程并求解。”
多步骤综合
“解分式方程x/2 + (x+1)/3 = 5，并检验解的正确性。”
易错点辨析
“下列关于分式方程的说法正确的是：A…B…C…”
智卷云需求书6.0-2025-07-29-16-36
 
·······················································································详细版
·······························································································
1 背景与痛点
 
1.1 政策背景
 
近年来，政府出台政策，明确禁止学校统一采购教辅资料（如练习册），以减轻学生课业负担，规
范教育资源采购行为。
2.2 现实需求
 
尽管政策限制了教辅采购，但日常教学中，作业和试题依然是刚性需求。教师和学生面临“题从哪里
来”的双重难题：
内容生产：如何高效、优质地生成符合教学要求的题目。
作业承载与批改：如何便捷地组织、分发、批改作业，提升教学效率。
2 出题维度梳理
 
2.1 按知识层级
 
题目可按知识点、课、单元、期中/期末等不同层级进行组织，满足不同教学阶段的需求。
2.2 按题目来源与题型
 
真题：小考、中考、高考等原题，支持标签化筛选。
相似题：基于真题或典型题目，AI自动生成结构相似的新题。
变式题：围绕同一知识点，生成难度递进、情境多样的题目（如生活应用、竞赛、场景化等）——


<!-- Page 2 -->

2.3 变量标签
 
难度、年级、学科、知识点、应用场景、生活常识（可选，适用于小学/初中阶段和新课标）、新课
标、区域教研组等，均可作为标签进行多维度筛选和组合。
2.4 输出形式
 
课间练习：A5/A4单页，便于课堂快速练习。
单元/期中/期末卷：A3试卷，支持多题型混排（选择、填空、问答、阅读等），自动赋分。
3 系统架构思路
 
3.1 模块化设计
 
系统拆分为可拼装的小模块，便于灵活扩展和维护。
3.2 主要功能模块
 
题库检索：支持联网搜索，标签化检索，权重排序，提示词工程优化检索体验。
AI出题：根据出题规则和输入条件，AI自动生成题目，补充题库不足。
排版输出：支持A4/A3等多种纸张大小，自动排版，题目分值灵活设置。
权限与数据接口：为不同角色（领导、家长、教师）预留视图和数据接口（后期实现）。
老师样题上传与AI仿题：支持老师在前端直接上传样题（如Word、PDF、图片等），智能体可基于
上传样题进行风格、结构、难度等多维度的模仿出题，实现个性化自动出题。（新增）
4 核心流程与任务清单
 
4.1 核心流程（突出个人样题模仿）
 
步骤1：输入条件与样题上传
 
用户操作：在前端界面选择/输入年级、学科、知识点、题型、难度、题目数量等参数。
个人样题模仿：教师可上传个人样题（如Word、PDF、图片等），作为AI出题的风格和内容参考。
技术要点：
前端支持多标签选择和样题文件上传。
样题内容与参数一同组装为AI接口请求。
验收标准：参数和样题上传便捷，界面响应流畅，样题内容可被AI识别。
步骤2：AI模仿出题
 
系统操作：前端调用AI出题接口，将用户输入的参数和上传的样题一并发送，AI根据样题风格和内
容自动生成新题。
技术要点：
AI接口需支持“样题模仿”模式，能理解并学习上传样题的结构、风格、难度等。
生成题目自动打标签、去重、结构化存储。


<!-- Page 3 -->

验收标准：生成的新题与上传样题在风格、结构、难度等方面高度相似，且满足用户设定的筛选条
件。
步骤3：题目预览与编辑
 
用户操作：前端展示AI生成的题目，教师可手动调整题目顺序、分值、题型，支持增删题目。
技术要点：
支持拖拽排序、分值编辑、题型切换、题目增删。
实时保存编辑结果，支持撤销/重做。
验收标准：所有编辑操作无误，题目内容与顺序实时更新，界面友好。
步骤4：选择输出模板与排版
 
用户操作：选择输出模板（A4/A3、单页/多页、题型混排等），设置排版参数。
技术要点：
模板参数与题目数据结构自动适配。
支持模板预览。
验收标准：模板选择后，排版预览与参数一致。
步骤5：生成与导出
 
系统操作：前端将题目数据（JSON）和排版参数直接发送给后端Python导出函数（即“出题大师
V3”现有的docx导出方案），由后端自动排版、分页、赋分、题型排列，生成最终Word文档（如需
PDF可后续扩展）。
技术要点：
直接复用现有docx导出逻辑，无需重写排版算法。
仅需根据新需求调整参数和前端对接方式。
验收标准：生成文档排版美观、内容完整、格式正确，满足新版需求。
步骤6：下载/打印
 
用户操作：前端接收后端返回的文档URL，用户可一键下载生成的Word（或PDF）文件，或通过浏
览器/本地打印功能直接打印。
技术要点：
保持现有的URL返回与文件存储机制，确保下载链接有效、文件安全。
前端可增加“下载”与“打印”按钮，调用浏览器原生打印功能。
验收标准：文件可正常下载、打开、打印，内容与预览一致。
说明：本环节只需在“出题大师V3”现有导出方案基础上，按新需求调整参数和前端对接方式，无需
重写导出逻辑，开发量极小。
4.2 本周任务清单（开发推进）
 
1. 需求梳理与技术方案
明确每个核心流程的功能点、交互细节、接口参数。
输出详细产品功能清单和技术实现方案。
2. 架构搭建与模块联通（前端+智能体方案）


<!-- Page 4 -->

搭建前端应用基础架构，集成智能体API（如平台RAG/LLM接口、第三方AI出题API等）。
前端直接调用智能体API，实现题库检索、AI出题、题目编辑、排版等功能的联通。
所有数据（如题目、标签、参数）在前端本地管理（如state、localStorage、IndexedDB
等），或通过API与平台/云端交互。
明确各API的调用规范、参数格式和数据流转方式，确保前端与智能体API的数据交互顺畅。
3. 输入条件交互开发（前端+智能体方案）
前端实现多标签选择、参数输入、表单校验等交互界面。
用户输入的所有参数和标签，直接由前端组装为API请求，发送给智能体API（如题库检索、AI
出题等）。
前端负责处理API返回的数据，进行题目展示、编辑、排版等后续操作。
所有用户操作和数据变更均在前端本地完成，无需自有后端参与。
4. 题库筛选与AI补题流程开发
实现题库多标签检索、权重排序、去重。
实现AI自动补题逻辑，确保题目数量满足需求。
5. 题目预览与编辑功能开发
实现题目列表预览、拖拽排序、分值编辑、题目增删等交互。
实现编辑结果的实时保存与回显。
6. 排版与导出功能开发
实现模板选择、排版参数设置、自动分页与赋分。
实现PDF/Word导出功能，支持文件下载。
> 我们的排版参数设置分为基础功能和进阶功能两部分，建议分阶段实现：
第一阶段：基础排版参数设置（优先上线）
 
只需支持常用参数：纸张大小、字体、字号、答案是否显示、分值显示、页眉页脚等。
前端用表单收集参数，后端用模板渲染（HTML/CSS或直接PDF/Word），导出文件即可。
这部分实现难度不大，优先保证导出功能可用。
第二阶段：复杂排版（后续迭代）
 
包括自动分页、题型混排、所见即所得预览等。
这部分可以用开源库（如jsPDF、pdf-lib、docx.js、WeasyPrint等）辅助实现，难度较高，需
要详细设计和测试。
可以等基础功能稳定后再逐步开发。
7. 端到端流程打通与自测
从输入条件到下载/打印，完成全链路流程自测。
整理bug与优化建议，准备下周迭代。


<!-- Page 5 -->

标
签
类
别
字段名
类型
示例值/说明
备注
年
级
grade
string
"一年级"、"高三"
枚举/可扩
展，前端标签
体系
学
科
subject
string
"数学"、"英语"
枚举/可扩
展，前端标签
体系
题
型
question_type
string
"选择题"、"填空题"
枚举/可扩
展，前端标签
体系
难
度
difficulty
string
"易"、"中"、"难"、"竞赛"
枚举/可扩
展，前端标签
体系
知
识
点
knowledge_point
array
["分数加减法", "牛顿第二定
律"]
支持多选/树
状结构，前端
标签体系
应
用
场
景
scenario
string
"课堂练习"、"竞赛"
枚举/可扩
展，前端标签
体系
4.3 验收标准
 
每个流程节点均有明确的输入、输出、交互和异常处理。
支持Web端自适应布局，兼容主流浏览器，界面响应流畅。
生成文档内容与用户设置完全一致。
主要功能点均有自测用例，bug率低于预期。
5 标签与参数设计
 
5.1 设计原则
 
标准化：所有标签字段需有统一的命名、类型、取值范围，便于数据一致性和后续维护。
多标签支持：每道题可绑定多个标签，支持并集/交集筛选。
可扩展性：预留自定义标签和参数，便于后续增加新维度（如“教材版本”、“题目风格”等）。
结构化：题目数据结构需支持嵌套（如题干、选项、答案、解析、标签等分层存储）。
兼容性：支持与主流题库、教育平台的数据对接和迁移。
5.2 标签类别与字段定义
 


<!-- Page 6 -->

标
签
类
别
字段名
类型
示例值/说明
备注
题
目
来
源
source_type
string
"高考真题"、"AI生成"、"样题
仿作"
枚举/可扩
展，前端+智
能体API生成
样
题
上
传
sample_upload
file
Word、PDF、图片等
用户上传样题
文件，前端
+智能体API
样
题
来
源
sample_source
string
"用户上传"
新增，指AI仿
作的样题来
源，前端+智
能体API
区
域
教
研
组
region
string
"广东省"、"全国通用"
枚举/可扩
展，前端标签
体系
新
课
标
curriculum_standard
string
"2022版"、"2017版"
枚举/可扩
展，前端标签
体系
题
目
数
量
question_count
integer
10
生成/筛选参
数，前端+智
能体API
输
出
格
式
output_format
string
"A4"、"A3"、"PDF"、"Word"
枚举，前端导
出设置
标
签
筛
选
tag_filter_mode
string
"AND"、"OR"
并集/交集筛
选，前端本地
实现
权
重
排
序
weight
float
0.8
检索/排序
用，前端+智
能体API
去
重
deduplicate
boolean
true/false
检索参数，前
端+智能体API


<!-- Page 7 -->

标
签
类
别
字段名
类型
示例值/说明
备注
顺
序
调
整
drag_sort
array
[3,1,2,4]
题目ID顺序，
前端本地实现
分
值
设
置
score_per_question
float
5.0
单题分值，前
端本地实现
题
型
混
排
question_mix
boolean
true/false
前端本地实现
增
删
题
目
add/remove
array
[题目ID]
前端本地实现
模
板
选
择
template_id
string
"A4-vertical"
前端本地实现
自
动
分
页
auto_paginate
boolean
true/false
前端本地实现
文
件
导
出
export_type
string
"PDF"、"Word"
前端本地实现
下
载
链
接
download_url
string
"https://..."
前端本地实现
5.3 题目结构建议（JSON示例）
 
{
  "id": "q123456",
  "content": {
    "stem": "下列哪项是牛顿第二定律的正确表达？",
    "options": ["F=ma", "E=mc^2", "a^2+b^2=c^2", "V=IR"],


<!-- Page 8 -->

5.4 检索与生成参数（接口建议）
 
支持多标签并集/交集筛选（如“年级=初二 AND 学科=物理 AND 难度=中”）
支持权重排序（如“优先返回高考真题”）
支持题目数量、题型、分值等参数定制
支持去重、混排、顺序调整等高级参数
支持导出格式、模板选择、自动分页等排版参数
5.5 典型用例
 
用例1：老师想要“高三数学期末卷（难度中等，选择题+填空题，A3排版）”
检索参数： grade=高三, subject=数学, question_type=[选择题,填空题], difficulty=
中, output_format=A3
用例2：自动生成10道与“分数加减法”相关的变式题
检索参数： knowledge_point=分数加减法, source_type=变式题, question_count=10, 
deduplicate=true
用例3：导出为PDF并自动分页
排版参数： export_type=PDF, auto_paginate=true
5.6 扩展性说明
 
自定义标签：支持后续增加如“教材版本”、“题目风格”、“适用教材”等新标签。
多层级知识点：知识点字段支持树状结构，便于细粒度筛选。
多语言支持：如需国际化，标签和内容字段可支持多语言。
题目结构扩展：支持多种题型（如主观题、组合题、材料题等）的结构化扩展。
    "answer": "F=ma",
    "explanation": "牛顿第二定律的公式为F=ma。",
    "attachments": []
  },
  "tags": {
    "grade": "初二",
    "subject": "物理",
    "question_type": "选择题",
    "difficulty": "中",
    "knowledge_point": ["牛顿第二定律"],
    "scenario": "课堂练习",
    "source_type": "AI生成",
    "region": "全国通用",
    "curriculum_standard": "2022版"
  },
  "score": 5,
  "order": 1
}


<!-- Page 9 -->

智卷云智能体需求点
实现方式/
模块
具体实现方式/说明
题库数据管理（多标签、分
段、导入导出）
前端本地管
理/智能体
API
题目数据、标签、分段等均由前端本地存储（如
state、localStorage），或通过智能体API动态
生成/导入/导出
题库标签体系（年级、学科、
题型等）
前端标签系
统
前端实现多标签体系，支持自定义标签字段，多
标签筛选与组合
题库检索（多标签、权重、去
重）
智能体API
前端将筛选条件直接传递给智能体API，由API返
回符合条件的题目，支持多标签、权重、去重等
参数
AI自动出题/变式题生成
智能体API
前端调用AI出题API，传递参数（标签、样题
等），由智能体生成题目和变式题
题目预览与编辑
前端交互
前端实现题目列表展示、顺序调整、分值设置、
题目增删等交互
题目分值设置/题型混排/顺序
调整
前端交互
前端支持分值编辑、题型混排、拖拽排序等功能
题目去重
智能体API
前端在请求AI生成题目时传递去重参数，或本地
对题目内容进行去重处理
AI补题（题库不足时自动生
成）
智能体API
前端判断题目数量不足时，自动调用AI接口补充
生成题目
多模板排版
（A4/A3/PDF/Word）
前端排版
+导出库
前端实现模板选择和排版参数设置，使用如
jsPDF、docx.js等库导出PDF/Word
自动分页/赋分/题型排列
前端排版算
法
前端实现自动分页、赋分、题型排列等排版逻辑
下载/打印/导出
前端导出/
打印
前端生成PDF/Word文件，支持本地下载和调用
浏览器打印功能
角色权限（教师/教研组/管理
者）
前端用户管
理（如有）
如需区分角色，可在前端实现简单的用户身份管
理和权限控制
5.7 数据迁移与兼容
 
设计时应考虑与主流题库（如学科网、猿题库等）字段的兼容，便于数据导入导出。
标签字段建议采用标准化英文命名，便于API对接和国际化。
6 补充说明
 
用户体验：界面简洁，操作流程清晰，支持Web端访问。
数据安全：用户数据、题库内容需加密存储，确保隐私安全。
后期扩展：预留API接口，便于后续对接第三方题库、打印服务等。
6.1 平台功能与智卷云智能体开发需求检索表：
 


<!-- Page 10 -->

智卷云智能体需求点
实现方式/
模块
具体实现方式/说明
场景化案例/标签（如竞赛、
生活应用）
前端标签系
统
前端支持自定义标签，题目可按场景标签筛选
题目结构（题干、选项、答
案、解析等）
前端结构化
数据
前端以结构化数据管理题目内容，支持题干、选
项、答案、解析等字段
题目数量/优先级/权重排序
智能体API/
前端排序
前端传递数量、优先级、权重等参数给API，或本
地排序
手动增删题目
前端交互
前端支持题目手动增删
模板选择/自动分页
前端排版
前端支持模板选择和自动分页功能
API对接/第三方集成
智能体API/
平台API
前端通过API与智能体或第三方平台集成
日志与追踪
前端本地日
志（如有）
如需记录操作日志，可在前端本地存储日志，或
依赖平台日志功能
数据本地化/安全
前端本地存
储/平台安
全
数据存储于前端本地或平台，注意浏览器安全和
用户隐私
7 其他细节
 
7.1 AI出题流程（细化版）
 
7.1.1 触发条件
 
用户在“输入条件”界面设置好年级、学科、知识点、题型、难度、题目数量等参数，点击“生成题
目”。
系统先检索题库，若题库返回题目数量不足，则自动触发AI出题流程。
7.1.2 AI出题参数准备
 
收集用户输入的所有标签参数（如年级=高三，学科=数学，知识点=导数，题型=选择题，难度=中
等，题目数量=5）。
统计题库已返回题目数量，计算需AI生成的题目数量（如还差2题）。
7.1.3 AI出题提示词工程
 
生成标准化Prompt，示例：
支持多轮Prompt优化（如AI生成题目不符合要求，可自动补充说明重新生成）。
请为高三数学“导数”知识点，生成2道中等难度的选择题。每道题需包含题干、4个选项、标准答案、详细
解析。题目风格需贴合高考真题，避免与以下题目重复：[已检索到的题目内容列表]。


<!-- Page 11 -->

7.1.4 AI出题接口调用
 
调用平台AI模型（如大语言模型/自定义出题API），传入Prompt和参数。
支持异步处理，防止接口超时。
7.1.5 结果结构化与校验
 
对AI返回的题目进行结构化解析（JSON格式），字段包括：题干、选项、答案、解析、标签等。
自动校验题目结构完整性（如选项数量、答案唯一性、解析是否为空等）。
对AI生成题目与题库已有题目进行相似度比对，自动去重。
7.1.6 标签自动补全
 
为AI生成题目自动打上与用户输入一致的标签（年级、学科、知识点、题型、难度、来源=AI生成
等）。
支持后续人工补充/编辑标签。
7.1.7 题目入库与展示
 
将AI生成的题目与题库检索到的题目合并，统一展示在“题目预览与编辑”界面。
支持用户对AI生成题目进行编辑、删除、调整顺序等操作。
7.1.8 日志与追踪
 
记录每次AI出题的参数、Prompt、返回内容、用户操作日志，便于后续追溯和优化。
7.1.9 异常处理
 
AI接口异常/生成内容不合规时，前端友好提示“题目生成失败，请重试或调整条件”。
支持手动补题或重新生成。
7.1.10 典型用例
 
用例1：老师选择“高三数学，导数，选择题，难度中，5题”，题库仅返回3题，AI自动生成2题，合
并展示5题。
用例2：AI生成题目结构不完整，系统自动提示并重新生成，或允许老师手动补全。
7.1.11 技术要点
 
Prompt模板可配置，支持不同学科/题型/难度的差异化出题。
支持多模型切换（如本地大模型、云端API等）。
结构化解析需兼容多种AI返回格式，支持异常兜底。
支持批量生成、并发处理，提升效率。
7.1.12 验收标准
 
AI生成题目结构完整，标签准确，内容无重复。
题目与用户输入条件高度匹配。
前端展示与编辑流畅，异常处理友好。


<!-- Page 12 -->

7.2 排版算法（细化版）
 
7.2.1 输入参数
 
题目列表（含题干、选项、答案、解析、分值、题型、顺序等结构化数据）
用户选择的输出模板（如A4/A3、单页/多页、横版/竖版、题型混排/分组等）
题目分值、题型排列、分页规则、字体字号、页边距等排版参数
7.2.2 排版流程
 
步骤1：题目分组与排序
 
按题型、难度、知识点等参数对题目进行分组（如选择题、填空题、解答题分开）
按用户设定顺序或默认顺序排列题目
步骤2：分值与题号自动赋值
 
自动为每道题编号（如1、2、3…）
按题型或题目单独显示分值（如“（每题5分）”或“本大题共20分”）
步骤3：分页与版面计算
 
根据模板纸张大小（A4/A3）、页边距、字体字号、题目内容长度，动态计算每页可容纳题目数量
支持自动分页（如一页放不下自动换页），题型/大题可强制分页（如“解答题新起一页”）
步骤4：题型混排与分组
 
支持题型混排（如选择题、填空题、解答题穿插排列）
支持题型分组（如“第Ⅰ卷 选择题”“第Ⅱ卷 非选择题”）
步骤5：答案与解析处理
 
支持“仅题目”/“题目+答案”/“题目+答案+解析”多种导出模式
答案、解析可单独分页或附在卷末
步骤6：版式美化
 
自动调整题干、选项、分值、题号的对齐与缩进
支持题目内图片、公式的自适应缩放与排版
支持页眉、页脚、学校/班级/姓名等信息自动填充
步骤7：导出与格式转换
 
支持导出为PDF、Word等格式
保证导出文件在不同设备/打印机上版式一致
7.2.3 技术要点（原生实现版）
 
排版与导出功能通过原生JavaScript、HTML和CSS实现。
可通过动态生成HTML和CSS内容，利用浏览器自带的打印功能（window.print）或原生API导出为
PDF/Word（如iframe、canvas、或手动拼接docx/xml结构）。
兼容中英文、数学公式（可用MathML或SVG）、图片等多种内容类型。
所有交互、样式和数据处理均采用原生JavaScript、HTML、CSS实现。


<!-- Page 13 -->

7.2.4 验收标准
 
题目排版美观、分组合理、分页准确、无内容丢失或错位
导出文件可直接打印，内容与预览一致
支持大批量题目（如100题）不卡顿
7.3 前端交互（细化版）
 
7.3.1 主要交互页面
 
******* 输入条件页
 
多标签选择（年级、学科、知识点、题型、难度等），支持下拉、多选、搜索、清空
题目数量、输出格式、模板等参数输入
“一键生成”按钮，参数校验与错误提示
******* 题目预览与编辑页
 
题目列表展示（题干、选项、答案、解析、标签、分值等）
拖拽排序（支持题目顺序调整，实时预览）
分值编辑（可直接输入或批量设置）
题型切换（如选择题转填空题）
增删题目（支持单题/批量删除、手动添加新题）
题目内容编辑（弹窗/内联编辑，支持富文本、图片、公式）
题目标签编辑（可补充/修改标签）
7.3.1.3 模板与排版设置页
 
模板选择（A4/A3、单页/多页、横竖版等）
排版参数设置（字体、字号、页边距、题型分组/混排、答案显示方式等）
实时排版预览（所见即所得）
7.3.1.4 导出与下载页
 
导出格式选择（PDF/Word）
下载按钮，导出进度提示
下载历史与文件管理
打印按钮，支持直接调用本地/云打印机
7.3.2 交互细节
 
所有操作均需有“撤销/重做”功能
重要操作（如删除、导出）需二次确认
支持自动保存与手动保存
支持Web端自适应布局，兼容主流浏览器，界面响应流畅。
异常与错误友好提示（如AI生成失败、导出失败等）


<!-- Page 14 -->

单选题
多选题
填空题
书写题
默写题
判断题
连线题
简答题
综合题
语言运用
课内阅读
现代文阅读
古诗词赏析
文言文阅读
对比阅读
名著阅读
应用文写作
大作文
听写
短文听力题
 
选择题
判断题
图片填空
填空题
圈涂画写
改错题
操作题
连线题
计算题
解决问题
解答题
 
听力题
仿写
仿写对话
补全对话
听写-判断题
听力-填空题
7.3.3 技术要点
 
推荐使用现代前端框架（如React/Vue），配合UI组件库（如Ant Design、Element等）
拖拽排序可用react-beautiful-dnd、SortableJS等
富文本编辑可用Slate、Quill、TinyMCE等
实时预览可用iframe或虚拟DOM渲染
文件导出与下载需与后端/排版算法联动
7.3.4 验收标准
 
所有交互流畅、无明显卡顿
题目编辑、排序、分值调整等操作实时生效
排版预览与导出内容一致
支持大批量题目操作不卡顿
兼容主流浏览器和终端
8 各学科组卷题型模块
 
8.1 小学
 
8.1.1 小学语文
 
8.1.2 小学数学
 
8.1.3 小学英语
 


<!-- Page 15 -->

听力题
仿写
仿写对话
听力-连线题
听力-排序题
听力-选词填空-句子
听力-选词填空-短文
选择题
判断题
填空题
排序题
完形填空
任务型阅读
阅读理解
选词填空-句子
选词填空-短文
短文填空
完成句子
句型转换
改错题
短文改错
连词成句
书面表达
补全对话-选择
补全对话-填空
连线题
英汉互译
匹配题
画图题
简答题
书写题
 
 
选择题
填空题
判断题
连线题
排序题
填图题
作图题
简答题
实验题
综合题
 
 
单选题
多选题
填空题
书写
默写
名著阅读
语言运用
判断题
简答题
综合题
现代文阅读
文言文阅读
古诗词赏析
作文
小作文
单选题
判断题
填空题
解答题
多选题
 
8.1.4 小学科学
 
8.2 中学
 
8.2.1 初中语文
 
8.2.2 初中数学
 


<!-- Page 16 -->

听力题
填空题
排序题
匹配题
单项选择
完形填空
阅读理解
任务型阅读
补全对话
选词填空
完成句子
词汇运用
英汉互译
连词成句
句型转换
短文填空
改错题
书面表达
阅读理解-填空
阅读理解-判断
 
单选题
双选题
多选题
不定项选择
填空题
判断题
实验题
计算题
作图题
简答题
综合应用题
 
单选题
多选题
双选题
不定项选择题
选择填充题
填空题
判断题
简答题
实验题
计算题
推断题
工业流程题
单选题
多选题
双选题
不定项选择
填空题
判断题
实验题
计算题
作图题
简答题
推断题
工业流程题
综合应用题
选择填充题
 
8.2.3 初中英语
 
8.2.4 初中物理
 
8.2.5 初中化学
 
8.2.6 初中科学
 


<!-- Page 17 -->

单选题
多选题
填空题
判断题
解答题
 
听力题
单选题
单词拼写/单词释义
单词拼写-单句
单词辩音
语法填空
完成句子
句型转换
英汉互译
首字母短文填空
阅读填空
选词填空
完形填空
阅读理解
任务型阅读
阅读六选四
阅读七选五
阅读表达
改错
书面表达
句子语法分析
其他
词汇默写
语段填空
单选题
多选题
填空题
判断题
实验题
解答题
作图题
综合题
 
单选题
多选题
双选题
不定项选择
填空题
判断题
解答题
综合题
 
单选题
不定项选择题
多选题
填空题
判断题
解答题
综合题
 
 
8.3 高中
 
8.3.1 高中数学
 
8.3.2 高中英语
 
8.3.3 高中物理
 
8.3.4 高中化学
 
8.3.5 高中生物
 


<!-- Page 18 -->

单选题
多选题
不定项选择题
判断题
填空题
辨析题
简答题
材料分析题
论述题
开放性试题
综合题
 
单选题
多选题
判断题
填空题
简答题
材料分析题
综合题
论述题
开放性试题
单选题
多选题
填空题
判断题
简答题
开放性试题
材料阅读题
 
 
8.3.6 高中政治
 
8.3.7 高中地理
 
8.3.8 高中历史
 
9 百强学校名单
 
2025年全国百强高中完整名单
 
（按省份分类，排名不分先后）
安徽省
 
1. 合肥一中
2. 马鞍山二中
3. 安徽师范大学附属中学
4. 芜湖一中
5. 合肥一六八中学
6. 淮北一中
北京市
 
1. 中国人民大学附属中学
2. 北京第四中学
3. 北京师范大学附属实验中学
4. 北京大学附属中学
5. 清华大学附属中学
6. 中央民族大学附属中学
7. 北京市第一七一中学


<!-- Page 19 -->

重庆市
 
1. 重庆南开中学
2. 重庆市巴蜀中学
3. 重庆第八中学
4. 重庆外国语学校
福建省
 
1. 福州一中
2. 厦门一中
3. 厦门双十中学
4. 泉州五中
5. 莆田一中
甘肃省
 
1. 西北师范大学附属中学
2. 兰州一中
广东省
 
1. 华南师范大学附属中学
2. 深圳中学
3. 广东实验中学
4. 广州执信中学
广西壮族自治区
 
1. 南宁二中
2. 柳州高级中学
3. 广西师范大学附属外国语学校
贵州省
 
1. 贵阳一中
2. 兴义八中
海南省
 
1. 海南中学
2. 三亚一中
河北省
 
1. 衡水中学
2. 石家庄二中
3. 保定一中
4. 定州中学


<!-- Page 20 -->

河南省
 
1. 郑州外国语学校
2. 郑州一中
3. 开封高中
4. 鹤壁高中
5. 南阳市第一中学
黑龙江省
 
1. 哈尔滨第三中学
2. 大庆实验中学
3. 齐齐哈尔实验中学
湖北省
 
1. 华中师范大学第一附属中学
2. 黄冈中学
3. 襄阳四中
4. 武汉武钢三中
5. 孝感高中
6. 黄石二中
7. 襄阳一中
湖南省
 
1. 长沙市长郡中学
2. 长沙市雅礼中学
3. 湖南师范大学附属中学
4. 长沙市第一中学
5. 衡阳八中
吉林省
 
1. 东北师范大学附属中学
2. 吉林市第一中学
3. 延边第二中学
江苏省
 
1. 南京师范大学附属中学
2. 江苏省天一中学
3. 江苏省锡山高级中学
4. 江苏省淮阴中学
5. 江苏省海门中学


<!-- Page 21 -->

江西省
 
1. 江西师范大学附属中学
2. 临川一中
3. 九江一中
4. 南昌二中
辽宁省
 
1. 东北育才中学
2. 大连市第二十四中学
3. 本溪市高级中学
4. 阜新市实验中学
内蒙古自治区
 
1. 呼和浩特二中
2. 鄂尔多斯一中
宁夏回族自治区
 
1. 银川一中
青海省
 
1. 青海湟川中学
山东省
 
1. 山东师范大学附属中学
2. 山东省实验中学
3. 青岛二中
4. 济南外国语学校
山西省
 
1. 山西大学附属中学
2. 太原五中
3. 山西省实验中学
4. 忻州一中
陕西省
 
1. 西北工业大学附属中学
2. 西安高新一中
3. 陕西师范大学附属中学


<!-- Page 22 -->

上海市
 
1. 上海中学
2. 华东师范大学第二附属中学
3. 复旦大学附属中学
4. 上海交通大学附属中学
四川省
 
1. 成都市第七中学
2. 绵阳东辰国际学校
3. 南充高中
4. 石室中学
天津市
 
1. 天津南开中学
2. 天津耀华中学


