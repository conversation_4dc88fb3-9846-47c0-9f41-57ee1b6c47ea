const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/chat-BnToNoSM.js","assets/utils-DxgFcSvi.js"])))=>i.map(i=>d[i]);
var $=Object.defineProperty,S=Object.defineProperties;var R=Object.getOwnPropertyDescriptors;var _=Object.getOwnPropertySymbols;var T=Object.prototype.hasOwnProperty,F=Object.prototype.propertyIsEnumerable;var q=(s,e,t)=>e in s?$(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t,h=(s,e)=>{for(var t in e||(e={}))T.call(e,t)&&q(s,t,e[t]);if(_)for(var t of _(e))F.call(e,t)&&q(s,t,e[t]);return s},m=(s,e)=>S(s,R(e));var d=(s,e,t)=>new Promise((o,n)=>{var a=c=>{try{l(t.next(c))}catch(r){n(r)}},i=c=>{try{l(t.throw(c))}catch(r){n(r)}},l=c=>c.done?o(c.value):Promise.resolve(c.value).then(a,i);l((t=t.apply(s,e)).next())});import{aE as O,r as y,c as b}from"./vendor-CPqkYfXn.js";import{a as Q}from"./index-BdEKvwRr.js";import{a as j}from"./utils-DxgFcSvi.js";import{a as k}from"./ui-CjjzzDsP.js";const f=j.create({baseURL:"http://ai.procaas.com:3000/api",timeout:3e4,headers:{"Content-Type":"application/json",AUTHORIZATION:"application-309a58f2282306bc0accf8dd6115a852"}});f.interceptors.request.use(s=>{const e=localStorage.getItem("token");return e&&(s.headers.Authorization=`Bearer ${e}`),s},s=>Promise.reject(s));f.interceptors.response.use(s=>{var t;if((t=s.config.url)!=null&&t.includes("/questions/search"))return s;const{data:e}=s;if(e&&e.code===200)return s.data=e.data,s;if(e.code){const o={type:"NETWORK_ERROR",message:e.message||"API Error",details:e};return k.error(o.message),Promise.reject(o)}else return s},s=>{let e;if(s.response){const t=s.response.status;switch(t){case 401:localStorage.removeItem("token"),window.location.href="/login",e={type:"NETWORK_ERROR",message:"请先登录",details:s};break;case 403:e={type:"NETWORK_ERROR",message:"权限不足",details:s};break;case 404:e={type:"NETWORK_ERROR",message:"请求的资源不存在",details:s};break;case 500:e={type:"NETWORK_ERROR",message:"服务器内部错误",details:s};break;default:e={type:"NETWORK_ERROR",message:`请求失败 (${t})`,details:s}}}else s.request?e={type:"NETWORK_ERROR",message:"网络连接失败，请检查网络连接",details:s}:e={type:"NETWORK_ERROR",message:"请求配置错误",details:s};return k.error(e.message),Promise.reject(e)});class v{static searchQuestions(e){return d(this,null,function*(){var t;try{return(yield f.post("/questions/search",{grade:e.grade,subject:e.subject,knowledgePoints:e.knowledgePoints,questionTypes:e.questionTypes,difficulty:e.difficulty,region:e.region,limit:e.questionCount,sampleId:(t=e.sampleFile)==null?void 0:t.id})).data||[]}catch(o){return console.error("Failed to search questions:",o),this.getMockQuestions(e.questionCount)}})}static generatePaper(e,t){return d(this,null,function*(){var o;try{const{ChatService:n}=yield Q(()=>d(this,null,function*(){const{ChatService:u}=yield import("./chat-BnToNoSM.js");return{ChatService:u}}),__vite__mapDeps([0,1])),a=this.buildExamplePrompt(e);console.log("Step 1: Sending example prompt to AI");const i=yield n.sendMessage(a,{onProgress:t?u=>t(u*.4):void 0,maxRetries:3});console.log("Step 1: Example response received, length:",i.length),console.log("Step 1: Example response preview:",i.substring(0,500));const l=this.buildFormData(e);console.log("Step 2: Building form data from user input:",l),yield new Promise(u=>setTimeout(u,1e3));const c=yield n.sendMessage(`现在请根据以下参数生成${e.count}道题目：

**参数说明：**
- 学科：${e.filterParams.subject}
- 年级：${e.filterParams.grade}  
- 知识点：${((o=e.filterParams.knowledgePoints)==null?void 0:o.join(", "))||"基础知识点"}
- 总题数：${e.count}题

**要求：**
1. 只返回JSON格式的题目数组
2. 不要任何表单配置或说明文字
3. 确保题目内容符合学科和年级特点
4. 每道题都要有完整的题干、选项、答案和解析`,{form_data:l,onProgress:t?u=>t(40+u*.6):void 0,maxRetries:3});console.log("Step 2: Form response received, length:",c.length),console.log("Step 2: Form response preview:",c.substring(0,1e3));const r=this.parsePaperResponse(c,e.count);return console.log("Successfully parsed",r.length,"questions"),r}catch(n){return console.error("Failed to generate paper with AI:",n),this.getMockGeneratedQuestions(e.count)}})}static buildExamplePrompt(e){return`你是一个专业的试卷生成助手。请严格按照以下步骤帮我生成试卷：

**第一步：理解格式要求**
我需要你生成标准的JSON格式题目，不要返回任何表单配置或界面元素。

**JSON格式要求：**
\`\`\`json
[
  {
    "stem": "题干内容",
    "options": ["选项A", "选项B", "选项C", "选项D"],
    "answer": "正确答案",
    "explanation": "详细解析",
    "questionType": "single_choice",
    "difficulty": "medium",
    "knowledgePoint": ["知识点1", "知识点2"],
    "score": 5
  }
]
\`\`\`

**重要提醒：**
1. 不要返回任何表单配置、HTML标签或界面元素
2. 不要询问用户选择题目类型或数量
3. 直接根据我提供的参数生成题目
4. 只返回JSON格式的题目数组
5. 每道题必须包含：题干、选项、答案、解析

**题目类型说明：**
- single_choice: 单选题
- multiple_choice: 多选题  
- true_false: 判断题
- fill_blank: 填空题
- short_answer: 简答题

**难度级别：**
- easy: 简单
- medium: 中等
- hard: 困难

请确认你理解以上要求，我将立即提供具体的生成参数。`}static buildFormData(e){var p,g;const{filterParams:t,count:o}=e,n=t.questionTypes||[];let a=0,i=0,l=0,c=0,r=0;n.includes("single_choice")&&(a=Math.floor(o*.4)),n.includes("multiple_choice")&&(i=Math.floor(o*.2)),n.includes("true_false")&&(l=Math.floor(o*.1)),n.includes("fill_blank")&&(c=Math.floor(o*.15)),n.includes("short_answer")&&(r=Math.floor(o*.15));const u=a+i+l+c+r,w=o-u;return w>0&&(a+=w),{subject:t.subject||"数学",grade:t.grade||"初一",知识点:((p=t.knowledgePoints)==null?void 0:p.join(", "))||"基础知识点",single_choice_count:a,multiple_choice_count:i,true_false_count:l,fill_blank_count:c,short_answer_count:r,difficulty:((g=t.difficulty)==null?void 0:g.join(", "))||"easy,medium",教材:t.region||"通用教材",上下册:"上册",total_count:o,instruction:"请根据以上参数直接生成题目，不要返回表单配置"}}static parsePaperResponse(e,t){try{if(console.log("Raw AI response for parsing:",e.substring(0,500)+"..."),!e||e.trim().length===0)return console.warn("Empty AI response received"),this.getMockGeneratedQuestions(t);let o=e;const n=e.match(/```json\n([\s\S]*?)\n```/);if(n)o=n[1],console.log("Found JSON code block format");else if(e.trim().startsWith("[")){const r=e.match(/\[[\s\S]*\]/);r&&(o=r[0],console.log("Found direct JSON array format"))}else if(e.trim().startsWith("{")){const r=e.match(/\{[\s\S]*\}/);r&&(o=r[0],console.log("Found direct JSON object format"))}else{const r=e.match(/\[[\s\S]*?\]/)||e.match(/\{[\s\S]*?\}/);r&&(o=r[0],console.log("Found embedded JSON format"))}console.log("Extracted JSON string:",o.substring(0,200)+"..."),o=o.trim(),o=this.fixJsonFormat(o);const a=JSON.parse(o);let i=[];Array.isArray(a)?i=a:a.questions&&Array.isArray(a.questions)?i=a.questions:a.data&&Array.isArray(a.data)?i=a.data:a.items&&Array.isArray(a.items)?i=a.items:i=[a],console.log(`Found ${i.length} questions in response`);const l=i.filter(r=>r&&(r.stem||r.question||r.content));return console.log(`Valid questions: ${l.length} out of ${i.length}`),l.map((r,u)=>this.formatQuestionFromAI(r,u)).slice(0,t)}catch(o){console.error("Failed to parse AI paper response:",o),console.error("Response sample:",e.substring(0,1e3));const n=this.extractQuestionsFromText(e,t);return n.length>0?(console.log(`Extracted ${n.length} questions from text`),n):(console.warn("Using mock questions as fallback"),this.getMockGeneratedQuestions(t))}}static formatQuestionFromAI(e,t){console.log(`Formatting question ${t+1}:`,JSON.stringify(e,null,2));try{let o;e.options?Array.isArray(e.options)?o=e.options:typeof e.options=="string"&&(o=e.options.split(`
`).filter(l=>l.trim())):e.choices&&(Array.isArray(e.choices)?o=e.choices.map((l,c)=>`选项${String.fromCharCode(65+c)}：${l}`):typeof e.choices=="string"&&(o=e.choices.split(`
`).filter(l=>l.trim())));let n=[];e.knowledgePoint?Array.isArray(e.knowledgePoint)?n=e.knowledgePoint:typeof e.knowledgePoint=="string"&&(n=[e.knowledgePoint]):e.knowledgePoints?Array.isArray(e.knowledgePoints)?n=e.knowledgePoints:typeof e.knowledgePoints=="string"&&(n=[e.knowledgePoints]):n=["AI组卷"];let a="single_choice";e.questionType||e.type?a=e.questionType||e.type:o&&o.length>0?e.multiple_answer||e.allowMultiple?a="multiple_choice":a="single_choice":e.true_false!==void 0?a="true_false":a="short_answer";const i={id:e.id||`ai-paper-${Date.now()}-${t}`,content:{stem:e.stem||e.question||e.content||e.title||"题目内容",options:o,answer:e.answer||e.correctAnswer||e.rightAnswer||"参考答案",explanation:e.explanation||e.analysis||e.solution||e.explain||"解析内容",attachments:e.attachments||[]},tags:{grade:e.grade||e.level||"grade9",subject:e.subject||"综合",questionType:a,difficulty:e.difficulty||e.level||"medium",knowledgePoint:n,scenario:e.scenario||"智能组卷",sourceType:"AI组卷"},score:e.score||e.points||5,order:e.order||e.sequence||t+1};return console.log(`Formatted question ${t+1}:`,i),i}catch(o){return console.error(`Error formatting question ${t+1}:`,o),console.error("Question item:",e),{id:`ai-error-${Date.now()}-${t}`,content:{stem:`题目 ${t+1} (格式化错误)`,options:void 0,answer:"答案",explanation:"解析",attachments:[]},tags:{grade:"grade9",subject:"综合",questionType:"single_choice",difficulty:"medium",knowledgePoint:["AI组卷"],scenario:"智能组卷",sourceType:"AI组卷"},score:5,order:t+1}}}static fixJsonFormat(e){let t=e.trim();return t=t.replace(/\/\*[\s\S]*?\*\//g,""),t=t.replace(/\/\/.*$/gm,""),t=t.replace(/,(\s*[}\]])/g,"$1"),t=t.replace(/'([^']*?)'/g,'"$1"'),t=t.replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)(\s*):/g,'$1"$2"$3:'),t}static extractQuestionsFromText(e,t){const o=[],n=[new RegExp("(\\d+)[\\.\\s、]*(.+?)(?=\\n\\d+[\\.\\s、]*|\\n选项|\\n答案|\\n解析|$)","gs"),new RegExp("第(\\d+)题[\\s：]*(.+?)(?=第\\d+题|\\n选项|\\n答案|\\n解析|$)","gs"),new RegExp("【题目】[\\s：]*(.+?)(?=【题目】|【选项】|【答案】|【解析】|$)","gs")];for(const a of n){const i=e.matchAll(a);let l=0;for(const c of i){if(l>=t)break;const r=c[2]||c[1];r&&r.length>10&&(o.push({id:`extracted-${Date.now()}-${l}`,content:{stem:r.trim(),options:void 0,answer:"待解析",explanation:"待解析",attachments:[]},tags:{grade:"grade9",subject:"综合",questionType:"short_answer",difficulty:"medium",knowledgePoint:["文本提取"],scenario:"智能组卷",sourceType:"AI组卷"},score:5,order:l+1}),l++)}if(o.length>0)break}return o.slice(0,t)}static uploadSample(e,t){return d(this,null,function*(){try{const o=new FormData;return o.append("file",e),(yield f.post("/samples/upload",o,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:a=>{if(a.total&&t){const i=Math.round(a.loaded*100/a.total);t(i)}}})).data}catch(o){return console.error("Failed to upload sample:",o),{id:"mock-sample-"+Date.now(),filename:e.name,size:e.size,uploadTime:new Date().toISOString(),status:"completed"}}})}static getSampleInfo(e){return d(this,null,function*(){try{return yield f.get(`/samples/${e}`)}catch(t){throw console.error("Failed to get sample info:",t),t}})}static createQuestion(e){return d(this,null,function*(){try{return(yield f.post("/questions",e)).data}catch(t){return console.error("Failed to create question:",t),console.warn("API not available, simulating successful creation for development"),m(h({},e),{id:`question-${Date.now()}`})}})}static updateQuestion(e,t){return d(this,null,function*(){try{return(yield f.put(`/questions/${e}`,t)).data}catch(o){return console.error("Failed to update question:",o),console.warn("API not available, simulating successful update for development"),m(h({},t),{id:e})}})}static deleteQuestion(e){return d(this,null,function*(){try{yield f.delete(`/questions/${e}`)}catch(t){return console.error("Failed to delete question:",t),console.warn("API not available, simulating successful deletion for development"),Promise.resolve()}})}static batchUpdateQuestions(e){return d(this,null,function*(){try{return(yield f.post("/questions/batch-update",{updates:e})).data}catch(t){return console.error("Failed to batch update questions:",t),console.warn("API not available, simulating successful batch update for development"),e.map(o=>m(h({},o.updates),{id:o.id}))}})}static getQuestionStats(e){return d(this,null,function*(){try{return yield f.post("/questions/stats",e)}catch(t){return console.error("Failed to get question stats:",t),{totalCount:0,byType:{},byDifficulty:{}}}})}static getMockQuestions(e){const t=[],o=["single_choice","multiple_choice","fill_blank","short_answer"],n=["easy","medium","hard"],a=["数学","语文","英语","物理","化学"];for(let i=1;i<=e;i++){const l=o[Math.floor(Math.random()*o.length)],c=n[Math.floor(Math.random()*n.length)],r=a[Math.floor(Math.random()*a.length)];t.push({id:`mock-question-${i}`,content:{stem:`这是第${i}道${r}题目，测试题干内容。请根据题目要求选择正确的答案。`,options:l.includes("choice")?["选项A：这是第一个选项","选项B：这是第二个选项","选项C：这是第三个选项","选项D：这是第四个选项"]:void 0,answer:l.includes("choice")?"A":"参考答案内容",explanation:`这是第${i}题的详细解析。解题思路包括：1. 分析题意 2. 应用相关知识点 3. 得出正确答案。`,attachments:[]},tags:{grade:"grade9",subject:r,questionType:l,difficulty:c,knowledgePoint:[`${r}基础知识`,`${r}应用能力`],scenario:"基础练习",sourceType:"题库"},score:Math.floor(Math.random()*10)+1,order:i})}return t}static getMockGeneratedQuestions(e){return this.getMockQuestions(e).map(o=>m(h({},o),{tags:m(h({},o.tags),{sourceType:"AI生成"})}))}static testTwoStepFormCollection(){return d(this,null,function*(){var t;console.log("🧪 开始测试两步式表单收集流程...");const e={filterParams:{grade:"初一",subject:"数学",knowledgePoints:["基础知识点"],questionTypes:["single_choice","multiple_choice","true_false","fill_blank","short_answer"],difficulty:["easy","medium"],region:"通用教材",questionCount:10},count:10};console.log("📋 测试参数：",e);try{const o=this.buildExamplePrompt(e);console.log("📝 第一步示例提示词："),console.log(o);const n=this.buildFormData(e);console.log("📋 第二步表单数据："),console.log(JSON.stringify(n,null,2));const a=`现在请根据以下参数生成${e.count}道题目：

**参数说明：**
- 学科：${e.filterParams.subject}
- 年级：${e.filterParams.grade}  
- 知识点：${((t=e.filterParams.knowledgePoints)==null?void 0:t.join(", "))||"基础知识点"}
- 总题数：${e.count}题

**要求：**
1. 只返回JSON格式的题目数组
2. 不要任何表单配置或说明文字
3. 确保题目内容符合学科和年级特点
4. 每道题都要有完整的题干、选项、答案和解析`;console.log("📄 第二步消息内容："),console.log(a),console.log("✅ 两步式表单收集流程测试完成"),console.log("📊 关键改进："),console.log("   ✅ 明确告诉AI不要返回表单配置"),console.log("   ✅ 分离格式说明和参数提交"),console.log("   ✅ 在表单数据中加入明确指示"),console.log("   ✅ 优化题型分布计算")}catch(o){throw console.error("❌ 测试失败：",o),o}})}}const C=O("filter",()=>{const s=y({grade:"",subject:"",knowledgePoints:[],questionTypes:[],difficulty:[],region:"",questionCount:10,sampleFile:void 0}),e=y([]),t=y(!1),o=y(null),n=y(0),a=b(()=>e.value.length>0),i=b(()=>e.value.length),l=p=>{s.value=h(h({},s.value),p)},c=p=>d(void 0,null,function*(){try{t.value=!0,o.value=null;const g=p||s.value,P=yield v.searchQuestions(g);e.value=P}catch(g){o.value=g instanceof Error?g.message:"Failed to fetch questions",e.value=[]}finally{t.value=!1}}),r=p=>d(void 0,null,function*(){try{t.value=!0,o.value=null,n.value=0;const g=p||s.value,P=yield v.generatePaper({filterParams:g,count:g.questionCount},A=>{n.value=A});e.value=P,n.value=100}catch(g){o.value=g instanceof Error?g.message:"AI组卷失败",e.value=[]}finally{t.value=!1,setTimeout(()=>{n.value=0},1e3)}}),u=()=>{e.value=[],o.value=null};return{filterParams:s,questions:e,loading:t,error:o,progress:n,hasQuestions:a,questionCount:i,updateFilterParams:l,fetchQuestions:c,generatePaperWithAI:r,clearQuestions:u,resetFilter:()=>{s.value={grade:"",subject:"",knowledgePoints:[],questionTypes:[],difficulty:[],region:"",questionCount:10,sampleFile:void 0},u()}}});export{v as Q,C as u};
