{"root": ["./src/main.ts", "./src/vite-env.d.ts", "./src/router/index.ts", "./src/services/api.ts", "./src/services/chat.ts", "./src/services/export.ts", "./src/services/question.ts", "./src/services/test-error-handling.ts", "./src/stores/filter.ts", "./src/types/index.ts", "./src/utils/chinese-canvas.ts", "./src/utils/chinese-font-data.ts", "./src/utils/font.ts", "./src/utils/html-to-pdf.ts", "./src/utils/index.ts", "./src/utils/real-chinese-font.ts", "./src/utils/test-chinese-export.ts", "./src/app.vue", "./src/components/helloworld.vue", "./src/components/forms/gradeselect.vue", "./src/components/forms/knowledgepointselect.vue", "./src/components/forms/questiontypedifficultyselect.vue", "./src/components/forms/regioncountselect.vue", "./src/components/forms/sampleupload.vue", "./src/components/forms/subjectselect.vue", "./src/components/ui/exportpreview.vue", "./src/components/ui/questioneditor.vue", "./src/components/ui/questionlist.vue", "./src/components/ui/questionpreview.vue", "./src/views/testexport.vue", "./src/views/export/index.vue", "./src/views/paperlayout/index.vue", "./src/views/questionedit/index.vue", "./src/views/questionfilter/index.vue"], "errors": true, "version": "5.8.3"}