<template>
  <div class="paper-layout">
    <div class="layout-container">
      <!-- Left Panel: Layout Settings -->
      <div class="settings-panel">
        <el-card>
          <template #header>
            <span>排版设置</span>
          </template>
          
          <div class="settings-content">
            <el-form :model="layoutParams" label-width="80px">
              <!-- Paper Basic Info -->
              <el-form-item label="试卷标题">
                <el-input
                  v-model="paperTitle"
                  placeholder="请输入试卷标题"
                  maxlength="50"
                  show-word-limit
                  style="width: 100%"
                />
                <div class="quick-titles">
                  <el-button 
                    size="small" 
                    text 
                    @click="paperTitle = '期末考试试卷'"
                  >
                    期末考试
                  </el-button>
                  <el-button 
                    size="small" 
                    text 
                    @click="paperTitle = '期中考试试卷'"
                  >
                    期中考试
                  </el-button>
                  <el-button 
                    size="small" 
                    text 
                    @click="paperTitle = '单元测试试卷'"
                  >
                    单元测试
                  </el-button>
                  <el-button 
                    size="small" 
                    text 
                    @click="paperTitle = '模拟考试试卷'"
                  >
                    模拟考试
                  </el-button>
                  <el-button 
                    size="small" 
                    text 
                    @click="paperTitle = '月考试卷'"
                  >
                    月考
                  </el-button>
                  <el-button 
                    size="small" 
                    text 
                    @click="paperTitle = '练习题'"
                  >
                    练习题
                  </el-button>
                </div>
              </el-form-item>
              
              <el-form-item label="考试时长">
                <el-input-number
                  v-model="examDuration"
                  :min="30"
                  :max="300"
                  :step="15"
                  controls-position="right"
                  style="width: 100%"
                />
                <div class="input-label">分钟</div>
              </el-form-item>
              
              <!-- Paper Size and Orientation -->
              <el-form-item label="纸张大小">
                <el-select v-model="layoutParams.paperSize" style="width: 100%">
                  <el-option label="A4" value="A4" />
                  <el-option label="A3" value="A3" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="纸张方向">
                <el-radio-group v-model="layoutParams.orientation">
                  <el-radio value="portrait">纵向</el-radio>
                  <el-radio value="landscape">横向</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <!-- Font Settings -->
              <el-form-item label="正文字体">
                <el-slider
                  v-model="layoutParams.fontSize"
                  :min="10"
                  :max="24"
                  :step="1"
                  show-input
                  style="width: 100%"
                />
                <div class="input-label">{{ layoutParams.fontSize }}px (正文字体大小)</div>
              </el-form-item>
              
              <el-form-item label="标题字体">
                <el-slider
                  v-model="layoutParams.titleFontSize"
                  :min="16"
                  :max="32"
                  :step="1"
                  show-input
                  style="width: 100%"
                />
                <div class="input-label">{{ layoutParams.titleFontSize }}px (标题字体大小)</div>
              </el-form-item>
              
              <el-form-item label="行距">
                <el-slider
                  v-model="layoutParams.lineHeight"
                  :min="1.0"
                  :max="2.5"
                  :step="0.1"
                  show-input
                  style="width: 100%"
                />
              </el-form-item>
              
              <!-- Margins -->
              <el-form-item label="页边距">
                <el-row :gutter="8">
                  <el-col :span="12">
                    <el-input-number 
                      v-model="layoutParams.margin.top" 
                      :min="10" 
                      :max="50"
                      size="small"
                      controls-position="right"
                    />
                    <div class="margin-label">上边距(mm)</div>
                  </el-col>
                  <el-col :span="12">
                    <el-input-number 
                      v-model="layoutParams.margin.bottom"
                      :min="10" 
                      :max="50"
                      size="small"
                      controls-position="right"
                    />
                    <div class="margin-label">下边距(mm)</div>
                  </el-col>
                </el-row>
                <el-row :gutter="8" style="margin-top: 8px">
                  <el-col :span="12">
                    <el-input-number 
                      v-model="layoutParams.margin.left"
                      :min="10" 
                      :max="50"
                      size="small"
                      controls-position="right"
                    />
                    <div class="margin-label">左边距(mm)</div>
                  </el-col>
                  <el-col :span="12">
                    <el-input-number 
                      v-model="layoutParams.margin.right"
                      :min="10" 
                      :max="50"
                      size="small"
                      controls-position="right"
                    />
                    <div class="margin-label">右边距(mm)</div>
                  </el-col>
                </el-row>
              </el-form-item>
              
              <!-- Question Grouping -->
              <el-form-item label="题型排列">
                <el-radio-group v-model="layoutParams.questionGrouping">
                  <el-radio value="byType">按题型分组</el-radio>
                  <el-radio value="mixed">混合排列</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <!-- Answer Display -->
              <el-form-item label="答案显示">
                <el-switch
                  v-model="layoutParams.showAnswer"
                  active-text="显示答案"
                  inactive-text="隐藏答案"
                />
              </el-form-item>
              
              <el-form-item label="解析显示">
                <el-switch
                  v-model="layoutParams.showExplanation"
                  active-text="显示解析"
                  inactive-text="隐藏解析"
                />
              </el-form-item>
              
              <!-- Pagination Settings -->
              <el-form-item label="每页题数">
                <el-select 
                  v-model="questionsPerPage" 
                  @change="(value: number) => updateQuestionsPerPage(value)"
                  style="width: 100%"
                >
                  <el-option label="3题/页" :value="3" />
                  <el-option label="4题/页" :value="4" />
                  <el-option label="5题/页" :value="5" />
                  <el-option label="6题/页" :value="6" />
                  <el-option label="8题/页" :value="8" />
                  <el-option label="10题/页" :value="10" />
                  <el-option label="全部题目" :value="999" />
                </el-select>
              </el-form-item>
              
              <!-- Page Navigation -->
              <el-form-item label="跳转页面" v-if="estimatedPages > 1">
                <el-select 
                  v-model="currentPage" 
                  @change="(value: number) => goToPage(value)"
                  style="width: 100%"
                >
                  <el-option 
                    v-for="page in estimatedPages" 
                    :key="page"
                    :label="`第${page}页`" 
                    :value="page" 
                  />
                </el-select>
              </el-form-item>
              
              <!-- Actions -->
              <el-form-item>
                <el-button @click="handleResetLayout" style="width: 100%">
                  重置设置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
        
        <!-- Paper Statistics -->
        <el-card style="margin-top: 16px">
          <template #header>
            <span>试卷信息</span>
          </template>
          
          <div class="paper-stats">
            <el-descriptions :column="1" size="small">
              <el-descriptions-item label="题目总数">{{ questions.length }}</el-descriptions-item>
              <el-descriptions-item label="总分">{{ totalScore }}</el-descriptions-item>
              <el-descriptions-item label="总页数">{{ estimatedPages }}</el-descriptions-item>
              <el-descriptions-item label="当前页" v-if="estimatedPages > 1">{{ currentPage }} / {{ estimatedPages }}</el-descriptions-item>
              <el-descriptions-item label="题型分布">
                <div class="type-distribution">
                  <el-tag 
                    v-for="(count, type) in questionTypeStats" 
                    :key="type"
                    size="small"
                    style="margin: 2px"
                  >
                    {{ getQuestionTypeLabel(type) }}: {{ count }}
                  </el-tag>
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </div>
      
      <!-- Right Panel: Preview -->
      <div class="preview-panel">
        <el-card>
          <template #header>
            <div class="preview-header">
              <span>试卷预览</span>
              <div class="preview-actions">
                <!-- Pagination Controls -->
                <div v-if="estimatedPages > 1" class="pagination-controls">
                  <el-button-group size="small">
                    <el-button @click="prevPage" :disabled="currentPage === 1">
                      上一页
                    </el-button>
                    <el-button disabled>
                      {{ currentPage }} / {{ estimatedPages }}
                    </el-button>
                    <el-button @click="nextPage" :disabled="currentPage === estimatedPages">
                      下一页
                    </el-button>
                  </el-button-group>
                </div>
                
                <!-- Zoom Controls -->
                <el-button-group>
                  <el-button size="small" @click="zoomOut" :disabled="zoomLevel <= 0.5">
                    <el-icon><ZoomOut /></el-icon>
                  </el-button>
                  <el-button size="small" @click="resetZoom">
                    {{ (zoomLevel * 100).toFixed(0) }}%
                  </el-button>
                  <el-button size="small" @click="zoomIn" :disabled="zoomLevel >= 2">
                    <el-icon><ZoomIn /></el-icon>
                  </el-button>
                </el-button-group>
                <el-button size="small" type="primary" @click="handleExport">
                  <el-icon><Download /></el-icon>
                  导出试卷
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="preview-content">
            <div 
              class="paper-preview" 
              :style="previewStyle"
              ref="previewRef"
            >
              <!-- Paper Header - Only show on first page -->
              <div class="paper-header" v-if="currentPage === 1">
                <h1 class="paper-title">{{ paperTitle }}</h1>
                <div class="paper-info">
                  <div class="info-row">
                    <span>姓名：__________</span>
                    <span>班级：__________</span>
                    <span>学号：__________</span>
                  </div>
                  <div class="info-row">
                    <span>学科：{{ paperSubject }}</span>
                    <span>总分：{{ totalScore }}分</span>
                    <span>时间：{{ examDuration }}分钟</span>
                  </div>
                  <div class="info-row">
                    <span>题目数：{{ questions.length }}题</span>
                    <span v-if="estimatedPages > 1">第{{ currentPage }}页 / 共{{ estimatedPages }}页</span>
                    <span v-if="estimatedPages > 1">当前页：{{ currentPageQuestions.length }}题</span>
                  </div>
                </div>
              </div>
              
              <!-- Page header for non-first pages -->
              <div class="page-header" v-if="currentPage > 1 && estimatedPages > 1">
                <div class="page-info">
                  <span>{{ paperTitle }}</span>
                  <span>第{{ currentPage }}页 / 共{{ estimatedPages }}页</span>
                </div>
              </div>
              
              <!-- Questions Content -->
              <div class="questions-content">
                <div v-if="layoutParams.questionGrouping === 'byType'">
                  <div 
                    v-for="(group, type) in currentPageGroupedQuestions" 
                    :key="type"
                    class="question-group"
                  >
                    <h3 class="group-title">
                      {{ getQuestionTypeLabel(type) }}
                      <span class="group-info">(共{{ group.length }}题，每题{{ group[0]?.score }}分)</span>
                    </h3>
                    
                    <div 
                      v-for="question in group" 
                      :key="question.id"
                      class="question-item"
                    >
                      <QuestionPreview
                        :question="question"
                        :number="getQuestionNumber(question.id)"
                        :show-answer="layoutParams.showAnswer"
                        :show-explanation="layoutParams.showExplanation"
                        :font-size="layoutParams.fontSize"
                        :line-height="layoutParams.lineHeight"
                      />
                    </div>
                  </div>
                </div>
                
                <div v-else>
                  <div 
                    v-for="question in currentPageQuestions" 
                    :key="question.id"
                    class="question-item"
                  >
                    <QuestionPreview
                      :question="question"
                      :number="getQuestionNumber(question.id)"
                      :show-answer="layoutParams.showAnswer"
                      :show-explanation="layoutParams.showExplanation"
                      :font-size="layoutParams.fontSize"
                      :line-height="layoutParams.lineHeight"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    
    <!-- Export Dialog -->
    <el-dialog
      v-model="exportDialogVisible"
      title="导出试卷"
      width="40%"
    >
      <div class="export-options">
        <el-form :model="exportOptions" label-width="100px">
          <el-form-item label="导出格式">
            <el-radio-group v-model="exportOptions.format">
              <el-radio value="pdf">PDF格式</el-radio>
              <el-radio value="word">Word格式</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="文件名">
            <el-input 
              v-model="exportOptions.filename" 
              placeholder="请输入文件名"
            />
          </el-form-item>
          
          <el-form-item label="包含内容">
            <el-checkbox-group v-model="exportOptions.includes">
              <el-checkbox value="questions">题目</el-checkbox>
              <el-checkbox value="answers">答案</el-checkbox>
              <el-checkbox value="explanations">解析</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirmExport"
          :loading="exporting"
        >
          确定导出
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ZoomIn, ZoomOut, Download } from '@element-plus/icons-vue'
import { useFilterStore } from '@/stores/filter'
import type { Question, LayoutParams } from '@/types'

// Import components
import QuestionPreview from '@/components/ui/QuestionPreview.vue'

const filterStore = useFilterStore()
const router = useRouter()

// State
const questions = ref<Question[]>([])
const layoutParams = ref<LayoutParams>({
  paperSize: 'A4',
  orientation: 'portrait',
  fontSize: 14,
  titleFontSize: 24,
  lineHeight: 1.5,
  margin: {
    top: 25,
    right: 20,
    bottom: 25,
    left: 20
  },
  showAnswer: false,
  showExplanation: false,
  questionGrouping: 'byType'
})

const zoomLevel = ref(1)
const previewRef = ref<HTMLElement>()
const exportDialogVisible = ref(false)
const exporting = ref(false)

// Pagination state
const currentPage = ref(1)
const questionsPerPage = ref(6) // Default questions per page

// Paper settings
const paperTitle = ref('期末考试试卷')
const examDuration = ref(120)

// Export options
const exportOptions = ref({
  format: 'pdf',
  filename: '试卷',
  includes: ['questions']
})

// Computed properties
const totalScore = computed(() => {
  return questions.value.reduce((total, question) => total + question.score, 0)
})

const questionTypeStats = computed(() => {
  const stats: Record<string, number> = {}
  questions.value.forEach(question => {
    const type = question.tags.questionType
    stats[type] = (stats[type] || 0) + 1
  })
  return stats
})

const estimatedPages = computed(() => {
  // Calculate total pages based on questionsPerPage
  return Math.max(1, Math.ceil(questions.value.length / questionsPerPage.value))
})

const currentPageQuestions = computed(() => {
  const start = (currentPage.value - 1) * questionsPerPage.value
  const end = start + questionsPerPage.value
  return questions.value.slice(start, end)
})

const currentPageGroupedQuestions = computed(() => {
  const groups: Record<string, Question[]> = {}
  currentPageQuestions.value.forEach(question => {
    const type = question.tags.questionType
    if (!groups[type]) {
      groups[type] = []
    }
    groups[type].push(question)
  })
  return groups
})

// Get subject info from questions
const paperSubject = computed(() => {
  if (questions.value.length > 0) {
    const subjects = [...new Set(questions.value.map(q => q.tags.subject))]
    return subjects.length === 1 ? subjects[0] : '综合'
  }
  return '数学' // default subject
})

const previewStyle = computed(() => {
  const { paperSize, orientation, margin, fontSize, titleFontSize, lineHeight } = layoutParams.value
  
  let width = paperSize === 'A4' ? 210 : 297  // mm
  let height = paperSize === 'A4' ? 297 : 420 // mm
  
  if (orientation === 'landscape') {
    [width, height] = [height, width]
  }
  
  // Convert to px for preview (assuming 96 DPI)
  const scale = 3.779527559  // px per mm at 96 DPI
  const scaledWidth = width * scale * zoomLevel.value
  const scaledHeight = height * scale * zoomLevel.value
  
  return {
    width: `${scaledWidth}px`,
    minHeight: `${scaledHeight}px`,
    padding: `${margin.top * scale * zoomLevel.value}px ${margin.right * scale * zoomLevel.value}px ${margin.bottom * scale * zoomLevel.value}px ${margin.left * scale * zoomLevel.value}px`,
    fontSize: `${fontSize * zoomLevel.value}px`,
    lineHeight: lineHeight,
    transform: `scale(${zoomLevel.value})`,
    transformOrigin: 'top left',
    '--title-font-size': `${titleFontSize * zoomLevel.value}px`,
    '--content-font-size': `${fontSize * zoomLevel.value}px`
  }
})

// Question type labels
const questionTypeLabels: Record<string, string> = {
  'single_choice': '单选题',
  'multiple_choice': '多选题',
  'true_false': '判断题',
  'fill_blank': '填空题',
  'short_answer': '简答题',
  'essay': '解答题',
  'calculation': '计算题',
  'application': '应用题',
  'analysis': '分析题',
  'comprehensive': '综合题'
}

const getQuestionTypeLabel = (type: string) => questionTypeLabels[type] || type

const getQuestionNumber = (questionId: string) => {
  return questions.value.findIndex(q => q.id === questionId) + 1
}

// Methods
const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value = Math.min(2, zoomLevel.value + 0.1)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1)
  }
}

const resetZoom = () => {
  zoomLevel.value = 1
}

// Pagination methods
const goToPage = (page: number) => {
  if (page >= 1 && page <= estimatedPages.value) {
    currentPage.value = page
  }
}

const nextPage = () => {
  if (currentPage.value < estimatedPages.value) {
    currentPage.value++
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const updateQuestionsPerPage = (count: number) => {
  questionsPerPage.value = count
  // Reset to first page when changing questions per page
  currentPage.value = 1
}

const handleResetLayout = () => {
  layoutParams.value = {
    paperSize: 'A4',
    orientation: 'portrait',
    fontSize: 14,
    titleFontSize: 24,
    lineHeight: 1.5,
    margin: {
      top: 25,
      right: 20,
      bottom: 25,
      left: 20
    },
    showAnswer: false,
    showExplanation: false,
    questionGrouping: 'byType'
  }
  // Reset paper title and exam duration as well
  paperTitle.value = '期末考试试卷'
  examDuration.value = 120
  ElMessage.success('排版设置已重置')
}

const handleExport = () => {
  if (questions.value.length === 0) {
    ElMessage.warning('没有题目可以导出')
    return
  }
  
  exportOptions.value.filename = `${paperTitle.value}_${new Date().toISOString().slice(0, 10)}`
  exportDialogVisible.value = true
}

const handleConfirmExport = async () => {
  try {
    exporting.value = true
    
    // Navigate to export page
    router.push('/export')
    
    ElMessage.success('跳转到导出页面')
    exportDialogVisible.value = false
  } catch (error) {
    console.error('Navigation failed:', error)
    ElMessage.error('跳转失败，请重试')
  } finally {
    exporting.value = false
  }
}

// Initialize with questions from filter store
onMounted(() => {
  if (filterStore.questions.length > 0) {
    questions.value = [...filterStore.questions]
  }
})

// Watch for changes from filter store
watch(
  () => filterStore.questions,
  (newQuestions) => {
    if (newQuestions.length > 0) {
      questions.value = [...newQuestions]
    }
  },
  { deep: true }
)
</script>

<style scoped>
.paper-layout {
  height: 100vh;
  background-color: var(--el-fill-color-extra-light);
}

.layout-container {
  display: flex;
  height: 100vh;
  gap: 16px;
  padding: 16px;
}

.settings-panel {
  width: 320px;
  flex-shrink: 0;
}

.settings-content {
  max-height: 70vh;
  overflow-y: auto;
}

.margin-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  text-align: center;
  margin-top: 4px;
}

.input-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  text-align: center;
  margin-top: 4px;
}

.quick-titles {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
  justify-content: flex-start;
}

.quick-titles .el-button {
  padding: 2px 8px;
  font-size: 11px;
  height: auto;
  min-height: 24px;
}

.paper-stats {
  padding: 16px 0;
}

.type-distribution {
  line-height: 1.8;
}

.preview-panel {
  flex: 1;
  min-width: 0;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.pagination-controls {
  display: flex;
  align-items: center;
}

.preview-content {
  height: calc(100vh - 180px);
  overflow: auto;
  padding: 20px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  position: relative;
}

.preview-content::-webkit-scrollbar {
  width: 8px;
}

.preview-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.preview-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.preview-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.paper-preview {
  background-color: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  max-width: 100%;
}

.paper-header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ddd;
  font-size: var(--content-font-size, 14px);
  color: #666;
}

.page-info {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.page-info span:first-child {
  font-weight: bold;
  color: #333;
}

.paper-title {
  font-size: var(--title-font-size, 24px);
  font-weight: bold;
  margin: 0 0 16px 0;
  color: #333;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.paper-info {
  font-size: var(--content-font-size, 14px);
  color: #666;
  overflow: hidden;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin: 8px 0;
  flex-wrap: wrap;
  gap: 8px;
}

.questions-content {
  margin-top: 20px;
  overflow: hidden;
  word-wrap: break-word;
  font-size: var(--content-font-size, 14px);
}

/* Adjust margin when no paper header (non-first pages) */
.page-header + .questions-content {
  margin-top: 15px;
}

.question-group {
  margin-bottom: 30px;
  overflow: hidden;
}

.group-title {
  font-size: calc(var(--content-font-size, 14px) * 1.3);
  font-weight: bold;
  margin: 0 0 16px 0;
  color: #333;
  border-bottom: 1px solid #ddd;
  padding-bottom: 8px;
  overflow: hidden;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.group-info {
  font-size: var(--content-font-size, 14px);
  font-weight: normal;
  color: #666;
  overflow: hidden;
  word-wrap: break-word;
}

.question-item {
  margin-bottom: 20px;
  page-break-inside: avoid;
  overflow: hidden;
  word-wrap: break-word;
  font-size: var(--content-font-size, 14px);
}

.export-options {
  padding: 20px 0;
}

/* Print styles */
@media print {
  .paper-preview {
    box-shadow: none;
    margin: 0;
    transform: none !important;
    overflow: visible;
  }
  
  .question-item {
    break-inside: avoid;
  }
  
  .page-header {
    break-after: avoid;
  }
  
  * {
    overflow: visible !important;
  }
}

/* Global text overflow prevention */
.paper-preview * {
  box-sizing: border-box;
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
}

.paper-preview img {
  max-width: 100%;
  height: auto;
}

.paper-preview table {
  table-layout: fixed;
  width: 100%;
}

.paper-preview td, 
.paper-preview th {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Responsive design */
@media (max-width: 1200px) {
  .layout-container {
    flex-direction: column;
  }
  
  .settings-panel {
    width: 100%;
    max-height: 400px;
  }
  
  .settings-content {
    max-height: 300px;
  }
  
  .preview-actions {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .pagination-controls {
    order: -1;
    width: 100%;
    justify-content: center;
    margin-bottom: 8px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }
  
  .page-info {
    flex-direction: column;
    gap: 5px;
    align-items: center;
  }
  
  .quick-titles {
    justify-content: center;
  }
  
  .quick-titles .el-button {
    font-size: 10px;
    padding: 1px 6px;
  }
}
</style>