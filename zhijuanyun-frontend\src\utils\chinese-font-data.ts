// 中文字体数据
// 这个文件包含了一个简化的中文字体的base64编码数据
// 注意：这是一个示例实现，实际项目中需要使用完整的字体文件

// 简化的思源黑体字体数据（仅包含常用字符）
// 注意：这是一个示例字体数据，实际使用时需要完整的字体文件
//
// 获取完整中文字体的步骤：
// 1. 下载思源黑体: https://github.com/adobe-fonts/source-han-sans
// 2. 使用在线工具转换TTF到base64: https://www.giftofspeed.com/base64-encoder/
// 3. 或使用jsPDF字体转换器: https://github.com/MrRio/jsPDF/tree/master/fontconverter
//
// 临时解决方案：使用一个最小的中文字体数据
export const NOTO_SANS_SC_BASE64 = `
AAEAAAAOAIAAAwBgT1MvMlpKUGgAAADsAAAAYGNtYXAADACaAAABTAAAACxjdnQgACECIAAAAXgAAAAEZ2FzcP//AAMAAAGAAAAACGdseWYADACaAAABiAAAACxoZWFkE7kDeAAAAbQAAAA2aGhlYQcCBAAAAAHsAAAAJGhtdHgADACaAAACEAAAAAxsb2NhAAIAAgAAAgwAAAAIbWF4cAAKABQAAAIkAAAAIG5hbWUADACaAAACRAAAACxwb3N0AAMAAgAAAnAAAAAg
`

// 获取字体数据
export function getChineseFontBase64(): string {
  return NOTO_SANS_SC_BASE64.replace(/\s/g, '')
}

// 字体加载器
export class ChineseFontLoader {
  private static loaded = false
  private static fontName = 'NotoSansSC'
  
  static async loadToJsPDF(pdf: any): Promise<boolean> {
    if (this.loaded) return true
    
    try {
      const fontData = getChineseFontBase64()
      
      // 添加字体文件到虚拟文件系统
      pdf.addFileToVFS(`${this.fontName}.ttf`, fontData)
      
      // 注册字体
      pdf.addFont(`${this.fontName}.ttf`, this.fontName, 'normal')
      
      this.loaded = true
      console.log('Chinese font loaded successfully')
      return true
    } catch (error) {
      console.error('Failed to load Chinese font:', error)
      return false
    }
  }
  
  static getFontName(): string {
    return this.fontName
  }
  
  static isLoaded(): boolean {
    return this.loaded
  }
  
  static reset(): void {
    this.loaded = false
  }
}

// 文本处理工具
export class ChineseTextProcessor {
  // 检测是否包含中文
  static containsChinese(text: string): boolean {
    return /[\u4e00-\u9fff]/.test(text)
  }
  
  // 将中文转换为拼音（简化版本）
  static toPinyin(text: string): string {
    const pinyinMap: { [key: string]: string } = {
      '题': 'ti',
      '目': 'mu',
      '答': 'da',
      '案': 'an',
      '解': 'jie',
      '析': 'xi',
      '分': 'fen',
      '总': 'zong',
      '时': 'shi',
      '间': 'jian',
      '分钟': 'fenzhong',
      '注意': 'zhuyi',
      '事项': 'shixiang',
      '试卷': 'shijuan',
      '考试': 'kaoshi',
      '满分': 'manfen',
      '第': 'di',
      '一': 'yi',
      '二': 'er',
      '三': 'san',
      '四': 'si',
      '五': 'wu',
      '六': 'liu',
      '七': 'qi',
      '八': 'ba',
      '九': 'jiu',
      '十': 'shi',
      '本': 'ben',
      '共': 'gong',
      '满': 'man',
      '考': 'kao',
      '请': 'qing',
      '在': 'zai',
      '前': 'qian',
      '仔': 'zi',
      '细': 'xi',
      '阅': 'yue',
      '读': 'du',
      '各': 'ge',
      '要': 'yao',
      '求': 'qiu',
      '所': 'suo',
      '有': 'you',
      '必': 'bi',
      '须': 'xu',
      '写': 'xie',
      '上': 'shang',
      '无': 'wu',
      '效': 'xiao',
      '结': 'jie',
      '束': 'shu',
      '后': 'hou',
      '将': 'jiang',
      '和': 'he',
      '并': 'bing',
      '交': 'jiao',
      '回': 'hui'
    }

    let result = text
    for (const [chinese, pinyin] of Object.entries(pinyinMap)) {
      result = result.replace(new RegExp(chinese, 'g'), pinyin)
    }

    // 对于剩余的中文字符，保持原样而不是替换为问号
    // 这样至少可以在支持中文的环境中正确显示
    return result
  }
  
  // 将中文转换为英文（简化版本）
  static toEnglish(text: string): string {
    const englishMap: { [key: string]: string } = {
      '题目': 'Question',
      '答案': 'Answer',
      '解析': 'Explanation',
      '分': 'pts',
      '总分': 'Total Score',
      '时间': 'Time',
      '分钟': 'min',
      '注意事项': 'Instructions',
      '试卷': 'Test Paper',
      '考试': 'Exam',
      '满分': 'Full Score',
      '第': 'No.',
      '一': '1',
      '二': '2',
      '三': '3',
      '四': '4',
      '五': '5',
      '六': '6',
      '七': '7',
      '八': '8',
      '九': '9',
      '十': '10',
      '选择题': 'Multiple Choice',
      '填空题': 'Fill in Blanks',
      '判断题': 'True/False',
      '简答题': 'Short Answer',
      '计算题': 'Calculation',
      '应用题': 'Application',
      '综合题': 'Comprehensive',
      '本试卷共': 'This paper has',
      '题': 'questions',
      '考试时间': 'exam time',
      '请在答题前仔细阅读各题目要求': 'Please read the requirements carefully before answering',
      '所有答案必须写在答题纸上': 'All answers must be written on the answer sheet',
      '写在试卷上无效': 'Writing on the test paper is invalid',
      '考试结束后': 'After the exam',
      '将试卷和答题纸一并交回': 'return both the test paper and answer sheet'
    }

    let result = text
    // 按长度排序，优先匹配长的短语
    const sortedEntries = Object.entries(englishMap).sort((a, b) => b[0].length - a[0].length)

    for (const [chinese, english] of sortedEntries) {
      result = result.replace(new RegExp(chinese, 'g'), english)
    }

    // 对于剩余的中文字符，保持原样
    // 这样在支持中文的环境中可以正确显示
    return result
  }
}
