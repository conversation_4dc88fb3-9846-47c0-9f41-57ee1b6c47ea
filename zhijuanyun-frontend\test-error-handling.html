<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI组卷错误处理测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: #fafafa;
        }
        
        .test-section h2 {
            color: #555;
            margin-top: 0;
        }
        
        button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #337ecc;
        }
        
        button:disabled {
            background-color: #c0c4cc;
            cursor: not-allowed;
        }
        
        .log-container {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .success {
            color: #48bb78;
        }
        
        .error {
            color: #f56565;
        }
        
        .warning {
            color: #ed8936;
        }
        
        .info {
            color: #4299e1;
        }
        
        .description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status.running {
            background-color: #fef5e7;
            color: #d69e2e;
        }
        
        .status.success {
            background-color: #f0fff4;
            color: #38a169;
        }
        
        .status.error {
            background-color: #fed7d7;
            color: #e53e3e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 AI组卷错误处理测试</h1>
        
        <div class="test-section">
            <h2>📋 测试说明</h2>
            <div class="description">
                这个测试页面用于验证AI组卷功能的错误处理机制是否正常工作。主要测试以下场景：
                <ul>
                    <li>AI返回包含"Exception:"的错误响应</li>
                    <li>AI返回包含"Error:"的错误响应</li>
                    <li>AI返回包含"undefined"的错误响应</li>
                    <li>AI返回无效JSON格式的响应</li>
                    <li>AI返回有效JSON格式的响应</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔧 JSON解析错误处理测试</h2>
            <div class="description">
                测试QuestionService.parsePaperResponse方法对各种错误响应的处理能力。
            </div>
            <button onclick="runJsonParsingTest()" id="jsonTestBtn">
                运行JSON解析测试
                <span id="jsonTestStatus"></span>
            </button>
        </div>
        
        <div class="test-section">
            <h2>🚀 完整AI组卷流程测试</h2>
            <div class="description">
                测试完整的AI组卷流程，包括与真实AI服务的交互和错误处理。
            </div>
            <button onclick="runFullGenerationTest()" id="fullTestBtn">
                运行完整流程测试
                <span id="fullTestStatus"></span>
            </button>
        </div>
        
        <div class="test-section">
            <h2>🎯 运行所有测试</h2>
            <div class="description">
                一次性运行所有测试用例，获得完整的测试报告。
            </div>
            <button onclick="runAllTests()" id="allTestBtn">
                运行所有测试
                <span id="allTestStatus"></span>
            </button>
            <button onclick="clearLog()" style="background-color: #909399;">
                清空日志
            </button>
        </div>
        
        <div class="log-container" id="logContainer">
            <div>等待测试开始...</div>
        </div>
    </div>

    <script>
        // 日志输出函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '<div>日志已清空，等待测试开始...</div>';
        }
        
        function setStatus(buttonId, statusId, status, text) {
            const statusElement = document.getElementById(statusId);
            statusElement.className = `status ${status}`;
            statusElement.textContent = text;
            
            const button = document.getElementById(buttonId);
            button.disabled = status === 'running';
        }
        
        // 模拟QuestionService.parsePaperResponse方法进行测试
        function mockParsePaperResponse(response, count) {
            // 检查是否包含错误信息
            if (response.includes('Exception:') || response.includes('Error:') || response.includes('undefined')) {
                log(`检测到错误响应: ${response.substring(0, 100)}...`, 'warning');
                throw new Error('AI返回了错误信息，请重试');
            }
            
            // 尝试解析JSON
            try {
                const data = JSON.parse(response);
                log('JSON解析成功', 'success');
                return Array.isArray(data) ? data : [data];
            } catch (error) {
                log(`JSON解析失败: ${error.message}`, 'error');
                throw new Error('AI返回的数据格式不正确，请重试');
            }
        }
        
        async function runJsonParsingTest() {
            setStatus('jsonTestBtn', 'jsonTestStatus', 'running', '运行中...');
            log('🧪 开始JSON解析错误处理测试', 'info');
            
            let passedTests = 0;
            let totalTests = 5;
            
            // 测试用例1: Exception错误
            try {
                const errorResponse1 = "Exception:'表单收集' is undefined参照这个，帮我连一下智能体";
                mockParsePaperResponse(errorResponse1, 5);
                log('❌ 测试1失败: 应该抛出异常但没有抛出', 'error');
            } catch (error) {
                log('✅ 测试1通过: 正确捕获了Exception错误', 'success');
                passedTests++;
            }
            
            // 测试用例2: Error错误
            try {
                const errorResponse2 = "Error: Something went wrong with the AI service";
                mockParsePaperResponse(errorResponse2, 5);
                log('❌ 测试2失败: 应该抛出异常但没有抛出', 'error');
            } catch (error) {
                log('✅ 测试2通过: 正确捕获了Error错误', 'success');
                passedTests++;
            }
            
            // 测试用例3: undefined错误
            try {
                const errorResponse3 = "The variable 'formData' is undefined in the current context";
                mockParsePaperResponse(errorResponse3, 5);
                log('❌ 测试3失败: 应该抛出异常但没有抛出', 'error');
            } catch (error) {
                log('✅ 测试3通过: 正确捕获了undefined错误', 'success');
                passedTests++;
            }
            
            // 测试用例4: 有效JSON
            try {
                const validResponse = `[{"stem": "测试题目", "options": ["A", "B", "C", "D"], "answer": "A"}]`;
                const result = mockParsePaperResponse(validResponse, 5);
                log('✅ 测试4通过: 正确解析了有效JSON', 'success');
                passedTests++;
            } catch (error) {
                log('❌ 测试4失败: 有效JSON解析失败', 'error');
            }
            
            // 测试用例5: 无效JSON
            try {
                const invalidJsonResponse = `{"stem": "测试题目", "options": ["A", "B"`;
                mockParsePaperResponse(invalidJsonResponse, 5);
                log('❌ 测试5失败: 应该抛出JSON解析异常', 'error');
            } catch (error) {
                log('✅ 测试5通过: 正确捕获了JSON格式错误', 'success');
                passedTests++;
            }
            
            const success = passedTests === totalTests;
            log(`🎯 JSON解析测试完成: ${passedTests}/${totalTests} 通过`, success ? 'success' : 'warning');
            setStatus('jsonTestBtn', 'jsonTestStatus', success ? 'success' : 'error', 
                     success ? '测试通过' : '部分失败');
        }
        
        async function runFullGenerationTest() {
            setStatus('fullTestBtn', 'fullTestStatus', 'running', '运行中...');
            log('🧪 开始完整AI组卷流程测试', 'info');
            log('⚠️ 注意: 这是模拟测试，实际AI服务需要在真实环境中测试', 'warning');
            
            // 模拟测试参数
            const testParams = {
                filterParams: {
                    grade: '初一',
                    subject: '数学',
                    knowledgePoints: ['基础知识点'],
                    questionTypes: ['single_choice'],
                    difficulty: ['medium'],
                    region: '通用教材',
                    questionCount: 5
                },
                count: 5
            };
            
            log('📋 测试参数: ' + JSON.stringify(testParams, null, 2), 'info');
            
            // 模拟成功场景
            try {
                log('🔄 模拟AI组卷请求...', 'info');
                await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟网络延迟
                
                log('✅ 模拟AI组卷成功', 'success');
                log('📊 模拟结果: 生成5道题目', 'success');
                
                setStatus('fullTestBtn', 'fullTestStatus', 'success', '测试通过');
            } catch (error) {
                log('❌ AI组卷失败: ' + error.message, 'error');
                setStatus('fullTestBtn', 'fullTestStatus', 'error', '测试失败');
            }
            
            log('🎯 完整流程测试完成', 'info');
        }
        
        async function runAllTests() {
            setStatus('allTestBtn', 'allTestStatus', 'running', '运行中...');
            log('🚀 开始运行所有错误处理测试', 'info');
            log('=' * 50, 'info');
            
            await runJsonParsingTest();
            
            log('\n' + '=' * 50, 'info');
            
            await runFullGenerationTest();
            
            log('\n' + '=' * 50, 'info');
            log('🏁 所有测试完成', 'success');
            setStatus('allTestBtn', 'allTestStatus', 'success', '全部完成');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 测试页面加载完成，可以开始测试', 'info');
            log('💡 建议先运行JSON解析测试，然后运行完整流程测试', 'info');
        });
    </script>
</body>
</html>
