// 两步式表单收集流程测试脚本
// 这个脚本可以在浏览器控制台中运行

console.log('🧪 两步式表单收集流程测试')
console.log('==================================')

// 模拟测试参数
const testParams = {
  filterParams: {
    grade: '初一',
    subject: '数学',
    knowledgePoints: ['基础知识点'],
    questionTypes: ['single_choice', 'multiple_choice', 'true_false', 'fill_blank', 'short_answer'],
    difficulty: ['easy', 'medium'],
    region: '通用教材',
    questionCount: 10
  },
  count: 10
}

console.log('📋 测试参数：')
console.log(JSON.stringify(testParams, null, 2))

// 第一步：构建示例提示词
function buildExamplePrompt() {
  return `你是一个专业的试卷生成助手。我需要你帮我生成一份试卷。

请严格按照以下JSON格式生成题目：

\`\`\`json
[
  {
    "stem": "题干内容",
    "options": ["选项A", "选项B", "选项C", "选项D"],
    "answer": "正确答案",
    "explanation": "详细解析",
    "questionType": "single_choice",
    "difficulty": "medium",
    "knowledgePoint": ["知识点1", "知识点2"],
    "score": 5
  }
]
\`\`\`

要求：
1. 返回标准的JSON数组格式
2. 每道题包含：题干(stem)、选项(options)、答案(answer)、解析(explanation)
3. 题目类型包括：单选题(single_choice)、多选题(multiple_choice)、判断题(true_false)、填空题(fill_blank)、简答题(short_answer)
4. 难度级别：easy(简单)、medium(中等)、hard(困难)

请确认你理解了以上格式要求，我将接下来提供具体的参数。`
}

// 第二步：构建表单数据
function buildFormData(params) {
  const { filterParams, count } = params
  
  // 根据题型分布题目数量
  const questionTypes = filterParams.questionTypes || []
  
  // 简化的题型分布逻辑
  let singleSelect = 0
  let multiSelect = 0
  let trueFalse = 0
  let essay = 0
  let application = 0
  
  // 根据题型分配数量
  if (questionTypes.includes('single_choice')) {
    singleSelect = Math.floor(count * 0.4)
  }
  if (questionTypes.includes('multiple_choice')) {
    multiSelect = Math.floor(count * 0.2)
  }
  if (questionTypes.includes('true_false')) {
    trueFalse = Math.floor(count * 0.1)
  }
  if (questionTypes.includes('fill_blank')) {
    essay = Math.floor(count * 0.15)
  }
  if (questionTypes.includes('short_answer')) {
    essay = Math.floor(count * 0.1)
  }
  if (questionTypes.includes('application')) {
    application = Math.floor(count * 0.05)
  }
  
  return {
    subject: filterParams.subject || '数学',
    grade: filterParams.grade || '初一',
    知识点: filterParams.knowledgePoints?.join(', ') || '基础知识点',
    single_select: singleSelect,
    true_false: trueFalse,
    multi_select: multiSelect,
    essay_num: essay,
    application_num: application,
    教材: filterParams.region || '通用教材',
    上下册: '上册',
    total_count: count
  }
}

// 运行测试
try {
  const examplePrompt = buildExamplePrompt()
  const formData = buildFormData(testParams)
  
  console.log('📝 第一步 - 示例提示词：')
  console.log(examplePrompt)
  
  console.log('📋 第二步 - 表单数据：')
  console.log(JSON.stringify(formData, null, 2))
  
  console.log('✅ 测试完成！')
  console.log('📊 预期流程：')
  console.log('   1. 发送示例格式要求给AI')
  console.log('   2. 等待AI响应确认')
  console.log('   3. 发送用户具体参数表单')
  console.log('   4. 获得实际题目内容')
  
  console.log('🎯 关键改进：')
  console.log('   - 确保AI理解格式要求后再处理具体参数')
  console.log('   - 避免AI返回表单配置而不是题目')
  console.log('   - 通过两步式交互提高题目生成质量')
  
} catch (error) {
  console.error('❌ 测试失败：', error)
}