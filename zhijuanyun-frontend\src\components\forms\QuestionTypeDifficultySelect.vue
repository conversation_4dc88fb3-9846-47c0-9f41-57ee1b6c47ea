<template>
  <div class="question-type-difficulty-select">
    <el-row :gutter="16">
      <el-col :span="12">
        <el-form-item label="题型">
          <el-select
            v-model="questionTypes"
            multiple
            placeholder="请选择题型"
            clearable
            style="width: 100%"
            @change="handleQuestionTypeChange"
          >
            <el-option
              v-for="type in questionTypeOptions"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="难度">
          <el-select
            v-model="difficulty"
            multiple
            placeholder="请选择难度"
            clearable
            style="width: 100%"
            @change="handleDifficultyChange"
          >
            <el-option
              v-for="level in difficultyOptions"
              :key="level.value"
              :label="level.label"
              :value="level.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Option {
  label: string
  value: string
}

interface Props {
  modelValueQuestionTypes?: string[]
  modelValueDifficulty?: string[]
}

interface Emits {
  (e: 'update:modelValueQuestionTypes', value: string[]): void
  (e: 'update:modelValueDifficulty', value: string[]): void
  (e: 'change', type: 'questionTypes' | 'difficulty', value: string[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const questionTypeOptions = ref<Option[]>([
  { label: '单选题', value: 'single_choice' },
  { label: '多选题', value: 'multiple_choice' },
  { label: '判断题', value: 'true_false' },
  { label: '填空题', value: 'fill_blank' },
  { label: '简答题', value: 'short_answer' },
  { label: '解答题', value: 'essay' },
  { label: '计算题', value: 'calculation' },
  { label: '应用题', value: 'application' },
  { label: '分析题', value: 'analysis' },
  { label: '综合题', value: 'comprehensive' }
])

const difficultyOptions = ref<Option[]>([
  { label: '容易', value: 'easy' },
  { label: '中等', value: 'medium' },
  { label: '困难', value: 'hard' },
  { label: '很难', value: 'very_hard' }
])

const questionTypes = computed({
  get: () => props.modelValueQuestionTypes || [],
  set: (value: string[]) => emit('update:modelValueQuestionTypes', value)
})

const difficulty = computed({
  get: () => props.modelValueDifficulty || [],
  set: (value: string[]) => emit('update:modelValueDifficulty', value)
})

const handleQuestionTypeChange = (value: string[]) => {
  emit('change', 'questionTypes', value)
}

const handleDifficultyChange = (value: string[]) => {
  emit('change', 'difficulty', value)
}
</script>

<style scoped>
.question-type-difficulty-select {
  margin-bottom: 16px;
}
</style>