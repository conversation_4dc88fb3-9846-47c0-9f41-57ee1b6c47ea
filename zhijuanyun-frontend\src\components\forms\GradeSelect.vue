<template>
  <div class="grade-select">
    <el-form-item label="年级" required>
      <el-select
        v-model="modelValue"
        placeholder="请选择年级"
        clearable
        style="width: 100%"
        @change="handleChange"
      >
        <el-option
          v-for="grade in grades"
          :key="grade.value"
          :label="grade.label"
          :value="grade.value"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Grade {
  label: string
  value: string
}

interface Props {
  modelValue?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const grades = ref<Grade[]>([
  { label: '小学一年级', value: 'grade1' },
  { label: '小学二年级', value: 'grade2' },
  { label: '小学三年级', value: 'grade3' },
  { label: '小学四年级', value: 'grade4' },
  { label: '小学五年级', value: 'grade5' },
  { label: '小学六年级', value: 'grade6' },
  { label: '初中一年级', value: 'grade7' },
  { label: '初中二年级', value: 'grade8' },
  { label: '初中三年级', value: 'grade9' },
  { label: '高中一年级', value: 'grade10' },
  { label: '高中二年级', value: 'grade11' },
  { label: '高中三年级', value: 'grade12' }
])

const modelValue = computed({
  get: () => props.modelValue || '',
  set: (value: string) => emit('update:modelValue', value)
})

const handleChange = (value: string) => {
  emit('change', value)
  
  // Add haptic feedback on mobile
  if ('vibrate' in navigator) {
    navigator.vibrate(50)
  }
}
</script>

<style scoped>
.grade-select {
  margin-bottom: 16px;
}
</style>