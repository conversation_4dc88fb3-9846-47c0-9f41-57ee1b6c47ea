<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>答题卡导出测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', SimSun, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background: #117a8b;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .preview-area {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            max-height: 400px;
            overflow-y: auto;
        }
        .sample-questions {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>答题卡导出功能测试</h1>
            <p>测试中文答题卡的PDF导出功能</p>
        </div>

        <div class="test-section">
            <h3>📋 答题卡导出测试</h3>
            <p>测试包含选择题和非选择题的答题卡导出功能</p>
            <div class="button-group">
                <button class="btn btn-primary" onclick="testAnswerSheetExport()">测试答题卡导出</button>
                <button class="btn btn-success" onclick="testMixedQuestionTypes()">测试混合题型答题卡</button>
                <button class="btn btn-info" onclick="previewAnswerSheetHTML()">预览答题卡HTML</button>
            </div>
            <div id="answerSheetStatus" class="status" style="display: none;"></div>
            <div class="sample-questions">
                示例题目：包含单选题、多选题、填空题、简答题等多种题型
            </div>
        </div>

        <div class="test-section">
            <h3>📄 普通试卷对比测试</h3>
            <p>对比普通试卷和答题卡模式的导出效果</p>
            <div class="button-group">
                <button class="btn btn-primary" onclick="testNormalPaperExport()">测试普通试卷</button>
                <button class="btn btn-success" onclick="compareExportModes()">对比两种模式</button>
            </div>
            <div id="normalPaperStatus" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔍 HTML预览区域</h3>
            <p>查看生成的HTML内容</p>
            <div class="button-group">
                <button class="btn btn-info" onclick="showHTMLPreview()">显示HTML预览</button>
                <button class="btn btn-primary" onclick="clearPreview()">清除预览</button>
            </div>
            <div id="htmlPreview" class="preview-area" style="display: none;"></div>
        </div>
    </div>

    <!-- 引入必要的库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        // 测试数据
        const sampleQuestions = [
            {
                id: '1',
                content: {
                    stem: '下列哪个选项是正确的中文表述？',
                    options: ['这是选项A', '这是选项B', '这是选项C', '这是选项D'],
                    answer: 'A',
                    explanation: '这是中文解析内容'
                },
                tags: {
                    questionType: 'single_choice',
                    subject: '语文'
                },
                score: 5
            },
            {
                id: '2',
                content: {
                    stem: '以下哪些是中国的传统节日？（多选）',
                    options: ['春节', '中秋节', '圣诞节', '端午节'],
                    answer: 'ABD',
                    explanation: '春节、中秋节、端午节都是中国传统节日'
                },
                tags: {
                    questionType: 'multiple_choice',
                    subject: '文化'
                },
                score: 8
            },
            {
                id: '3',
                content: {
                    stem: '请填空：中国的首都是_____。',
                    answer: '北京',
                    explanation: '中国的首都是北京'
                },
                tags: {
                    questionType: 'fill_blank',
                    subject: '地理'
                },
                score: 3
            },
            {
                id: '4',
                content: {
                    stem: '请简述中华文化的特点。',
                    answer: '中华文化源远流长、博大精深，具有包容性和创新性。',
                    explanation: '这是一道关于中华文化特点的简答题'
                },
                tags: {
                    questionType: 'short_answer',
                    subject: '文化'
                },
                score: 12
            }
        ]

        const answerSheetSettings = {
            title: '中文测试试卷',
            duration: 120,
            answerSheet: true,
            includes: ['questions'],
            watermark: {
                enabled: true,
                text: '测试答题卡'
            }
        }

        const normalPaperSettings = {
            title: '中文测试试卷',
            duration: 120,
            answerSheet: false,
            includes: ['questions', 'answers'],
            watermark: {
                enabled: true,
                text: '测试试卷'
            }
        }

        // 显示状态信息
        function showStatus(elementId, message, type = 'info') {
            const statusEl = document.getElementById(elementId)
            statusEl.textContent = message
            statusEl.className = `status ${type}`
            statusEl.style.display = 'block'
        }

        // 下载文件
        function downloadBlob(blob, filename) {
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = filename
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)
        }

        // 计算总分
        function getTotalScore(questions) {
            return questions.reduce((total, q) => total + q.score, 0)
        }

        // 测试答题卡导出
        async function testAnswerSheetExport() {
            showStatus('answerSheetStatus', '正在生成答题卡PDF...', 'info')
            
            try {
                const totalScore = getTotalScore(sampleQuestions)
                const blob = await exportToPDF(sampleQuestions, answerSheetSettings, totalScore)
                
                downloadBlob(blob, '中文答题卡测试.pdf')
                showStatus('answerSheetStatus', '✅ 答题卡PDF生成成功！请检查下载的文件中文字符是否正确显示。', 'success')
            } catch (error) {
                console.error('答题卡导出失败:', error)
                showStatus('answerSheetStatus', '❌ 答题卡导出失败: ' + error.message, 'error')
            }
        }

        // 测试混合题型答题卡
        async function testMixedQuestionTypes() {
            showStatus('answerSheetStatus', '正在生成混合题型答题卡...', 'info')
            
            try {
                // 添加更多题型
                const mixedQuestions = [
                    ...sampleQuestions,
                    {
                        id: '5',
                        content: {
                            stem: '判断：中国是世界上人口最多的国家。',
                            answer: '正确',
                            explanation: '中国确实是世界上人口最多的国家'
                        },
                        tags: {
                            questionType: 'true_false',
                            subject: '地理'
                        },
                        score: 2
                    },
                    {
                        id: '6',
                        content: {
                            stem: '计算题：如果一个班级有30名学生，其中60%是女生，那么男生有多少人？',
                            answer: '12人',
                            explanation: '30 × (1 - 0.6) = 30 × 0.4 = 12人'
                        },
                        tags: {
                            questionType: 'calculation',
                            subject: '数学'
                        },
                        score: 10
                    }
                ]
                
                const totalScore = getTotalScore(mixedQuestions)
                const blob = await exportToPDF(mixedQuestions, answerSheetSettings, totalScore)
                
                downloadBlob(blob, '混合题型答题卡测试.pdf')
                showStatus('answerSheetStatus', '✅ 混合题型答题卡生成成功！包含选择题、填空题、简答题、判断题、计算题。', 'success')
            } catch (error) {
                console.error('混合题型答题卡导出失败:', error)
                showStatus('answerSheetStatus', '❌ 混合题型答题卡导出失败: ' + error.message, 'error')
            }
        }

        // 测试普通试卷导出
        async function testNormalPaperExport() {
            showStatus('normalPaperStatus', '正在生成普通试卷PDF...', 'info')
            
            try {
                const totalScore = getTotalScore(sampleQuestions)
                const blob = await exportToPDF(sampleQuestions, normalPaperSettings, totalScore)
                
                downloadBlob(blob, '中文试卷测试.pdf')
                showStatus('normalPaperStatus', '✅ 普通试卷PDF生成成功！', 'success')
            } catch (error) {
                console.error('普通试卷导出失败:', error)
                showStatus('normalPaperStatus', '❌ 普通试卷导出失败: ' + error.message, 'error')
            }
        }

        // 对比两种导出模式
        async function compareExportModes() {
            showStatus('normalPaperStatus', '正在生成两种模式的PDF进行对比...', 'info')
            
            try {
                const totalScore = getTotalScore(sampleQuestions)
                
                // 生成答题卡
                const answerSheetBlob = await exportToPDF(sampleQuestions, answerSheetSettings, totalScore)
                downloadBlob(answerSheetBlob, '对比测试-答题卡模式.pdf')
                
                // 生成普通试卷
                const normalPaperBlob = await exportToPDF(sampleQuestions, normalPaperSettings, totalScore)
                downloadBlob(normalPaperBlob, '对比测试-普通试卷模式.pdf')
                
                showStatus('normalPaperStatus', '✅ 两种模式PDF都已生成！请对比查看答题卡模式和普通试卷模式的区别。', 'success')
            } catch (error) {
                console.error('对比测试失败:', error)
                showStatus('normalPaperStatus', '❌ 对比测试失败: ' + error.message, 'error')
            }
        }

        // 预览答题卡HTML
        function previewAnswerSheetHTML() {
            try {
                const totalScore = getTotalScore(sampleQuestions)
                const htmlContent = createExamHTML(sampleQuestions, answerSheetSettings, totalScore)
                
                const previewEl = document.getElementById('htmlPreview')
                previewEl.innerHTML = htmlContent
                previewEl.style.display = 'block'
                
                showStatus('answerSheetStatus', '✅ 答题卡HTML预览已显示', 'success')
            } catch (error) {
                console.error('HTML预览失败:', error)
                showStatus('answerSheetStatus', '❌ HTML预览失败: ' + error.message, 'error')
            }
        }

        // 显示HTML预览
        function showHTMLPreview() {
            previewAnswerSheetHTML()
        }

        // 清除预览
        function clearPreview() {
            const previewEl = document.getElementById('htmlPreview')
            previewEl.innerHTML = ''
            previewEl.style.display = 'none'
        }

        // 导出PDF的核心函数（简化版）
        async function exportToPDF(questions, settings, totalScore) {
            const htmlString = createExamHTML(questions, settings, totalScore)
            
            // 创建临时DOM元素
            const tempDiv = document.createElement('div')
            tempDiv.innerHTML = htmlString
            tempDiv.style.position = 'absolute'
            tempDiv.style.left = '-9999px'
            tempDiv.style.top = '-9999px'
            tempDiv.style.width = '800px'
            tempDiv.style.padding = '20px'
            tempDiv.style.fontFamily = 'Microsoft YaHei, SimSun, sans-serif'
            tempDiv.style.fontSize = '12px'
            tempDiv.style.lineHeight = '1.6'
            tempDiv.style.color = '#333'
            tempDiv.style.backgroundColor = '#fff'
            
            document.body.appendChild(tempDiv)
            
            try {
                // 使用html2canvas渲染
                const canvas = await html2canvas(tempDiv, {
                    scale: 1.5,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: false,
                    width: tempDiv.scrollWidth,
                    height: tempDiv.scrollHeight
                })
                
                // 创建PDF
                const { jsPDF } = window.jspdf
                const pdf = new jsPDF('p', 'mm', 'a4')
                
                const imgData = canvas.toDataURL('image/png')
                const imgWidth = 210 // A4 width in mm
                const pageHeight = 295 // A4 height in mm
                const imgHeight = (canvas.height * imgWidth) / canvas.width
                let heightLeft = imgHeight
                
                let position = 0
                
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
                heightLeft -= pageHeight
                
                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight
                    pdf.addPage()
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
                    heightLeft -= pageHeight
                }
                
                return pdf.output('blob')
            } finally {
                document.body.removeChild(tempDiv)
            }
        }

        // 创建试卷HTML（简化版）
        function createExamHTML(questions, settings, totalScore) {
            if (settings.answerSheet) {
                return createAnswerSheetHTML(questions, settings, totalScore)
            } else {
                return createNormalPaperHTML(questions, settings, totalScore)
            }
        }

        // 创建答题卡HTML
        function createAnswerSheetHTML(questions, settings, totalScore) {
            const choiceQuestions = questions.filter(q => 
                ['single_choice', 'multiple_choice', 'true_false'].includes(q.tags.questionType)
            )
            
            const nonChoiceQuestions = questions.filter(q => 
                !['single_choice', 'multiple_choice', 'true_false'].includes(q.tags.questionType)
            )
            
            return `
                <div style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: 'Microsoft YaHei', SimSun, sans-serif; font-size: 12px;">
                    <!-- 答题卡头部 -->
                    <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid #000; padding-bottom: 15px;">
                        <h1 style="font-size: 20px; margin: 0 0 10px 0;">${settings.title} - 答题卡</h1>
                        <div style="font-size: 12px; color: #333;">
                            总分：${totalScore}分 | 时间：${settings.duration}分钟 | 题目数：${questions.length}题
                        </div>
                    </div>
                    
                    <!-- 考生信息区 -->
                    <div style="margin-bottom: 20px; border: 1px solid #000; padding: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                            <div style="display: flex; align-items: center;">
                                <span style="margin-right: 10px;">姓名：</span>
                                <div style="display: inline-flex; gap: 2px;">
                                    ${Array(6).fill(0).map(() => '<div style="width: 20px; height: 20px; border: 1px solid #000; display: inline-block;"></div>').join('')}
                                </div>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <span style="margin-right: 10px;">学号：</span>
                                <div style="display: inline-flex; gap: 2px;">
                                    ${Array(10).fill(0).map(() => '<div style="width: 20px; height: 20px; border: 1px solid #000; display: inline-block;"></div>').join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    ${choiceQuestions.length > 0 ? createChoiceAnswerAreaHTML(choiceQuestions) : ''}
                    ${nonChoiceQuestions.length > 0 ? createNonChoiceAnswerAreaHTML(nonChoiceQuestions) : ''}
                </div>
            `
        }

        // 创建选择题答题区域HTML
        function createChoiceAnswerAreaHTML(choiceQuestions) {
            let html = `
                <div style="margin-bottom: 25px; border: 1px solid #000; padding: 15px;">
                    <h3 style="margin: 0 0 15px 0; font-size: 14px; text-align: center; background: #f0f0f0; padding: 8px;">选择题答题区</h3>
                    <div style="display: grid; grid-template-columns: repeat(5, 1fr); gap: 10px;">
            `
            
            choiceQuestions.forEach((question, index) => {
                const questionNum = index + 1
                const optionCount = question.content.options ? question.content.options.length : 4
                const options = ['A', 'B', 'C', 'D', 'E', 'F'].slice(0, optionCount)
                
                html += `
                    <div style="text-align: center; padding: 8px; border: 1px solid #ddd;">
                        <div style="font-weight: bold; margin-bottom: 5px; font-size: 11px;">${questionNum}</div>
                        <div style="display: flex; justify-content: center; gap: 3px;">
                            ${options.map(option => `
                                <div style="text-align: center;">
                                    <div style="font-size: 9px; margin-bottom: 2px;">${option}</div>
                                    <div style="width: 12px; height: 12px; border: 1px solid #000; border-radius: 50%; margin: 0 auto;"></div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `
            })
            
            html += `
                    </div>
                </div>
            `
            
            return html
        }

        // 创建非选择题答题区域HTML
        function createNonChoiceAnswerAreaHTML(nonChoiceQuestions) {
            let html = `
                <div style="margin-bottom: 25px; border: 1px solid #000; padding: 15px;">
                    <h3 style="margin: 0 0 15px 0; font-size: 14px; text-align: center; background: #f0f0f0; padding: 8px;">非选择题答题区</h3>
            `
            
            nonChoiceQuestions.forEach((question, index) => {
                const questionNum = index + 1
                const answerLines = getAnswerLinesCount(question.tags.questionType, question.score)
                
                html += `
                    <div style="margin-bottom: 20px; border: 1px solid #ddd; padding: 10px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <span style="font-weight: bold; font-size: 12px;">${questionNum}.</span>
                            <span style="font-size: 11px; color: #666;">(${question.score}分)</span>
                        </div>
                        <div style="min-height: ${answerLines * 20}px;">
                            ${Array(answerLines).fill(0).map(() => 
                                '<div style="height: 18px; border-bottom: 1px solid #ddd; margin-bottom: 2px;"></div>'
                            ).join('')}
                        </div>
                    </div>
                `
            })
            
            html += `
                </div>
            `
            
            return html
        }

        // 创建普通试卷HTML
        function createNormalPaperHTML(questions, settings, totalScore) {
            return `
                <div style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: 'Microsoft YaHei', SimSun, sans-serif;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="font-size: 24px; margin-bottom: 10px;">${settings.title}</h1>
                        <div style="font-size: 14px; color: #666;">
                            本试卷共 ${questions.length} 题，总分 ${totalScore} 分，考试时间 ${settings.duration} 分钟
                        </div>
                    </div>
                    
                    <div class="questions">
                        ${questions.map((question, index) => createQuestionHTML(question, index + 1, settings)).join('')}
                    </div>
                </div>
            `
        }

        // 创建单个题目HTML
        function createQuestionHTML(question, index, settings) {
            const { content, score } = question
            
            let html = `
                <div style="margin-bottom: 25px;">
                    <div style="font-weight: bold; margin-bottom: 8px;">
                        ${index}. (${score}分) ${content.stem}
                    </div>
            `
            
            if (content.options && content.options.length > 0) {
                html += '<div style="margin-left: 20px; line-height: 1.8;">'
                content.options.forEach((option, optIndex) => {
                    const letter = String.fromCharCode(65 + optIndex)
                    html += `<div>${letter}. ${option}</div>`
                })
                html += '</div>'
            }
            
            if (settings.includes && settings.includes.includes('answers')) {
                html += `<div style="margin-top: 10px; color: #007bff;"><strong>答案：</strong>${content.answer}</div>`
            }
            
            html += '</div>'
            return html
        }

        // 获取答题行数
        function getAnswerLinesCount(questionType, score) {
            const baseLines = {
                'fill_blank': 2,
                'short_answer': 4,
                'essay': 8,
                'calculation': 6,
                'proof': 8,
                'analysis': 6,
                'design': 10
            }
            
            const lines = baseLines[questionType] || 4
            const scoreMultiplier = Math.max(1, Math.floor(score / 5))
            return Math.min(lines * scoreMultiplier, 15)
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            console.log('答题卡导出测试页面已加载')
            console.log('可用的测试功能：')
            console.log('1. 测试答题卡导出')
            console.log('2. 测试混合题型答题卡')
            console.log('3. 测试普通试卷导出')
            console.log('4. 对比两种导出模式')
            console.log('5. 预览HTML内容')
        })

        // 全局测试函数
        window.testAnswerSheet = {
            testAnswerSheetExport,
            testMixedQuestionTypes,
            testNormalPaperExport,
            compareExportModes,
            previewAnswerSheetHTML
        }
    </script>
</body>
</html>
