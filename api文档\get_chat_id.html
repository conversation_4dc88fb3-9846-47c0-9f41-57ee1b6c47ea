<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>获取 Chat ID 接口文档 - Apifox风格</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0 auto;
            max-width: 960px;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        pre {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            background-color: #eee;
            padding: 2px 4px;
            border-radius: 3px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 8px;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>

<h1>📄 接口名称：获取 Chat ID</h1>

<h2>🔗 请求地址</h2>
<pre>GET https://ai.procaas.com/api/application/{profile_id}/chat/open</pre>

<p><strong>说明：</strong> 替换 <code>{profile_id}</code> 为你从上一个接口获取到的实际 Profile ID。</p>

<h2>⚙️ 请求头（Headers）</h2>
<table>
    <tr><th>Key</th><th>Value</th></tr>
    <tr><td>AUTHORIZATION</td><td>api_key</td></tr>
    <tr><td>Accept</td><td>application/json</td></tr>
</table>

<h2>✅ 成功响应示例（Status Code: 200）</h2>
<pre>
{
  "code": 200,
  "message": "成功",
  "data": "33ee141a-50a9-11f0-856d-0242ac130003"
}
</pre>

<h2>❌ 失败响应示例（例如 Status Code: 400 或 404）</h2>
<pre>
{
  "code": 400,
  "message": "请求失败",
  "data": null
}
</pre>

<h2>⚠️ 注意事项</h2>
<ul>
    <li>确保替换 URL 中的 <code>{profile_id}</code> 为实际有效的 Profile ID。</li>
    <li>确保在请求头中正确设置 <code>AUTHORIZATION</code> 字段。</li>
    <li>该接口用于打开一个新的会话，并返回对应的 Chat ID，可用于后续交互。</li>
</ul>


</body>
</html>