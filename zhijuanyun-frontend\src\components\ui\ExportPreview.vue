<template>
  <div class="export-preview">
    <!-- Normal Paper Mode -->
    <div v-if="!settings.answerSheet" class="paper-container">
      <!-- Paper Header -->
      <div class="paper-header">
        <h1 class="paper-title">{{ settings.title }}</h1>
        <div class="paper-info">
          <div class="info-row">
            <span>姓名：______________</span>
            <span>班级：______________</span>
            <span>学号：______________</span>
          </div>
          <div class="info-row">
            <span>总分：{{ totalScore }}分</span>
            <span>时间：{{ settings.duration }}分钟</span>
            <span>题目数：{{ questions.length }}题</span>
          </div>
        </div>
        
        <div class="instructions">
          <h4>注意事项：</h4>
          <ol>
            <li>本试卷共{{ questions.length }}题，满分{{ totalScore }}分，考试时间{{ settings.duration }}分钟。</li>
            <li>请在答题前仔细阅读各题目要求。</li>
            <li>所有答案必须写在答题纸上，写在试卷上无效。</li>
            <li>考试结束后，将试卷和答题纸一并交回。</li>
          </ol>
        </div>
      </div>
      
      <!-- Questions Content -->
      <div class="questions-content">
        <div 
          v-for="(question, index) in questions" 
          :key="question.id"
          class="question-item"
        >
          <div class="question-header">
            <span class="question-number">{{ index + 1 }}.</span>
            <span class="question-score">({{ question.score }}分)</span>
          </div>
          
          <div class="question-stem" v-html="question.content.stem"></div>
          
          <!-- Options for choice questions -->
          <div 
            v-if="question.content.options && question.content.options.length > 0" 
            class="question-options"
          >
            <div 
              v-for="(option, optIndex) in question.content.options" 
              :key="optIndex"
              class="option-item"
            >
              {{ String.fromCharCode(65 + optIndex) }}. {{ option }}
            </div>
          </div>
          
          <!-- Answer space for non-choice questions -->
          <div 
            v-else-if="needsAnswerSpace(question.tags.questionType)"
            class="answer-space"
          >
            <div 
              v-for="i in getAnswerLines(question.tags.questionType)" 
              :key="i"
              class="answer-line"
            ></div>
          </div>
          
          <!-- Answer (if included) -->
          <div v-if="settings.includes.includes('answers')" class="question-answer">
            <strong>答案：</strong>{{ question.content.answer }}
          </div>
          
          <!-- Explanation (if included) -->
          <div 
            v-if="settings.includes.includes('explanations') && question.content.explanation" 
            class="question-explanation"
          >
            <strong>解析：</strong>
            <div v-html="question.content.explanation"></div>
          </div>
        </div>
      </div>
      
      <!-- Score Breakdown (if included) -->
      <div v-if="settings.includes.includes('score_breakdown')" class="score-breakdown">
        <h3>分值分布</h3>
        <div class="breakdown-content">
          <div 
            v-for="(count, type) in questionTypeStats" 
            :key="type"
            class="breakdown-item"
          >
            <span>{{ getQuestionTypeLabel(type) }}：</span>
            <span>{{ count }}题，共{{ getTypeScore(type) }}分</span>
          </div>
        </div>
      </div>
      
      <!-- Watermark (if enabled) -->
      <div 
        v-if="settings.watermark.enabled" 
        class="watermark"
      >
        {{ settings.watermark.text }}
      </div>
    </div>
    
    <!-- Answer Sheet Mode -->
    <div v-else class="answer-sheet-container">
      <!-- Answer Sheet Header -->
      <div class="answer-sheet-header">
        <h1 class="sheet-title">{{ settings.title }} - 答题卡</h1>
        <div class="sheet-info">
          <div class="info-section">
            <div class="info-grid">
              <div class="info-item">
                <label>姓名：</label>
                <div class="fill-boxes">
                  <div v-for="i in 4" :key="i" class="fill-box"></div>
                </div>
              </div>
              <div class="info-item">
                <label>学号：</label>
                <div class="fill-boxes">
                  <div v-for="i in 8" :key="i" class="fill-box"></div>
                </div>
              </div>
              <div class="info-item">
                <label>班级：</label>
                <div class="fill-boxes">
                  <div v-for="i in 6" :key="i" class="fill-box"></div>
                </div>
              </div>
              <div class="info-item">
                <label>考场：</label>
                <div class="fill-boxes">
                  <div v-for="i in 3" :key="i" class="fill-box"></div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="exam-info">
            <div class="exam-details">
              <span>考试科目：{{ paperSubject }}</span>
              <span>考试时间：{{ settings.duration }}分钟</span>
              <span>总分：{{ totalScore }}分</span>
            </div>
          </div>
        </div>
        
        <div class="sheet-instructions">
          <h4>填涂说明：</h4>
          <div class="instruction-content">
            <div class="instruction-text">
              <p>1. 答题前，考生先将自己的姓名、学号、班级等信息填写清楚。</p>
              <p>2. 选择题部分请用2B铅笔填涂答题卡，如需改动，用橡皮擦擦干净后，再选涂其他答案。</p>
              <p>3. 非选择题部分请用黑色签字笔在答题区域内作答，超出答题区域的答案无效。</p>
              <p>4. 保持答题卡清洁，不要折叠、不要弄破。</p>
            </div>
            <div class="fill-example">
              <span>正确填涂：</span>
              <div class="example-bubble filled"></div>
              <span>错误填涂：</span>
              <div class="example-bubble wrong1"></div>
              <div class="example-bubble wrong2"></div>
              <div class="example-bubble wrong3"></div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Answer Areas -->
      <div class="answer-areas">
        <!-- Choice Questions Section -->
        <div v-if="choiceQuestions.length > 0" class="choice-section">
          <h3 class="section-title">选择题答题区</h3>
          <div class="choice-grid">
            <div 
              v-for="question in choiceQuestions" 
              :key="question.id"
              class="choice-item"
            >
              <div class="question-num">{{ getQuestionNumber(question.id) }}</div>
              <div class="choice-options">
                <div 
                  v-for="option in getOptionsForQuestion(question)" 
                  :key="option"
                  class="option-bubble"
                >
                  <span class="option-label">{{ option }}</span>
                  <div class="bubble"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Non-Choice Questions Section -->
        <div v-if="nonChoiceQuestions.length > 0" class="essay-section">
          <h3 class="section-title">非选择题答题区</h3>
          <div class="essay-questions">
            <div 
              v-for="question in nonChoiceQuestions" 
              :key="question.id"
              class="essay-item"
            >
              <div class="essay-header">
                <span class="question-number">{{ getQuestionNumber(question.id) }}.</span>
                <span class="question-score">({{ question.score }}分)</span>
                <span class="question-type">{{ getQuestionTypeLabel(question.tags.questionType) }}</span>
              </div>
              <div class="essay-answer-area">
                <div 
                  v-for="line in getAnswerLines(question.tags.questionType)" 
                  :key="line"
                  class="answer-line"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Score Table -->
      <div class="score-table">
        <h4>评分表</h4>
        <table class="score-grid">
          <thead>
            <tr>
              <th>题号</th>
              <th v-for="question in questions.slice(0, Math.min(questions.length, 20))" :key="question.id">
                {{ getQuestionNumber(question.id) }}
              </th>
              <th>小计</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>得分</td>
              <td v-for="question in questions.slice(0, Math.min(questions.length, 20))" :key="question.id" class="score-cell"></td>
              <td class="total-score-cell"></td>
            </tr>
          </tbody>
        </table>
        <div v-if="questions.length > 20" class="score-overflow">
          <p>注：超过20题的评分请在背面或另附评分表</p>
        </div>
      </div>
      
      <!-- Watermark (if enabled) -->
      <div 
        v-if="settings.watermark.enabled" 
        class="watermark"
      >
        {{ settings.watermark.text }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Question, ExportSettings } from '@/types'


interface Props {
  questions: Question[]
  settings: ExportSettings
  totalScore: number
}

const props = defineProps<Props>()

// Question type labels
const questionTypeLabels: Record<string, string> = {
  'single_choice': '单选题',
  'multiple_choice': '多选题',
  'true_false': '判断题',
  'fill_blank': '填空题',
  'short_answer': '简答题',
  'essay': '解答题',
  'calculation': '计算题',
  'application': '应用题',
  'analysis': '分析题',
  'comprehensive': '综合题'
}

const questionTypeStats = computed(() => {
  const stats: Record<string, number> = {}
  props.questions.forEach(question => {
    const type = question.tags.questionType
    stats[type] = (stats[type] || 0) + 1
  })
  return stats
})

// Answer sheet specific computed properties
const choiceQuestions = computed(() => {
  return props.questions.filter(q => 
    ['single_choice', 'multiple_choice', 'true_false'].includes(q.tags.questionType)
  )
})

const nonChoiceQuestions = computed(() => {
  return props.questions.filter(q => 
    !['single_choice', 'multiple_choice', 'true_false'].includes(q.tags.questionType)
  )
})

const paperSubject = computed(() => {
  if (props.questions.length > 0) {
    const subjects = [...new Set(props.questions.map(q => q.tags.subject))]
    return subjects.length === 1 ? subjects[0] : '综合'
  }
  return '数学' // default subject
})

const getQuestionNumber = (questionId: string): number => {
  return props.questions.findIndex(q => q.id === questionId) + 1
}

const getOptionsForQuestion = (question: Question): string[] => {
  const type = question.tags.questionType
  if (type === 'true_false') {
    return ['A', 'B'] // 判断题通常是对错，用A/B表示
  } else if (type === 'multiple_choice') {
    return ['A', 'B', 'C', 'D', 'E', 'F'] // 多选题可能有更多选项
  } else {
    return ['A', 'B', 'C', 'D'] // 单选题标准选项
  }
}

const getQuestionTypeLabel = (type: string) => questionTypeLabels[type] || type

const getTypeScore = (type: string) => {
  return props.questions
    .filter(q => q.tags.questionType === type)
    .reduce((total, q) => total + q.score, 0)
}

const needsAnswerSpace = (questionType: string): boolean => {
  return ['fill_blank', 'short_answer', 'essay', 'calculation', 'application'].includes(questionType)
}

const getAnswerLines = (questionType: string): number => {
  const lineMap: Record<string, number> = {
    'fill_blank': 2,
    'short_answer': 4,
    'essay': 8,
    'calculation': 6,
    'application': 8
  }
  return lineMap[questionType] || 3
}
</script>

<style scoped>
.export-preview {
  width: 210mm;
  min-height: 297mm;
  margin: 0 auto;
  background-color: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  position: relative;
}

.paper-container {
  padding: 25mm 20mm;
  font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.paper-header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
}

.paper-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 16px 0;
  color: #333;
}

.paper-info {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin: 8px 0;
}

.instructions {
  text-align: left;
  margin-top: 20px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.instructions h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
}

.instructions ol {
  margin: 0;
  padding-left: 20px;
}

.instructions li {
  margin: 4px 0;
  line-height: 1.5;
}

.questions-content {
  margin-top: 30px;
}

.question-item {
  margin-bottom: 24px;
  page-break-inside: avoid;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.question-number {
  font-weight: bold;
  color: #333;
  font-size: 16px;
}

.question-score {
  font-size: 14px;
  color: #666;
}

.question-stem {
  margin-bottom: 12px;
  line-height: 1.6;
}

.question-options {
  margin: 12px 0;
  padding-left: 20px;
}

.option-item {
  margin: 6px 0;
  line-height: 1.5;
}

.answer-space {
  margin: 16px 0;
  min-height: 40px;
}

.answer-line {
  height: 20px;
  border-bottom: 1px solid #333;
  margin: 8px 0;
}

.question-answer {
  margin: 12px 0;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
  font-size: 13px;
}

.question-explanation {
  margin: 12px 0;
  padding: 8px 12px;
  background-color: #fefce8;
  border: 1px solid #fde047;
  border-radius: 4px;
  font-size: 13px;
}

.question-explanation strong {
  display: block;
  margin-bottom: 4px;
  color: #a16207;
}

.score-breakdown {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #fafafa;
}

.score-breakdown h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #333;
}

.breakdown-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  border-bottom: 1px dotted #ccc;
}

.watermark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 48px;
  color: rgba(0, 0, 0, 0.1);
  font-weight: bold;
  pointer-events: none;
  z-index: 10;
  white-space: nowrap;
}

/* Answer Sheet Styles */
.answer-sheet-container {
  width: 210mm;
  min-height: 297mm;
  margin: 0 auto;
  background-color: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  position: relative;
  padding: 15mm 10mm;
  font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
}

.answer-sheet-header {
  border-bottom: 2px solid #000;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.sheet-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  margin: 0 0 15px 0;
  color: #000;
}

.sheet-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.info-section {
  flex: 1;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item label {
  font-weight: bold;
  white-space: nowrap;
  min-width: 40px;
}

.fill-boxes {
  display: flex;
  gap: 2px;
}

.fill-box {
  width: 16px;
  height: 16px;
  border: 1px solid #000;
  background-color: white;
}

.exam-info {
  flex: 1;
  text-align: right;
  padding-left: 20px;
}

.exam-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 11px;
}

.sheet-instructions {
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.sheet-instructions h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: bold;
}

.instruction-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.instruction-text {
  flex: 1;
  font-size: 10px;
  line-height: 1.3;
}

.instruction-text p {
  margin: 2px 0;
}

.fill-example {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  white-space: nowrap;
}

.example-bubble {
  width: 12px;
  height: 12px;
  border: 1px solid #000;
  border-radius: 50%;
  display: inline-block;
}

.example-bubble.filled {
  background-color: #000;
}

.example-bubble.wrong1 {
  background: linear-gradient(45deg, transparent 40%, #000 40%, #000 60%, transparent 60%);
}

.example-bubble.wrong2 {
  border: 2px solid #000;
  background-color: white;
}

.example-bubble.wrong3 {
  background-color: #666;
}

.answer-areas {
  margin-top: 20px;
}

.choice-section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 14px;
  font-weight: bold;
  margin: 0 0 12px 0;
  padding: 6px 12px;
  background-color: #f0f0f0;
  border-left: 4px solid #333;
}

.choice-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
  border: 1px solid #ccc;
  padding: 10px;
  background-color: #fafafa;
}

.choice-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px;
  border-bottom: 1px dotted #ccc;
}

.question-num {
  font-weight: bold;
  min-width: 20px;
  font-size: 11px;
}

.choice-options {
  display: flex;
  gap: 6px;
}

.option-bubble {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.option-label {
  font-size: 9px;
  font-weight: bold;
}

.bubble {
  width: 12px;
  height: 12px;
  border: 1px solid #000;
  border-radius: 50%;
  background-color: white;
}

.essay-section {
  margin-bottom: 25px;
}

.essay-questions {
  border: 1px solid #ccc;
  background-color: #fafafa;
}

.essay-item {
  border-bottom: 1px solid #ddd;
  padding: 8px;
}

.essay-item:last-child {
  border-bottom: none;
}

.essay-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  font-weight: bold;
}

.question-number {
  color: #333;
}

.question-score {
  color: #666;
  font-size: 10px;
}

.question-type {
  color: #888;
  font-size: 9px;
  background-color: #eee;
  padding: 1px 4px;
  border-radius: 2px;
}

.essay-answer-area {
  min-height: 60px;
}

.answer-line {
  height: 18px;
  border-bottom: 1px solid #ddd;
  margin-bottom: 2px;
}

.score-table {
  margin-top: 20px;
  padding: 10px;
  border: 1px solid #000;
  background-color: #f9f9f9;
}

.score-table h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
}

.score-grid {
  width: 100%;
  border-collapse: collapse;
  font-size: 10px;
}

.score-grid th,
.score-grid td {
  border: 1px solid #000;
  padding: 4px;
  text-align: center;
  min-width: 25px;
  height: 20px;
}

.score-grid th {
  background-color: #e8e8e8;
  font-weight: bold;
}

.score-cell {
  background-color: white;
}

.total-score-cell {
  background-color: #f0f0f0;
  font-weight: bold;
  width: 40px;
}

.score-overflow {
  margin-top: 8px;
  font-size: 9px;
  color: #666;
  text-align: center;
}

.score-overflow p {
  margin: 0;
}

/* Print styles */
@media print {
  .export-preview {
    box-shadow: none;
    margin: 0;
  }
  
  .question-item {
    break-inside: avoid;
    page-break-inside: avoid;
  }
  
  .question-answer,
  .question-explanation {
    background-color: transparent !important;
    border: 1px solid #ccc !important;
  }
  
  .score-breakdown {
    background-color: transparent !important;
  }
}
</style>