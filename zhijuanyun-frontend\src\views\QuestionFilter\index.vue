<template>
  <div class="question-filter">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>题目筛选</span>
          <div class="header-actions">
            <el-button @click="handleReset" type="info" size="small">重置</el-button>
            <el-button @click="handleSearch" type="primary" size="small" :loading="loading">
              AI组卷
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="filter-content">
        <el-form :model="filterForm" ref="formRef" label-width="80px">
          <!-- Grade Selection -->
          <GradeSelect
            v-model="filterForm.grade"
            @change="handleGradeChange"
          />
          
          <!-- Subject Selection -->
          <SubjectSelect
            v-model="filterForm.subject"
            :grade="filterForm.grade"
            @change="handleSubjectChange"
          />
          
          <!-- Knowledge Point Selection -->
          <KnowledgePointSelect
            v-model="filterForm.knowledgePoints"
            :subject="filterForm.subject"
            @change="handleKnowledgePointChange"
          />
          
          <!-- Question Type and Difficulty -->
          <QuestionTypeDifficultySelect
            v-model:model-value-question-types="filterForm.questionTypes"
            v-model:model-value-difficulty="filterForm.difficulty"
            @change="handleTypeDifficultyChange"
          />
          
          <!-- Region and Question Count -->
          <RegionCountSelect
            v-model:model-value-region="filterForm.region"
            v-model:model-value-question-count="filterForm.questionCount"
            @change="handleRegionCountChange"
          />
          
          <!-- Sample Upload -->
          <SampleUpload
            v-model="filterForm.sampleFile"
            @change="handleSampleChange"
          />
          
          <!-- Search Actions -->
          <el-form-item>
            <div class="search-actions">
              <el-button @click="handleReset" size="default">重置筛选</el-button>
              <el-button 
                type="primary" 
                @click="handleSearch" 
                :loading="loading"
                size="default"
              >
                开始搜索 ({{ filterForm.questionCount }} 题)
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <!-- Search Results -->
    <el-card v-if="hasSearched" class="results-card">
      <template #header>
        <div class="card-header">
          <span>AI组卷结果</span>
          <el-tag v-if="!loading" type="info" size="small">
            共生成 {{ questions.length }} 道题目
          </el-tag>
        </div>
      </template>
      
      <div class="results-content">
        <!-- Loading State -->
        <div v-if="loading" class="loading-state">
          <div class="loading-animation">
            <el-icon class="loading-icon"><loading /></el-icon>
            <div class="loading-steps">
              <div class="step" :class="{ active: progress >= 0 }">
                <span class="step-number">1</span>
                <span class="step-text">分析需求</span>
              </div>
              <div class="step" :class="{ active: progress >= 25 }">
                <span class="step-number">2</span>
                <span class="step-text">智能匹配</span>
              </div>
              <div class="step" :class="{ active: progress >= 50 }">
                <span class="step-number">3</span>
                <span class="step-text">生成题目</span>
              </div>
              <div class="step" :class="{ active: progress >= 75 }">
                <span class="step-number">4</span>
                <span class="step-text">优化调整</span>
              </div>
              <div class="step" :class="{ active: progress >= 100 }">
                <span class="step-number">5</span>
                <span class="step-text">完成组卷</span>
              </div>
            </div>
          </div>
          <div class="loading-text">
            <h3>AI智能组卷中...</h3>
            <p>{{ getProgressMessage() }}</p>
            <div v-if="progress > 0" class="progress-container">
              <el-progress 
                :percentage="progress" 
                :stroke-width="8"
                :show-text="true"
                status="active"
                :color="getProgressColor()"
              />
              <div class="progress-text">{{ progress }}%</div>
            </div>
          </div>
        </div>
        
        <!-- Error State -->
        <div v-else-if="error" class="error-state">
          <el-alert
            :title="error"
            type="error"
            show-icon
            :closable="false"
          />
          <el-button @click="handleSearch" type="primary" style="margin-top: 16px;">
            重新搜索
          </el-button>
        </div>
        
        <!-- Empty State -->
        <div v-else-if="questions.length === 0" class="empty-state">
          <el-empty description="未找到符合条件的题目">
            <el-button @click="handleSearch" type="primary">重新搜索</el-button>
          </el-empty>
        </div>
        
        <!-- Questions List -->
        <div v-else class="questions-list">
          <div v-for="(question, index) in questions" :key="question.id" class="question-item">
            <el-card shadow="hover">
              <div class="question-header">
                <span class="question-number">第 {{ index + 1 }} 题</span>
                <div class="question-tags">
                  <el-tag size="small" type="info">{{ question.tags.questionType }}</el-tag>
                  <el-tag size="small" type="warning">{{ question.tags.difficulty }}</el-tag>
                  <el-tag size="small">{{ question.score }}分</el-tag>
                </div>
              </div>
              
              <div class="question-content">
                <div class="question-stem" v-html="question.content.stem"></div>
                
                <div v-if="question.content.options && question.content.options.length > 0" class="question-options">
                  <div 
                    v-for="(option, optIndex) in question.content.options" 
                    :key="optIndex"
                    class="option-item"
                  >
                    {{ String.fromCharCode(65 + optIndex) }}. {{ option }}
                  </div>
                </div>
                
                <div class="question-answer">
                  <strong>答案：</strong>{{ question.content.answer }}
                </div>
                
                <div v-if="question.content.explanation" class="question-explanation">
                  <strong>解析：</strong>{{ question.content.explanation }}
                </div>
              </div>
              
              <div class="question-meta">
                <span>知识点：{{ question.tags.knowledgePoint.join('、') }}</span>
                <span>来源：{{ question.tags.sourceType }}</span>
              </div>
            </el-card>
          </div>
          
          <!-- Actions -->
          <div class="list-actions">
            <el-button @click="handleContinueEdit" type="primary" size="large">
              继续编辑题目
            </el-button>
            <el-button @click="handleNewSearch" size="large">
              重新组卷
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { useFilterStore } from '@/stores/filter'
import type { FilterParams, SampleInfo } from '@/types'

// Import form components
import GradeSelect from '@/components/forms/GradeSelect.vue'
import SubjectSelect from '@/components/forms/SubjectSelect.vue' 
import KnowledgePointSelect from '@/components/forms/KnowledgePointSelect.vue'
import QuestionTypeDifficultySelect from '@/components/forms/QuestionTypeDifficultySelect.vue'
import RegionCountSelect from '@/components/forms/RegionCountSelect.vue'
import SampleUpload from '@/components/forms/SampleUpload.vue'

const filterStore = useFilterStore()
const router = useRouter()
const formRef = ref<FormInstance>()
const hasSearched = ref(false)

// Form data
const filterForm = ref<FilterParams>({
  grade: '',
  subject: '',
  knowledgePoints: [],
  questionTypes: [],
  difficulty: [],
  region: '',
  questionCount: 10,
  sampleFile: undefined
})

// Computed properties
const loading = computed(() => filterStore.loading)
const questions = computed(() => filterStore.questions)
const error = computed(() => filterStore.error)
const progress = computed(() => filterStore.progress)

// Event handlers
const handleGradeChange = (value: string) => {
  filterForm.value.grade = value
  // Reset dependent fields when grade changes
  filterForm.value.subject = ''
  filterForm.value.knowledgePoints = []
}

const handleSubjectChange = (value: string) => {
  filterForm.value.subject = value
  // Reset knowledge points when subject changes
  filterForm.value.knowledgePoints = []
}

const handleKnowledgePointChange = (value: string[]) => {
  filterForm.value.knowledgePoints = value
}

const handleTypeDifficultyChange = (type: 'questionTypes' | 'difficulty', value: string[]) => {
  if (type === 'questionTypes') {
    filterForm.value.questionTypes = value
  } else {
    filterForm.value.difficulty = value
  }
}

const handleRegionCountChange = (type: 'region' | 'questionCount', value: string | number) => {
  if (type === 'region') {
    filterForm.value.region = value as string
  } else {
    filterForm.value.questionCount = value as number
  }
}

const handleSampleChange = (value: SampleInfo | null) => {
  filterForm.value.sampleFile = value || undefined
}

const handleSearch = async () => {
  // Enhanced form validation
  const validationErrors = validateForm()
  if (validationErrors.length > 0) {
    ElMessage.error(`请完善以下信息：${validationErrors.join('、')}`)
    return
  }
  
  try {
    // Show loading confirmation
    ElMessage.info('正在使用AI智能组卷，请稍候...')
    
    // Update store with current filter params
    filterStore.updateFilterParams(filterForm.value)
    
    // Save form state to localStorage
    saveFormState()
    
    // Use AI to generate paper
    await filterStore.generatePaperWithAI()
    hasSearched.value = true
    
    if (questions.value.length > 0) {
      ElMessage.success(`AI组卷成功，生成 ${questions.value.length} 道题目`)
      
      // Auto-save successful result
      saveSearchResult({
        params: filterForm.value,
        resultCount: questions.value.length,
        timestamp: new Date().toISOString()
      })
    }
  } catch (err) {
    console.error('AI paper generation error:', err)
    const errorMessage = err instanceof Error ? err.message : 'AI组卷失败'
    
    // Enhanced error handling with specific guidance
    handleErrorWithGuidance(errorMessage)
  }
}

const handleReset = () => {
  // Show confirmation dialog
  ElMessageBox.confirm('确定要重置所有筛选条件吗？', '确认重置', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    filterForm.value = {
      grade: '',
      subject: '',
      knowledgePoints: [],
      questionTypes: [],
      difficulty: [],
      region: '',
      questionCount: 10,
      sampleFile: undefined
    }
    
    filterStore.resetFilter()
    hasSearched.value = false
    
    // Clear saved form state
    clearFormState()
    
    ElMessage.success('筛选条件已重置')
  }).catch(() => {
    // User cancelled reset
  })
}

const handleNewSearch = () => {
  hasSearched.value = false
  filterStore.clearQuestions()
}

const handleContinueEdit = () => {
  // Save current state before navigation
  saveFormState()
  
  // Navigate to question edit view
  router.push('/edit')
}

// Enhanced validation function
const validateForm = (): string[] => {
  const errors: string[] = []
  
  if (!filterForm.value.grade) {
    errors.push('年级')
  }
  
  if (!filterForm.value.subject) {
    errors.push('学科')
  }
  
  if (filterForm.value.questionCount <= 0 || filterForm.value.questionCount > 100) {
    errors.push('题目数量（1-100）')
  }
  
  // Optional: Validate knowledge points if subject is selected
  if (filterForm.value.subject && filterForm.value.knowledgePoints.length === 0) {
    // Only warn, don't block
    ElMessage.warning('建议选择相关知识点以获得更精准的组卷结果')
  }
  
  return errors
}

// Enhanced error handling with user guidance
const handleErrorWithGuidance = (errorMessage: string) => {
  let guidance = ''
  
  if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
    guidance = '建议：减少题目数量或稍后重试'
  } else if (errorMessage.includes('网络') || errorMessage.includes('network')) {
    guidance = '建议：检查网络连接后重试'
  } else if (errorMessage.includes('重试') || errorMessage.includes('retry')) {
    guidance = '建议：稍后重试或联系技术支持'
  } else if (errorMessage.includes('参数') || errorMessage.includes('parameter')) {
    guidance = '建议：检查筛选条件后重试'
  } else {
    guidance = '建议：检查筛选条件或稍后重试'
  }
  
  ElMessage.error(`${errorMessage}。${guidance}`)
}

// Form persistence functions
const saveFormState = () => {
  try {
    const stateToSave = {
      ...filterForm.value,
      savedAt: new Date().toISOString()
    }
    localStorage.setItem('questionFilterState', JSON.stringify(stateToSave))
  } catch (error) {
    console.warn('Failed to save form state:', error)
  }
}

const loadFormState = () => {
  try {
    const savedState = localStorage.getItem('questionFilterState')
    if (savedState) {
      const parsedState = JSON.parse(savedState)
      // Check if state is recent (within 24 hours)
      const savedTime = new Date(parsedState.savedAt)
      const now = new Date()
      const hoursDiff = (now.getTime() - savedTime.getTime()) / (1000 * 60 * 60)
      
      if (hoursDiff < 24) {
        return parsedState
      }
    }
  } catch (error) {
    console.warn('Failed to load form state:', error)
  }
  return null
}

const clearFormState = () => {
  try {
    localStorage.removeItem('questionFilterState')
  } catch (error) {
    console.warn('Failed to clear form state:', error)
  }
}

const saveSearchResult = (result: any) => {
  try {
    const searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]')
    searchHistory.unshift(result)
    
    // Keep only last 10 results
    if (searchHistory.length > 10) {
      searchHistory.splice(10)
    }
    
    localStorage.setItem('searchHistory', JSON.stringify(searchHistory))
  } catch (error) {
    console.warn('Failed to save search result:', error)
  }
}

// Progress-related functions
const getProgressMessage = (): string => {
  if (progress.value < 25) {
    return '正在分析您的组卷需求...'
  } else if (progress.value < 50) {
    return '正在智能匹配题目...'
  } else if (progress.value < 75) {
    return '正在生成题目内容...'
  } else if (progress.value < 100) {
    return '正在优化题目质量...'
  } else {
    return '组卷完成！'
  }
}

const getProgressColor = (): string => {
  if (progress.value < 25) {
    return '#409EFF'
  } else if (progress.value < 50) {
    return '#67C23A'
  } else if (progress.value < 75) {
    return '#E6A23C'
  } else {
    return '#F56C6C'
  }
}

// Initialize form with saved state if available
onMounted(() => {
  const savedState = loadFormState()
  if (savedState) {
    // Restore form state
    filterForm.value = {
      grade: savedState.grade || '',
      subject: savedState.subject || '',
      knowledgePoints: savedState.knowledgePoints || [],
      questionTypes: savedState.questionTypes || [],
      difficulty: savedState.difficulty || [],
      region: savedState.region || '',
      questionCount: savedState.questionCount || 10,
      sampleFile: savedState.sampleFile || undefined
    }
    
    // Ask user if they want to continue with previous state
    ElMessageBox.confirm('检测到上次未完成的筛选，是否恢复？', '恢复筛选条件', {
      confirmButtonText: '恢复',
      cancelButtonText: '重新开始',
      type: 'info'
    }).then(() => {
      filterStore.updateFilterParams(filterForm.value)
      ElMessage.success('已恢复上次筛选条件')
    }).catch(() => {
      // User chose to start fresh
      clearFormState()
    })
  } else {
    // Initialize with store data if available
    const storeParams = filterStore.filterParams
    if (storeParams && (storeParams.grade || storeParams.subject)) {
      filterForm.value = { ...storeParams }
    }
  }
})
</script>

<style scoped>
.question-filter {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.filter-content {
  padding: 20px 0;
}

.search-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
}

.results-card {
  margin-top: 20px;
}

.results-content {
  min-height: 200px;
}

.loading-state {
  text-align: center;
  padding: 40px 0;
}

.loading-animation {
  margin-bottom: 24px;
}

.loading-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  animation: rotate 2s linear infinite;
  margin-bottom: 16px;
}

.loading-steps {
  display: flex;
  justify-content: space-between;
  max-width: 600px;
  margin: 0 auto;
  position: relative;
}

.loading-steps::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--el-border-color-lighter);
  z-index: 1;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background: var(--el-color-primary);
  color: white;
  transform: scale(1.1);
}

.step.active .step-text {
  color: var(--el-color-primary);
  font-weight: 500;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--el-fill-color-lighter);
  color: var(--el-text-color-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.step-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  white-space: nowrap;
}

.loading-text {
  margin-top: 16px;
  color: var(--el-text-color-regular);
}

.loading-text h3 {
  margin: 0 0 8px 0;
  color: var(--el-color-primary);
  font-size: 18px;
}

.loading-text p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.progress-container {
  margin-top: 12px;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.progress-text {
  margin-top: 8px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  text-align: center;
}

.error-state {
  text-align: center;
  padding: 40px 0;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.questions-list {
  margin-top: 16px;
}

.question-item {
  margin-bottom: 16px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.question-number {
  font-weight: bold;
  color: var(--el-color-primary);
}

.question-tags {
  display: flex;
  gap: 4px;
}

.question-content {
  margin-bottom: 12px;
}

.question-stem {
  margin-bottom: 12px;
  line-height: 1.6;
  font-size: 15px;
}

.question-options {
  margin: 12px 0;
  padding-left: 16px;
}

.option-item {
  margin: 4px 0;
  line-height: 1.5;
}

.question-answer {
  margin: 8px 0;
  padding: 8px 12px;
  background-color: var(--el-fill-color-extra-light);
  border-radius: 4px;
  font-size: 14px;
}

.question-explanation {
  margin: 8px 0;
  padding: 8px 12px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
}

.question-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  border-top: 1px solid var(--el-border-color-lighter);
  padding-top: 8px;
}

.list-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
  padding: 20px 0;
}
</style>