// 使用Canvas渲染中文文本为图片，然后插入到PDF中
// 这是一个更可靠的中文PDF导出解决方案

export interface CanvasTextOptions {
  fontSize?: number
  fontFamily?: string
  color?: string
  backgroundColor?: string
  padding?: number
  maxWidth?: number
  lineHeight?: number
}

export class ChineseCanvasRenderer {
  private static canvas: HTMLCanvasElement | null = null
  private static ctx: CanvasRenderingContext2D | null = null
  
  // 初始化Canvas
  private static initCanvas(): void {
    if (!this.canvas) {
      this.canvas = document.createElement('canvas')
      this.ctx = this.canvas.getContext('2d')
      
      // 设置高DPI支持
      const dpr = window.devicePixelRatio || 1
      this.canvas.style.width = '800px'
      this.canvas.style.height = '600px'
      this.canvas.width = 800 * dpr
      this.canvas.height = 600 * dpr
      
      if (this.ctx) {
        this.ctx.scale(dpr, dpr)
      }
    }
  }
  
  // 渲染中文文本为图片
  static renderTextToImage(
    text: string, 
    options: CanvasTextOptions = {}
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        this.initCanvas()
        
        if (!this.canvas || !this.ctx) {
          reject(new Error('Failed to initialize canvas'))
          return
        }
        
        const {
          fontSize = 16,
          fontFamily = 'Microsoft YaHei, SimSun, sans-serif',
          color = '#000000',
          backgroundColor = 'transparent',
          padding = 10,
          maxWidth = 780,
          lineHeight = 1.5
        } = options
        
        // 设置字体
        this.ctx.font = `${fontSize}px ${fontFamily}`
        this.ctx.fillStyle = color
        this.ctx.textBaseline = 'top'
        
        // 计算文本尺寸
        const lines = this.wrapText(text, maxWidth - 2 * padding, this.ctx)
        const textHeight = lines.length * fontSize * lineHeight
        const canvasWidth = maxWidth
        const canvasHeight = textHeight + 2 * padding
        
        // 调整Canvas尺寸
        this.canvas.width = canvasWidth
        this.canvas.height = canvasHeight
        
        // 重新设置字体（Canvas尺寸改变后需要重新设置）
        this.ctx.font = `${fontSize}px ${fontFamily}`
        this.ctx.fillStyle = color
        this.ctx.textBaseline = 'top'
        
        // 绘制背景
        if (backgroundColor !== 'transparent') {
          this.ctx.fillStyle = backgroundColor
          this.ctx.fillRect(0, 0, canvasWidth, canvasHeight)
          this.ctx.fillStyle = color
        }
        
        // 绘制文本
        lines.forEach((line, index) => {
          const y = padding + index * fontSize * lineHeight
          this.ctx!.fillText(line, padding, y)
        })
        
        // 转换为base64图片
        const imageData = this.canvas.toDataURL('image/png')
        resolve(imageData)
        
      } catch (error) {
        reject(error)
      }
    })
  }
  
  // 文本换行处理
  private static wrapText(
    text: string, 
    maxWidth: number, 
    ctx: CanvasRenderingContext2D
  ): string[] {
    const lines: string[] = []
    const paragraphs = text.split('\n')
    
    paragraphs.forEach(paragraph => {
      if (paragraph.trim() === '') {
        lines.push('')
        return
      }
      
      const words = paragraph.split('')
      let currentLine = ''
      
      for (const char of words) {
        const testLine = currentLine + char
        const metrics = ctx.measureText(testLine)
        
        if (metrics.width > maxWidth && currentLine !== '') {
          lines.push(currentLine)
          currentLine = char
        } else {
          currentLine = testLine
        }
      }
      
      if (currentLine) {
        lines.push(currentLine)
      }
    })
    
    return lines
  }
  
  // 批量渲染多个文本块
  static async renderMultipleTexts(
    texts: Array<{ text: string; options?: CanvasTextOptions }>
  ): Promise<string[]> {
    const results: string[] = []
    
    for (const item of texts) {
      try {
        const imageData = await this.renderTextToImage(item.text, item.options)
        results.push(imageData)
      } catch (error) {
        console.warn('Failed to render text:', item.text, error)
        results.push('')
      }
    }
    
    return results
  }
  
  // 清理资源
  static cleanup(): void {
    if (this.canvas) {
      this.canvas.remove()
      this.canvas = null
      this.ctx = null
    }
  }
}

// PDF中文文本渲染器
export class PDFChineseTextRenderer {
  // 检查是否需要使用Canvas渲染
  static needsCanvasRendering(text: string): boolean {
    return /[\u4e00-\u9fff]/.test(text)
  }
  
  // 为PDF渲染中文文本
  static async renderForPDF(
    pdf: any,
    text: string,
    x: number,
    y: number,
    options: CanvasTextOptions & { width?: number; height?: number } = {}
  ): Promise<void> {
    try {
      if (!this.needsCanvasRendering(text)) {
        // 非中文文本，直接使用PDF文本渲染
        pdf.setFont('helvetica')
        pdf.text(text, x, y)
        return
      }
      
      // 渲染为图片
      const imageData = await ChineseCanvasRenderer.renderTextToImage(text, {
        fontSize: 12,
        fontFamily: 'Microsoft YaHei, SimSun, sans-serif',
        ...options
      })
      
      if (imageData) {
        // 计算图片尺寸
        const imgWidth = options.width || 100
        const imgHeight = options.height || 20
        
        // 添加图片到PDF
        pdf.addImage(imageData, 'PNG', x, y, imgWidth, imgHeight)
      } else {
        // Fallback到文本
        const fallbackText = text.replace(/[\u4e00-\u9fff]/g, '?')
        pdf.setFont('helvetica')
        pdf.text(fallbackText, x, y)
      }
      
    } catch (error) {
      console.warn('Failed to render Chinese text:', error)
      // Fallback到简单文本
      const fallbackText = text.replace(/[\u4e00-\u9fff]/g, '?')
      pdf.setFont('helvetica')
      pdf.text(fallbackText, x, y)
    }
  }
}
