import { QuestionService } from './question'

/**
 * 测试AI组卷错误处理功能
 */
export class ErrorHandlingTest {
  
  /**
   * 测试JSON解析错误处理
   */
  static testJsonParsingErrorHandling() {
    console.log('🧪 开始测试JSON解析错误处理...')
    
    // 测试用例1: 包含Exception的响应
    const errorResponse1 = "Exception:'表单收集' is undefined参照这个，帮我连一下智能体"
    
    try {
      // @ts-ignore - 访问私有方法进行测试
      const result1 = QuestionService.parsePaperResponse(errorResponse1, 5)
      console.log('❌ 测试失败: 应该抛出异常但没有抛出')
    } catch (error) {
      console.log('✅ 测试通过: 正确捕获了Exception错误')
      console.log('错误信息:', error instanceof Error ? error.message : error)
    }
    
    // 测试用例2: 包含Error的响应
    const errorResponse2 = "Error: Something went wrong with the AI service"
    
    try {
      // @ts-ignore - 访问私有方法进行测试
      const result2 = QuestionService.parsePaperResponse(errorResponse2, 5)
      console.log('❌ 测试失败: 应该抛出异常但没有抛出')
    } catch (error) {
      console.log('✅ 测试通过: 正确捕获了Error错误')
      console.log('错误信息:', error instanceof Error ? error.message : error)
    }
    
    // 测试用例3: 包含undefined的响应
    const errorResponse3 = "The variable 'formData' is undefined in the current context"
    
    try {
      // @ts-ignore - 访问私有方法进行测试
      const result3 = QuestionService.parsePaperResponse(errorResponse3, 5)
      console.log('❌ 测试失败: 应该抛出异常但没有抛出')
    } catch (error) {
      console.log('✅ 测试通过: 正确捕获了undefined错误')
      console.log('错误信息:', error instanceof Error ? error.message : error)
    }
    
    // 测试用例4: 有效的JSON响应
    const validResponse = `[
      {
        "stem": "测试题目",
        "options": ["选项A", "选项B", "选项C", "选项D"],
        "answer": "A",
        "explanation": "这是解析",
        "questionType": "single_choice",
        "difficulty": "medium"
      }
    ]`
    
    try {
      // @ts-ignore - 访问私有方法进行测试
      const result4 = QuestionService.parsePaperResponse(validResponse, 5)
      console.log('✅ 测试通过: 正确解析了有效JSON')
      console.log('解析结果:', result4.length, '道题目')
    } catch (error) {
      console.log('❌ 测试失败: 有效JSON解析失败')
      console.log('错误信息:', error instanceof Error ? error.message : error)
    }
    
    // 测试用例5: 无效的JSON格式
    const invalidJsonResponse = `{
      "stem": "测试题目",
      "options": ["选项A", "选项B", "选项C", "选项D",
      "answer": "A"
      // 缺少闭合括号
    `
    
    try {
      // @ts-ignore - 访问私有方法进行测试
      const result5 = QuestionService.parsePaperResponse(invalidJsonResponse, 5)
      console.log('❌ 测试失败: 应该抛出JSON解析异常')
    } catch (error) {
      console.log('✅ 测试通过: 正确捕获了JSON格式错误')
      console.log('错误信息:', error instanceof Error ? error.message : error)
    }
    
    console.log('🎯 JSON解析错误处理测试完成')
  }
  
  /**
   * 测试完整的AI组卷流程错误处理
   */
  static async testFullGenerationErrorHandling() {
    console.log('🧪 开始测试完整AI组卷错误处理...')
    
    const testParams = {
      filterParams: {
        grade: '初一',
        subject: '数学',
        knowledgePoints: ['基础知识点'],
        questionTypes: ['single_choice'],
        difficulty: ['medium'],
        region: '通用教材',
        questionCount: 5
      },
      count: 5
    }
    
    try {
      const result = await QuestionService.generatePaper(testParams)
      console.log('AI组卷结果:', result.length, '道题目')
      
      if (result.length > 0) {
        console.log('✅ AI组卷成功')
        console.log('第一道题目:', result[0].content.stem)
      } else {
        console.log('⚠️ AI组卷返回空结果')
      }
    } catch (error) {
      console.log('❌ AI组卷失败')
      console.log('错误信息:', error instanceof Error ? error.message : error)
    }
    
    console.log('🎯 完整AI组卷错误处理测试完成')
  }
  
  /**
   * 运行所有测试
   */
  static async runAllTests() {
    console.log('🚀 开始运行所有错误处理测试...')
    console.log('=' * 50)
    
    // 运行JSON解析测试
    this.testJsonParsingErrorHandling()
    
    console.log('\n' + '=' * 50)
    
    // 运行完整流程测试
    await this.testFullGenerationErrorHandling()
    
    console.log('\n' + '=' * 50)
    console.log('🏁 所有测试完成')
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数暴露到全局
  (window as any).ErrorHandlingTest = ErrorHandlingTest
}
