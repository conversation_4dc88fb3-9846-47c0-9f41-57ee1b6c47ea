var Uo=Object.defineProperty,qo=Object.defineProperties;var Ho=Object.getOwnPropertyDescriptors;var Xi=Object.getOwnPropertySymbols;var Ko=Object.prototype.hasOwnProperty,Go=Object.prototype.propertyIsEnumerable;var Zi=(b,m,o)=>m in b?Uo(b,m,{enumerable:!0,configurable:!0,writable:!0,value:o}):b[m]=o,Se=(b,m)=>{for(var o in m||(m={}))Ko.call(m,o)&&Zi(b,o,m[o]);if(Xi)for(var o of Xi(m))Go.call(m,o)&&Zi(b,o,m[o]);return b},Oi=(b,m)=>qo(b,Ho(m));var We=(b,m,o)=>new Promise((n,e)=>{var h=a=>{try{l(o.next(a))}catch(s){e(s)}},t=a=>{try{l(o.throw(a))}catch(s){e(s)}},l=a=>a.done?n(a.value):Promise.resolve(a.value).then(h,t);l((o=o.apply(b,m)).next())});import{aC as zo,x as jr,r as ne,c as Wn,j as hn,al as Ft,y as zt,A as ht,Q as at,I as ft,z as Ct,M as Vt,O as Kt,D as Vo,K as nn,P as $e,a6 as Ye,ab as Qo,h as po,W as Wo,n as wr,S as go,H as Ue,a4 as $o,ax as Yo}from"./vendor-CPqkYfXn.js";import{u as Xo,Q as On}from"./filter-t8MhSl_r.js";import{g as yo,c as qi,d as ue,b as ji,a as le,e as Zo,v as Jo,f as ta,m as ea,p as na}from"./ui-CjjzzDsP.js";import{_ as Hi}from"./index-BdEKvwRr.js";import"./utils-DxgFcSvi.js";var mo={exports:{}};const ra=yo(zo);/**!
 * Sortable 1.14.0
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function Ji(b,m){var o=Object.keys(b);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(b);m&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(b,e).enumerable})),o.push.apply(o,n)}return o}function Ke(b){for(var m=1;m<arguments.length;m++){var o=arguments[m]!=null?arguments[m]:{};m%2?Ji(Object(o),!0).forEach(function(n){ia(b,n,o[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(b,Object.getOwnPropertyDescriptors(o)):Ji(Object(o)).forEach(function(n){Object.defineProperty(b,n,Object.getOwnPropertyDescriptor(o,n))})}return b}function Er(b){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Er=function(m){return typeof m}:Er=function(m){return m&&typeof Symbol=="function"&&m.constructor===Symbol&&m!==Symbol.prototype?"symbol":typeof m},Er(b)}function ia(b,m,o){return m in b?Object.defineProperty(b,m,{value:o,enumerable:!0,configurable:!0,writable:!0}):b[m]=o,b}function Ie(){return Ie=Object.assign||function(b){for(var m=1;m<arguments.length;m++){var o=arguments[m];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(b[n]=o[n])}return b},Ie.apply(this,arguments)}function oa(b,m){if(b==null)return{};var o={},n=Object.keys(b),e,h;for(h=0;h<n.length;h++)e=n[h],!(m.indexOf(e)>=0)&&(o[e]=b[e]);return o}function aa(b,m){if(b==null)return{};var o=oa(b,m),n,e;if(Object.getOwnPropertySymbols){var h=Object.getOwnPropertySymbols(b);for(e=0;e<h.length;e++)n=h[e],!(m.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(b,n)&&(o[n]=b[n])}return o}function la(b){return ua(b)||sa(b)||fa(b)||ca()}function ua(b){if(Array.isArray(b))return Mi(b)}function sa(b){if(typeof Symbol!="undefined"&&b[Symbol.iterator]!=null||b["@@iterator"]!=null)return Array.from(b)}function fa(b,m){if(b){if(typeof b=="string")return Mi(b,m);var o=Object.prototype.toString.call(b).slice(8,-1);if(o==="Object"&&b.constructor&&(o=b.constructor.name),o==="Map"||o==="Set")return Array.from(b);if(o==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return Mi(b,m)}}function Mi(b,m){(m==null||m>b.length)&&(m=b.length);for(var o=0,n=new Array(m);o<m;o++)n[o]=b[o];return n}function ca(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var da="1.14.0";function Xe(b){if(typeof window!="undefined"&&window.navigator)return!!navigator.userAgent.match(b)}var Ze=Xe(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Yn=Xe(/Edge/i),to=Xe(/firefox/i),Kn=Xe(/safari/i)&&!Xe(/chrome/i)&&!Xe(/android/i),bo=Xe(/iP(ad|od|hone)/i),ha=Xe(/chrome/i)&&Xe(/android/i),Oo={capture:!1,passive:!1};function _t(b,m,o){b.addEventListener(m,o,!Ze&&Oo)}function Lt(b,m,o){b.removeEventListener(m,o,!Ze&&Oo)}function Pr(b,m){if(m){if(m[0]===">"&&(m=m.substring(1)),b)try{if(b.matches)return b.matches(m);if(b.msMatchesSelector)return b.msMatchesSelector(m);if(b.webkitMatchesSelector)return b.webkitMatchesSelector(m)}catch(o){return!1}return!1}}function va(b){return b.host&&b!==document&&b.host.nodeType?b.host:b.parentNode}function je(b,m,o,n){if(b){o=o||document;do{if(m!=null&&(m[0]===">"?b.parentNode===o&&Pr(b,m):Pr(b,m))||n&&b===o)return b;if(b===o)break}while(b=va(b))}return null}var eo=/\s+/g;function Xt(b,m,o){if(b&&m)if(b.classList)b.classList[o?"add":"remove"](m);else{var n=(" "+b.className+" ").replace(eo," ").replace(" "+m+" "," ");b.className=(n+(o?" "+m:"")).replace(eo," ")}}function vt(b,m,o){var n=b&&b.style;if(n){if(o===void 0)return document.defaultView&&document.defaultView.getComputedStyle?o=document.defaultView.getComputedStyle(b,""):b.currentStyle&&(o=b.currentStyle),m===void 0?o:o[m];!(m in n)&&m.indexOf("webkit")===-1&&(m="-webkit-"+m),n[m]=o+(typeof o=="string"?"":"px")}}function vn(b,m){var o="";if(typeof b=="string")o=b;else do{var n=vt(b,"transform");n&&n!=="none"&&(o=n+" "+o)}while(!m&&(b=b.parentNode));var e=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return e&&new e(o)}function Eo(b,m,o){if(b){var n=b.getElementsByTagName(m),e=0,h=n.length;if(o)for(;e<h;e++)o(n[e],e);return n}return[]}function He(){var b=document.scrollingElement;return b||document.documentElement}function $t(b,m,o,n,e){if(!(!b.getBoundingClientRect&&b!==window)){var h,t,l,a,s,f,i;if(b!==window&&b.parentNode&&b!==He()?(h=b.getBoundingClientRect(),t=h.top,l=h.left,a=h.bottom,s=h.right,f=h.height,i=h.width):(t=0,l=0,a=window.innerHeight,s=window.innerWidth,f=window.innerHeight,i=window.innerWidth),(m||o)&&b!==window&&(e=e||b.parentNode,!Ze))do if(e&&e.getBoundingClientRect&&(vt(e,"transform")!=="none"||o&&vt(e,"position")!=="static")){var r=e.getBoundingClientRect();t-=r.top+parseInt(vt(e,"border-top-width")),l-=r.left+parseInt(vt(e,"border-left-width")),a=t+h.height,s=l+h.width;break}while(e=e.parentNode);if(n&&b!==window){var u=vn(e||b),y=u&&u.a,g=u&&u.d;u&&(t/=g,l/=y,i/=y,f/=g,a=t+f,s=l+i)}return{top:t,left:l,bottom:a,right:s,width:i,height:f}}}function no(b,m,o){for(var n=on(b,!0),e=$t(b)[m];n;){var h=$t(n)[o],t=void 0;if(t=e>=h,!t)return n;if(n===He())break;n=on(n,!1)}return!1}function wn(b,m,o,n){for(var e=0,h=0,t=b.children;h<t.length;){if(t[h].style.display!=="none"&&t[h]!==Tt.ghost&&(n||t[h]!==Tt.dragged)&&je(t[h],o.draggable,b,!1)){if(e===m)return t[h];e++}h++}return null}function Ki(b,m){for(var o=b.lastElementChild;o&&(o===Tt.ghost||vt(o,"display")==="none"||m&&!Pr(o,m));)o=o.previousElementSibling;return o||null}function ee(b,m){var o=0;if(!b||!b.parentNode)return-1;for(;b=b.previousElementSibling;)b.nodeName.toUpperCase()!=="TEMPLATE"&&b!==Tt.clone&&(!m||Pr(b,m))&&o++;return o}function ro(b){var m=0,o=0,n=He();if(b)do{var e=vn(b),h=e.a,t=e.d;m+=b.scrollLeft*h,o+=b.scrollTop*t}while(b!==n&&(b=b.parentNode));return[m,o]}function pa(b,m){for(var o in b)if(b.hasOwnProperty(o)){for(var n in m)if(m.hasOwnProperty(n)&&m[n]===b[o][n])return Number(o)}return-1}function on(b,m){if(!b||!b.getBoundingClientRect)return He();var o=b,n=!1;do if(o.clientWidth<o.scrollWidth||o.clientHeight<o.scrollHeight){var e=vt(o);if(o.clientWidth<o.scrollWidth&&(e.overflowX=="auto"||e.overflowX=="scroll")||o.clientHeight<o.scrollHeight&&(e.overflowY=="auto"||e.overflowY=="scroll")){if(!o.getBoundingClientRect||o===document.body)return He();if(n||m)return o;n=!0}}while(o=o.parentNode);return He()}function ga(b,m){if(b&&m)for(var o in m)m.hasOwnProperty(o)&&(b[o]=m[o]);return b}function Ei(b,m){return Math.round(b.top)===Math.round(m.top)&&Math.round(b.left)===Math.round(m.left)&&Math.round(b.height)===Math.round(m.height)&&Math.round(b.width)===Math.round(m.width)}var Gn;function So(b,m){return function(){if(!Gn){var o=arguments,n=this;o.length===1?b.call(n,o[0]):b.apply(n,o),Gn=setTimeout(function(){Gn=void 0},m)}}}function ya(){clearTimeout(Gn),Gn=void 0}function xo(b,m,o){b.scrollLeft+=m,b.scrollTop+=o}function Gi(b){var m=window.Polymer,o=window.jQuery||window.Zepto;return m&&m.dom?m.dom(b).cloneNode(!0):o?o(b).clone(!0)[0]:b.cloneNode(!0)}function io(b,m){vt(b,"position","absolute"),vt(b,"top",m.top),vt(b,"left",m.left),vt(b,"width",m.width),vt(b,"height",m.height)}function Si(b){vt(b,"position",""),vt(b,"top",""),vt(b,"left",""),vt(b,"width",""),vt(b,"height","")}var ye="Sortable"+new Date().getTime();function ma(){var b=[],m;return{captureAnimationState:function(){if(b=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(e){if(!(vt(e,"display")==="none"||e===Tt.ghost)){b.push({target:e,rect:$t(e)});var h=Ke({},b[b.length-1].rect);if(e.thisAnimationDuration){var t=vn(e,!0);t&&(h.top-=t.f,h.left-=t.e)}e.fromRect=h}})}},addAnimationState:function(n){b.push(n)},removeAnimationState:function(n){b.splice(pa(b,{target:n}),1)},animateAll:function(n){var e=this;if(!this.options.animation){clearTimeout(m),typeof n=="function"&&n();return}var h=!1,t=0;b.forEach(function(l){var a=0,s=l.target,f=s.fromRect,i=$t(s),r=s.prevFromRect,u=s.prevToRect,y=l.rect,g=vn(s,!0);g&&(i.top-=g.f,i.left-=g.e),s.toRect=i,s.thisAnimationDuration&&Ei(r,i)&&!Ei(f,i)&&(y.top-i.top)/(y.left-i.left)===(f.top-i.top)/(f.left-i.left)&&(a=Oa(y,r,u,e.options)),Ei(i,f)||(s.prevFromRect=f,s.prevToRect=i,a||(a=e.options.animation),e.animate(s,y,i,a)),a&&(h=!0,t=Math.max(t,a),clearTimeout(s.animationResetTimer),s.animationResetTimer=setTimeout(function(){s.animationTime=0,s.prevFromRect=null,s.fromRect=null,s.prevToRect=null,s.thisAnimationDuration=null},a),s.thisAnimationDuration=a)}),clearTimeout(m),h?m=setTimeout(function(){typeof n=="function"&&n()},t):typeof n=="function"&&n(),b=[]},animate:function(n,e,h,t){if(t){vt(n,"transition",""),vt(n,"transform","");var l=vn(this.el),a=l&&l.a,s=l&&l.d,f=(e.left-h.left)/(a||1),i=(e.top-h.top)/(s||1);n.animatingX=!!f,n.animatingY=!!i,vt(n,"transform","translate3d("+f+"px,"+i+"px,0)"),this.forRepaintDummy=ba(n),vt(n,"transition","transform "+t+"ms"+(this.options.easing?" "+this.options.easing:"")),vt(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){vt(n,"transition",""),vt(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},t)}}}}function ba(b){return b.offsetWidth}function Oa(b,m,o,n){return Math.sqrt(Math.pow(m.top-b.top,2)+Math.pow(m.left-b.left,2))/Math.sqrt(Math.pow(m.top-o.top,2)+Math.pow(m.left-o.left,2))*n.animation}var En=[],xi={initializeByDefault:!0},Xn={mount:function(m){for(var o in xi)xi.hasOwnProperty(o)&&!(o in m)&&(m[o]=xi[o]);En.forEach(function(n){if(n.pluginName===m.pluginName)throw"Sortable: Cannot mount plugin ".concat(m.pluginName," more than once")}),En.push(m)},pluginEvent:function(m,o,n){var e=this;this.eventCanceled=!1,n.cancel=function(){e.eventCanceled=!0};var h=m+"Global";En.forEach(function(t){o[t.pluginName]&&(o[t.pluginName][h]&&o[t.pluginName][h](Ke({sortable:o},n)),o.options[t.pluginName]&&o[t.pluginName][m]&&o[t.pluginName][m](Ke({sortable:o},n)))})},initializePlugins:function(m,o,n,e){En.forEach(function(l){var a=l.pluginName;if(!(!m.options[a]&&!l.initializeByDefault)){var s=new l(m,o,m.options);s.sortable=m,s.options=m.options,m[a]=s,Ie(n,s.defaults)}});for(var h in m.options)if(m.options.hasOwnProperty(h)){var t=this.modifyOption(m,h,m.options[h]);typeof t!="undefined"&&(m.options[h]=t)}},getEventProperties:function(m,o){var n={};return En.forEach(function(e){typeof e.eventProperties=="function"&&Ie(n,e.eventProperties.call(o[e.pluginName],m))}),n},modifyOption:function(m,o,n){var e;return En.forEach(function(h){m[h.pluginName]&&h.optionListeners&&typeof h.optionListeners[o]=="function"&&(e=h.optionListeners[o].call(m[h.pluginName],n))}),e}};function Fn(b){var m=b.sortable,o=b.rootEl,n=b.name,e=b.targetEl,h=b.cloneEl,t=b.toEl,l=b.fromEl,a=b.oldIndex,s=b.newIndex,f=b.oldDraggableIndex,i=b.newDraggableIndex,r=b.originalEvent,u=b.putSortable,y=b.extraEventProperties;if(m=m||o&&o[ye],!!m){var g,p=m.options,d="on"+n.charAt(0).toUpperCase()+n.substr(1);window.CustomEvent&&!Ze&&!Yn?g=new CustomEvent(n,{bubbles:!0,cancelable:!0}):(g=document.createEvent("Event"),g.initEvent(n,!0,!0)),g.to=t||o,g.from=l||o,g.item=e||o,g.clone=h,g.oldIndex=a,g.newIndex=s,g.oldDraggableIndex=f,g.newDraggableIndex=i,g.originalEvent=r,g.pullMode=u?u.lastPutMode:void 0;var c=Ke(Ke({},y),Xn.getEventProperties(n,m));for(var O in c)g[O]=c[O];o&&o.dispatchEvent(g),p[d]&&p[d].call(m,g)}}var Ea=["evt"],xe=function(m,o){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},e=n.evt,h=aa(n,Ea);Xn.pluginEvent.bind(Tt)(m,o,Ke({dragEl:lt,parentEl:Jt,ghostEl:It,rootEl:Wt,nextEl:dn,lastDownEl:Sr,cloneEl:te,cloneHidden:rn,dragStarted:Un,putSortable:de,activeSortable:Tt.active,originalEvent:e,oldIndex:An,oldDraggableIndex:zn,newIndex:Pe,newDraggableIndex:en,hideGhostForTarget:Po,unhideGhostForTarget:No,cloneNowHidden:function(){rn=!0},cloneNowShown:function(){rn=!1},dispatchSortableEvent:function(l){be({sortable:o,name:l,originalEvent:e})}},h))};function be(b){Fn(Ke({putSortable:de,cloneEl:te,targetEl:lt,rootEl:Wt,oldIndex:An,oldDraggableIndex:zn,newIndex:Pe,newDraggableIndex:en},b))}var lt,Jt,It,Wt,dn,Sr,te,rn,An,Pe,zn,en,dr,de,Tn=!1,Nr=!1,Ir=[],fn,Le,Ti,Ai,oo,ao,Un,Sn,Vn,Qn=!1,hr=!1,xr,ge,wi=[],_i=!1,Dr=[],Mr=typeof document!="undefined",vr=bo,lo=Yn||Ze?"cssFloat":"float",Sa=Mr&&!ha&&!bo&&"draggable"in document.createElement("div"),To=function(){if(Mr){if(Ze)return!1;var b=document.createElement("x");return b.style.cssText="pointer-events:auto",b.style.pointerEvents==="auto"}}(),Ao=function(m,o){var n=vt(m),e=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),h=wn(m,0,o),t=wn(m,1,o),l=h&&vt(h),a=t&&vt(t),s=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+$t(h).width,f=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+$t(t).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(h&&l.float&&l.float!=="none"){var i=l.float==="left"?"left":"right";return t&&(a.clear==="both"||a.clear===i)?"vertical":"horizontal"}return h&&(l.display==="block"||l.display==="flex"||l.display==="table"||l.display==="grid"||s>=e&&n[lo]==="none"||t&&n[lo]==="none"&&s+f>e)?"vertical":"horizontal"},xa=function(m,o,n){var e=n?m.left:m.top,h=n?m.right:m.bottom,t=n?m.width:m.height,l=n?o.left:o.top,a=n?o.right:o.bottom,s=n?o.width:o.height;return e===l||h===a||e+t/2===l+s/2},Ta=function(m,o){var n;return Ir.some(function(e){var h=e[ye].options.emptyInsertThreshold;if(!(!h||Ki(e))){var t=$t(e),l=m>=t.left-h&&m<=t.right+h,a=o>=t.top-h&&o<=t.bottom+h;if(l&&a)return n=e}}),n},wo=function(m){function o(h,t){return function(l,a,s,f){var i=l.options.group.name&&a.options.group.name&&l.options.group.name===a.options.group.name;if(h==null&&(t||i))return!0;if(h==null||h===!1)return!1;if(t&&h==="clone")return h;if(typeof h=="function")return o(h(l,a,s,f),t)(l,a,s,f);var r=(t?l:a).options.group.name;return h===!0||typeof h=="string"&&h===r||h.join&&h.indexOf(r)>-1}}var n={},e=m.group;(!e||Er(e)!="object")&&(e={name:e}),n.name=e.name,n.checkPull=o(e.pull,!0),n.checkPut=o(e.put),n.revertClone=e.revertClone,m.group=n},Po=function(){!To&&It&&vt(It,"display","none")},No=function(){!To&&It&&vt(It,"display","")};Mr&&document.addEventListener("click",function(b){if(Nr)return b.preventDefault(),b.stopPropagation&&b.stopPropagation(),b.stopImmediatePropagation&&b.stopImmediatePropagation(),Nr=!1,!1},!0);var cn=function(m){if(lt){m=m.touches?m.touches[0]:m;var o=Ta(m.clientX,m.clientY);if(o){var n={};for(var e in m)m.hasOwnProperty(e)&&(n[e]=m[e]);n.target=n.rootEl=o,n.preventDefault=void 0,n.stopPropagation=void 0,o[ye]._onDragOver(n)}}},Aa=function(m){lt&&lt.parentNode[ye]._isOutsideThisEl(m.target)};function Tt(b,m){if(!(b&&b.nodeType&&b.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(b));this.el=b,this.options=m=Ie({},m),b[ye]=this;var o={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(b.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Ao(b,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(t,l){t.setData("Text",l.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:Tt.supportPointer!==!1&&"PointerEvent"in window&&!Kn,emptyInsertThreshold:5};Xn.initializePlugins(this,b,o);for(var n in o)!(n in m)&&(m[n]=o[n]);wo(m);for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this));this.nativeDraggable=m.forceFallback?!1:Sa,this.nativeDraggable&&(this.options.touchStartThreshold=1),m.supportPointer?_t(b,"pointerdown",this._onTapStart):(_t(b,"mousedown",this._onTapStart),_t(b,"touchstart",this._onTapStart)),this.nativeDraggable&&(_t(b,"dragover",this),_t(b,"dragenter",this)),Ir.push(this.el),m.store&&m.store.get&&this.sort(m.store.get(this)||[]),Ie(this,ma())}Tt.prototype={constructor:Tt,_isOutsideThisEl:function(m){!this.el.contains(m)&&m!==this.el&&(Sn=null)},_getDirection:function(m,o){return typeof this.options.direction=="function"?this.options.direction.call(this,m,o,lt):this.options.direction},_onTapStart:function(m){if(m.cancelable){var o=this,n=this.el,e=this.options,h=e.preventOnFilter,t=m.type,l=m.touches&&m.touches[0]||m.pointerType&&m.pointerType==="touch"&&m,a=(l||m).target,s=m.target.shadowRoot&&(m.path&&m.path[0]||m.composedPath&&m.composedPath()[0])||a,f=e.filter;if(Ca(n),!lt&&!(/mousedown|pointerdown/.test(t)&&m.button!==0||e.disabled)&&!s.isContentEditable&&!(!this.nativeDraggable&&Kn&&a&&a.tagName.toUpperCase()==="SELECT")&&(a=je(a,e.draggable,n,!1),!(a&&a.animated)&&Sr!==a)){if(An=ee(a),zn=ee(a,e.draggable),typeof f=="function"){if(f.call(this,m,a,this)){be({sortable:o,rootEl:s,name:"filter",targetEl:a,toEl:n,fromEl:n}),xe("filter",o,{evt:m}),h&&m.cancelable&&m.preventDefault();return}}else if(f&&(f=f.split(",").some(function(i){if(i=je(s,i.trim(),n,!1),i)return be({sortable:o,rootEl:i,name:"filter",targetEl:a,fromEl:n,toEl:n}),xe("filter",o,{evt:m}),!0}),f)){h&&m.cancelable&&m.preventDefault();return}e.handle&&!je(s,e.handle,n,!1)||this._prepareDragStart(m,l,a)}}},_prepareDragStart:function(m,o,n){var e=this,h=e.el,t=e.options,l=h.ownerDocument,a;if(n&&!lt&&n.parentNode===h){var s=$t(n);if(Wt=h,lt=n,Jt=lt.parentNode,dn=lt.nextSibling,Sr=n,dr=t.group,Tt.dragged=lt,fn={target:lt,clientX:(o||m).clientX,clientY:(o||m).clientY},oo=fn.clientX-s.left,ao=fn.clientY-s.top,this._lastX=(o||m).clientX,this._lastY=(o||m).clientY,lt.style["will-change"]="all",a=function(){if(xe("delayEnded",e,{evt:m}),Tt.eventCanceled){e._onDrop();return}e._disableDelayedDragEvents(),!to&&e.nativeDraggable&&(lt.draggable=!0),e._triggerDragStart(m,o),be({sortable:e,name:"choose",originalEvent:m}),Xt(lt,t.chosenClass,!0)},t.ignore.split(",").forEach(function(f){Eo(lt,f.trim(),Pi)}),_t(l,"dragover",cn),_t(l,"mousemove",cn),_t(l,"touchmove",cn),_t(l,"mouseup",e._onDrop),_t(l,"touchend",e._onDrop),_t(l,"touchcancel",e._onDrop),to&&this.nativeDraggable&&(this.options.touchStartThreshold=4,lt.draggable=!0),xe("delayStart",this,{evt:m}),t.delay&&(!t.delayOnTouchOnly||o)&&(!this.nativeDraggable||!(Yn||Ze))){if(Tt.eventCanceled){this._onDrop();return}_t(l,"mouseup",e._disableDelayedDrag),_t(l,"touchend",e._disableDelayedDrag),_t(l,"touchcancel",e._disableDelayedDrag),_t(l,"mousemove",e._delayedDragTouchMoveHandler),_t(l,"touchmove",e._delayedDragTouchMoveHandler),t.supportPointer&&_t(l,"pointermove",e._delayedDragTouchMoveHandler),e._dragStartTimer=setTimeout(a,t.delay)}else a()}},_delayedDragTouchMoveHandler:function(m){var o=m.touches?m.touches[0]:m;Math.max(Math.abs(o.clientX-this._lastX),Math.abs(o.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){lt&&Pi(lt),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var m=this.el.ownerDocument;Lt(m,"mouseup",this._disableDelayedDrag),Lt(m,"touchend",this._disableDelayedDrag),Lt(m,"touchcancel",this._disableDelayedDrag),Lt(m,"mousemove",this._delayedDragTouchMoveHandler),Lt(m,"touchmove",this._delayedDragTouchMoveHandler),Lt(m,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(m,o){o=o||m.pointerType=="touch"&&m,!this.nativeDraggable||o?this.options.supportPointer?_t(document,"pointermove",this._onTouchMove):o?_t(document,"touchmove",this._onTouchMove):_t(document,"mousemove",this._onTouchMove):(_t(lt,"dragend",this),_t(Wt,"dragstart",this._onDragStart));try{document.selection?Tr(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch(n){}},_dragStarted:function(m,o){if(Tn=!1,Wt&&lt){xe("dragStarted",this,{evt:o}),this.nativeDraggable&&_t(document,"dragover",Aa);var n=this.options;!m&&Xt(lt,n.dragClass,!1),Xt(lt,n.ghostClass,!0),Tt.active=this,m&&this._appendGhost(),be({sortable:this,name:"start",originalEvent:o})}else this._nulling()},_emulateDragOver:function(){if(Le){this._lastX=Le.clientX,this._lastY=Le.clientY,Po();for(var m=document.elementFromPoint(Le.clientX,Le.clientY),o=m;m&&m.shadowRoot&&(m=m.shadowRoot.elementFromPoint(Le.clientX,Le.clientY),m!==o);)o=m;if(lt.parentNode[ye]._isOutsideThisEl(m),o)do{if(o[ye]){var n=void 0;if(n=o[ye]._onDragOver({clientX:Le.clientX,clientY:Le.clientY,target:m,rootEl:o}),n&&!this.options.dragoverBubble)break}m=o}while(o=o.parentNode);No()}},_onTouchMove:function(m){if(fn){var o=this.options,n=o.fallbackTolerance,e=o.fallbackOffset,h=m.touches?m.touches[0]:m,t=It&&vn(It,!0),l=It&&t&&t.a,a=It&&t&&t.d,s=vr&&ge&&ro(ge),f=(h.clientX-fn.clientX+e.x)/(l||1)+(s?s[0]-wi[0]:0)/(l||1),i=(h.clientY-fn.clientY+e.y)/(a||1)+(s?s[1]-wi[1]:0)/(a||1);if(!Tt.active&&!Tn){if(n&&Math.max(Math.abs(h.clientX-this._lastX),Math.abs(h.clientY-this._lastY))<n)return;this._onDragStart(m,!0)}if(It){t?(t.e+=f-(Ti||0),t.f+=i-(Ai||0)):t={a:1,b:0,c:0,d:1,e:f,f:i};var r="matrix(".concat(t.a,",").concat(t.b,",").concat(t.c,",").concat(t.d,",").concat(t.e,",").concat(t.f,")");vt(It,"webkitTransform",r),vt(It,"mozTransform",r),vt(It,"msTransform",r),vt(It,"transform",r),Ti=f,Ai=i,Le=h}m.cancelable&&m.preventDefault()}},_appendGhost:function(){if(!It){var m=this.options.fallbackOnBody?document.body:Wt,o=$t(lt,!0,vr,!0,m),n=this.options;if(vr){for(ge=m;vt(ge,"position")==="static"&&vt(ge,"transform")==="none"&&ge!==document;)ge=ge.parentNode;ge!==document.body&&ge!==document.documentElement?(ge===document&&(ge=He()),o.top+=ge.scrollTop,o.left+=ge.scrollLeft):ge=He(),wi=ro(ge)}It=lt.cloneNode(!0),Xt(It,n.ghostClass,!1),Xt(It,n.fallbackClass,!0),Xt(It,n.dragClass,!0),vt(It,"transition",""),vt(It,"transform",""),vt(It,"box-sizing","border-box"),vt(It,"margin",0),vt(It,"top",o.top),vt(It,"left",o.left),vt(It,"width",o.width),vt(It,"height",o.height),vt(It,"opacity","0.8"),vt(It,"position",vr?"absolute":"fixed"),vt(It,"zIndex","100000"),vt(It,"pointerEvents","none"),Tt.ghost=It,m.appendChild(It),vt(It,"transform-origin",oo/parseInt(It.style.width)*100+"% "+ao/parseInt(It.style.height)*100+"%")}},_onDragStart:function(m,o){var n=this,e=m.dataTransfer,h=n.options;if(xe("dragStart",this,{evt:m}),Tt.eventCanceled){this._onDrop();return}xe("setupClone",this),Tt.eventCanceled||(te=Gi(lt),te.draggable=!1,te.style["will-change"]="",this._hideClone(),Xt(te,this.options.chosenClass,!1),Tt.clone=te),n.cloneId=Tr(function(){xe("clone",n),!Tt.eventCanceled&&(n.options.removeCloneOnHide||Wt.insertBefore(te,lt),n._hideClone(),be({sortable:n,name:"clone"}))}),!o&&Xt(lt,h.dragClass,!0),o?(Nr=!0,n._loopId=setInterval(n._emulateDragOver,50)):(Lt(document,"mouseup",n._onDrop),Lt(document,"touchend",n._onDrop),Lt(document,"touchcancel",n._onDrop),e&&(e.effectAllowed="move",h.setData&&h.setData.call(n,e,lt)),_t(document,"drop",n),vt(lt,"transform","translateZ(0)")),Tn=!0,n._dragStartId=Tr(n._dragStarted.bind(n,o,m)),_t(document,"selectstart",n),Un=!0,Kn&&vt(document.body,"user-select","none")},_onDragOver:function(m){var o=this.el,n=m.target,e,h,t,l=this.options,a=l.group,s=Tt.active,f=dr===a,i=l.sort,r=de||s,u,y=this,g=!1;if(_i)return;function p(z,K){xe(z,y,Ke({evt:m,isOwner:f,axis:u?"vertical":"horizontal",revert:t,dragRect:e,targetRect:h,canSort:i,fromSortable:r,target:n,completed:c,onMove:function(L,_){return pr(Wt,o,lt,e,L,$t(L),m,_)},changed:O},K))}function d(){p("dragOverAnimationCapture"),y.captureAnimationState(),y!==r&&r.captureAnimationState()}function c(z){return p("dragOverCompleted",{insertion:z}),z&&(f?s._hideClone():s._showClone(y),y!==r&&(Xt(lt,de?de.options.ghostClass:s.options.ghostClass,!1),Xt(lt,l.ghostClass,!0)),de!==y&&y!==Tt.active?de=y:y===Tt.active&&de&&(de=null),r===y&&(y._ignoreWhileAnimating=n),y.animateAll(function(){p("dragOverAnimationComplete"),y._ignoreWhileAnimating=null}),y!==r&&(r.animateAll(),r._ignoreWhileAnimating=null)),(n===lt&&!lt.animated||n===o&&!n.animated)&&(Sn=null),!l.dragoverBubble&&!m.rootEl&&n!==document&&(lt.parentNode[ye]._isOutsideThisEl(m.target),!z&&cn(m)),!l.dragoverBubble&&m.stopPropagation&&m.stopPropagation(),g=!0}function O(){Pe=ee(lt),en=ee(lt,l.draggable),be({sortable:y,name:"change",toEl:o,newIndex:Pe,newDraggableIndex:en,originalEvent:m})}if(m.preventDefault!==void 0&&m.cancelable&&m.preventDefault(),n=je(n,l.draggable,o,!0),p("dragOver"),Tt.eventCanceled)return g;if(lt.contains(m.target)||n.animated&&n.animatingX&&n.animatingY||y._ignoreWhileAnimating===n)return c(!1);if(Nr=!1,s&&!l.disabled&&(f?i||(t=Jt!==Wt):de===this||(this.lastPutMode=dr.checkPull(this,s,lt,m))&&a.checkPut(this,s,lt,m))){if(u=this._getDirection(m,n)==="vertical",e=$t(lt),p("dragOverValid"),Tt.eventCanceled)return g;if(t)return Jt=Wt,d(),this._hideClone(),p("revert"),Tt.eventCanceled||(dn?Wt.insertBefore(lt,dn):Wt.appendChild(lt)),c(!0);var v=Ki(o,l.draggable);if(!v||Ia(m,u,this)&&!v.animated){if(v===lt)return c(!1);if(v&&o===m.target&&(n=v),n&&(h=$t(n)),pr(Wt,o,lt,e,n,h,m,!!n)!==!1)return d(),o.appendChild(lt),Jt=o,O(),c(!0)}else if(v&&Na(m,u,this)){var E=wn(o,0,l,!0);if(E===lt)return c(!1);if(n=E,h=$t(n),pr(Wt,o,lt,e,n,h,m,!1)!==!1)return d(),o.insertBefore(lt,E),Jt=o,O(),c(!0)}else if(n.parentNode===o){h=$t(n);var T=0,P,I=lt.parentNode!==o,D=!xa(lt.animated&&lt.toRect||e,n.animated&&n.toRect||h,u),w=u?"top":"left",S=no(n,"top","top")||no(lt,"top","top"),A=S?S.scrollTop:void 0;Sn!==n&&(P=h[w],Qn=!1,hr=!D&&l.invertSwap||I),T=Da(m,n,h,u,D?1:l.swapThreshold,l.invertedSwapThreshold==null?l.swapThreshold:l.invertedSwapThreshold,hr,Sn===n);var N;if(T!==0){var R=ee(lt);do R-=T,N=Jt.children[R];while(N&&(vt(N,"display")==="none"||N===It))}if(T===0||N===n)return c(!1);Sn=n,Vn=T;var C=n.nextElementSibling,B=!1;B=T===1;var U=pr(Wt,o,lt,e,n,h,m,B);if(U!==!1)return(U===1||U===-1)&&(B=U===1),_i=!0,setTimeout(Pa,30),d(),B&&!C?o.appendChild(lt):n.parentNode.insertBefore(lt,B?C:n),S&&xo(S,0,A-S.scrollTop),Jt=lt.parentNode,P!==void 0&&!hr&&(xr=Math.abs(P-$t(n)[w])),O(),c(!0)}if(o.contains(lt))return c(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){Lt(document,"mousemove",this._onTouchMove),Lt(document,"touchmove",this._onTouchMove),Lt(document,"pointermove",this._onTouchMove),Lt(document,"dragover",cn),Lt(document,"mousemove",cn),Lt(document,"touchmove",cn)},_offUpEvents:function(){var m=this.el.ownerDocument;Lt(m,"mouseup",this._onDrop),Lt(m,"touchend",this._onDrop),Lt(m,"pointerup",this._onDrop),Lt(m,"touchcancel",this._onDrop),Lt(document,"selectstart",this)},_onDrop:function(m){var o=this.el,n=this.options;if(Pe=ee(lt),en=ee(lt,n.draggable),xe("drop",this,{evt:m}),Jt=lt&&lt.parentNode,Pe=ee(lt),en=ee(lt,n.draggable),Tt.eventCanceled){this._nulling();return}Tn=!1,hr=!1,Qn=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),ki(this.cloneId),ki(this._dragStartId),this.nativeDraggable&&(Lt(document,"drop",this),Lt(o,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Kn&&vt(document.body,"user-select",""),vt(lt,"transform",""),m&&(Un&&(m.cancelable&&m.preventDefault(),!n.dropBubble&&m.stopPropagation()),It&&It.parentNode&&It.parentNode.removeChild(It),(Wt===Jt||de&&de.lastPutMode!=="clone")&&te&&te.parentNode&&te.parentNode.removeChild(te),lt&&(this.nativeDraggable&&Lt(lt,"dragend",this),Pi(lt),lt.style["will-change"]="",Un&&!Tn&&Xt(lt,de?de.options.ghostClass:this.options.ghostClass,!1),Xt(lt,this.options.chosenClass,!1),be({sortable:this,name:"unchoose",toEl:Jt,newIndex:null,newDraggableIndex:null,originalEvent:m}),Wt!==Jt?(Pe>=0&&(be({rootEl:Jt,name:"add",toEl:Jt,fromEl:Wt,originalEvent:m}),be({sortable:this,name:"remove",toEl:Jt,originalEvent:m}),be({rootEl:Jt,name:"sort",toEl:Jt,fromEl:Wt,originalEvent:m}),be({sortable:this,name:"sort",toEl:Jt,originalEvent:m})),de&&de.save()):Pe!==An&&Pe>=0&&(be({sortable:this,name:"update",toEl:Jt,originalEvent:m}),be({sortable:this,name:"sort",toEl:Jt,originalEvent:m})),Tt.active&&((Pe==null||Pe===-1)&&(Pe=An,en=zn),be({sortable:this,name:"end",toEl:Jt,originalEvent:m}),this.save()))),this._nulling()},_nulling:function(){xe("nulling",this),Wt=lt=Jt=It=dn=te=Sr=rn=fn=Le=Un=Pe=en=An=zn=Sn=Vn=de=dr=Tt.dragged=Tt.ghost=Tt.clone=Tt.active=null,Dr.forEach(function(m){m.checked=!0}),Dr.length=Ti=Ai=0},handleEvent:function(m){switch(m.type){case"drop":case"dragend":this._onDrop(m);break;case"dragenter":case"dragover":lt&&(this._onDragOver(m),wa(m));break;case"selectstart":m.preventDefault();break}},toArray:function(){for(var m=[],o,n=this.el.children,e=0,h=n.length,t=this.options;e<h;e++)o=n[e],je(o,t.draggable,this.el,!1)&&m.push(o.getAttribute(t.dataIdAttr)||La(o));return m},sort:function(m,o){var n={},e=this.el;this.toArray().forEach(function(h,t){var l=e.children[t];je(l,this.options.draggable,e,!1)&&(n[h]=l)},this),o&&this.captureAnimationState(),m.forEach(function(h){n[h]&&(e.removeChild(n[h]),e.appendChild(n[h]))}),o&&this.animateAll()},save:function(){var m=this.options.store;m&&m.set&&m.set(this)},closest:function(m,o){return je(m,o||this.options.draggable,this.el,!1)},option:function(m,o){var n=this.options;if(o===void 0)return n[m];var e=Xn.modifyOption(this,m,o);typeof e!="undefined"?n[m]=e:n[m]=o,m==="group"&&wo(n)},destroy:function(){xe("destroy",this);var m=this.el;m[ye]=null,Lt(m,"mousedown",this._onTapStart),Lt(m,"touchstart",this._onTapStart),Lt(m,"pointerdown",this._onTapStart),this.nativeDraggable&&(Lt(m,"dragover",this),Lt(m,"dragenter",this)),Array.prototype.forEach.call(m.querySelectorAll("[draggable]"),function(o){o.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Ir.splice(Ir.indexOf(this.el),1),this.el=m=null},_hideClone:function(){if(!rn){if(xe("hideClone",this),Tt.eventCanceled)return;vt(te,"display","none"),this.options.removeCloneOnHide&&te.parentNode&&te.parentNode.removeChild(te),rn=!0}},_showClone:function(m){if(m.lastPutMode!=="clone"){this._hideClone();return}if(rn){if(xe("showClone",this),Tt.eventCanceled)return;lt.parentNode==Wt&&!this.options.group.revertClone?Wt.insertBefore(te,lt):dn?Wt.insertBefore(te,dn):Wt.appendChild(te),this.options.group.revertClone&&this.animate(lt,te),vt(te,"display",""),rn=!1}}};function wa(b){b.dataTransfer&&(b.dataTransfer.dropEffect="move"),b.cancelable&&b.preventDefault()}function pr(b,m,o,n,e,h,t,l){var a,s=b[ye],f=s.options.onMove,i;return window.CustomEvent&&!Ze&&!Yn?a=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(a=document.createEvent("Event"),a.initEvent("move",!0,!0)),a.to=m,a.from=b,a.dragged=o,a.draggedRect=n,a.related=e||m,a.relatedRect=h||$t(m),a.willInsertAfter=l,a.originalEvent=t,b.dispatchEvent(a),f&&(i=f.call(s,a,t)),i}function Pi(b){b.draggable=!1}function Pa(){_i=!1}function Na(b,m,o){var n=$t(wn(o.el,0,o.options,!0)),e=10;return m?b.clientX<n.left-e||b.clientY<n.top&&b.clientX<n.right:b.clientY<n.top-e||b.clientY<n.bottom&&b.clientX<n.left}function Ia(b,m,o){var n=$t(Ki(o.el,o.options.draggable)),e=10;return m?b.clientX>n.right+e||b.clientX<=n.right&&b.clientY>n.bottom&&b.clientX>=n.left:b.clientX>n.right&&b.clientY>n.top||b.clientX<=n.right&&b.clientY>n.bottom+e}function Da(b,m,o,n,e,h,t,l){var a=n?b.clientY:b.clientX,s=n?o.height:o.width,f=n?o.top:o.left,i=n?o.bottom:o.right,r=!1;if(!t){if(l&&xr<s*e){if(!Qn&&(Vn===1?a>f+s*h/2:a<i-s*h/2)&&(Qn=!0),Qn)r=!0;else if(Vn===1?a<f+xr:a>i-xr)return-Vn}else if(a>f+s*(1-e)/2&&a<i-s*(1-e)/2)return Ra(m)}return r=r||t,r&&(a<f+s*h/2||a>i-s*h/2)?a>f+s/2?1:-1:0}function Ra(b){return ee(lt)<ee(b)?1:-1}function La(b){for(var m=b.tagName+b.className+b.src+b.href+b.textContent,o=m.length,n=0;o--;)n+=m.charCodeAt(o);return n.toString(36)}function Ca(b){Dr.length=0;for(var m=b.getElementsByTagName("input"),o=m.length;o--;){var n=m[o];n.checked&&Dr.push(n)}}function Tr(b){return setTimeout(b,0)}function ki(b){return clearTimeout(b)}Mr&&_t(document,"touchmove",function(b){(Tt.active||Tn)&&b.cancelable&&b.preventDefault()});Tt.utils={on:_t,off:Lt,css:vt,find:Eo,is:function(m,o){return!!je(m,o,m,!1)},extend:ga,throttle:So,closest:je,toggleClass:Xt,clone:Gi,index:ee,nextTick:Tr,cancelNextTick:ki,detectDirection:Ao,getChild:wn};Tt.get=function(b){return b[ye]};Tt.mount=function(){for(var b=arguments.length,m=new Array(b),o=0;o<b;o++)m[o]=arguments[o];m[0].constructor===Array&&(m=m[0]),m.forEach(function(n){if(!n.prototype||!n.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(n));n.utils&&(Tt.utils=Ke(Ke({},Tt.utils),n.utils)),Xn.mount(n)})};Tt.create=function(b,m){return new Tt(b,m)};Tt.version=da;var oe=[],qn,Bi,Fi=!1,Ni,Ii,Rr,Hn;function ja(){function b(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var m in this)m.charAt(0)==="_"&&typeof this[m]=="function"&&(this[m]=this[m].bind(this))}return b.prototype={dragStarted:function(o){var n=o.originalEvent;this.sortable.nativeDraggable?_t(document,"dragover",this._handleAutoScroll):this.options.supportPointer?_t(document,"pointermove",this._handleFallbackAutoScroll):n.touches?_t(document,"touchmove",this._handleFallbackAutoScroll):_t(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(o){var n=o.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?Lt(document,"dragover",this._handleAutoScroll):(Lt(document,"pointermove",this._handleFallbackAutoScroll),Lt(document,"touchmove",this._handleFallbackAutoScroll),Lt(document,"mousemove",this._handleFallbackAutoScroll)),uo(),Ar(),ya()},nulling:function(){Rr=Bi=qn=Fi=Hn=Ni=Ii=null,oe.length=0},_handleFallbackAutoScroll:function(o){this._handleAutoScroll(o,!0)},_handleAutoScroll:function(o,n){var e=this,h=(o.touches?o.touches[0]:o).clientX,t=(o.touches?o.touches[0]:o).clientY,l=document.elementFromPoint(h,t);if(Rr=o,n||this.options.forceAutoScrollFallback||Yn||Ze||Kn){Di(o,this.options,l,n);var a=on(l,!0);Fi&&(!Hn||h!==Ni||t!==Ii)&&(Hn&&uo(),Hn=setInterval(function(){var s=on(document.elementFromPoint(h,t),!0);s!==a&&(a=s,Ar()),Di(o,e.options,s,n)},10),Ni=h,Ii=t)}else{if(!this.options.bubbleScroll||on(l,!0)===He()){Ar();return}Di(o,this.options,on(l,!1),!1)}}},Ie(b,{pluginName:"scroll",initializeByDefault:!0})}function Ar(){oe.forEach(function(b){clearInterval(b.pid)}),oe=[]}function uo(){clearInterval(Hn)}var Di=So(function(b,m,o,n){if(m.scroll){var e=(b.touches?b.touches[0]:b).clientX,h=(b.touches?b.touches[0]:b).clientY,t=m.scrollSensitivity,l=m.scrollSpeed,a=He(),s=!1,f;Bi!==o&&(Bi=o,Ar(),qn=m.scroll,f=m.scrollFn,qn===!0&&(qn=on(o,!0)));var i=0,r=qn;do{var u=r,y=$t(u),g=y.top,p=y.bottom,d=y.left,c=y.right,O=y.width,v=y.height,E=void 0,T=void 0,P=u.scrollWidth,I=u.scrollHeight,D=vt(u),w=u.scrollLeft,S=u.scrollTop;u===a?(E=O<P&&(D.overflowX==="auto"||D.overflowX==="scroll"||D.overflowX==="visible"),T=v<I&&(D.overflowY==="auto"||D.overflowY==="scroll"||D.overflowY==="visible")):(E=O<P&&(D.overflowX==="auto"||D.overflowX==="scroll"),T=v<I&&(D.overflowY==="auto"||D.overflowY==="scroll"));var A=E&&(Math.abs(c-e)<=t&&w+O<P)-(Math.abs(d-e)<=t&&!!w),N=T&&(Math.abs(p-h)<=t&&S+v<I)-(Math.abs(g-h)<=t&&!!S);if(!oe[i])for(var R=0;R<=i;R++)oe[R]||(oe[R]={});(oe[i].vx!=A||oe[i].vy!=N||oe[i].el!==u)&&(oe[i].el=u,oe[i].vx=A,oe[i].vy=N,clearInterval(oe[i].pid),(A!=0||N!=0)&&(s=!0,oe[i].pid=setInterval(function(){n&&this.layer===0&&Tt.active._onTouchMove(Rr);var C=oe[this.layer].vy?oe[this.layer].vy*l:0,B=oe[this.layer].vx?oe[this.layer].vx*l:0;typeof f=="function"&&f.call(Tt.dragged.parentNode[ye],B,C,b,Rr,oe[this.layer].el)!=="continue"||xo(oe[this.layer].el,B,C)}.bind({layer:i}),24))),i++}while(m.bubbleScroll&&r!==a&&(r=on(r,!1)));Fi=s}},30),Io=function(m){var o=m.originalEvent,n=m.putSortable,e=m.dragEl,h=m.activeSortable,t=m.dispatchSortableEvent,l=m.hideGhostForTarget,a=m.unhideGhostForTarget;if(o){var s=n||h;l();var f=o.changedTouches&&o.changedTouches.length?o.changedTouches[0]:o,i=document.elementFromPoint(f.clientX,f.clientY);a(),s&&!s.el.contains(i)&&(t("spill"),this.onSpill({dragEl:e,putSortable:n}))}};function zi(){}zi.prototype={startIndex:null,dragStart:function(m){var o=m.oldDraggableIndex;this.startIndex=o},onSpill:function(m){var o=m.dragEl,n=m.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var e=wn(this.sortable.el,this.startIndex,this.options);e?this.sortable.el.insertBefore(o,e):this.sortable.el.appendChild(o),this.sortable.animateAll(),n&&n.animateAll()},drop:Io};Ie(zi,{pluginName:"revertOnSpill"});function Vi(){}Vi.prototype={onSpill:function(m){var o=m.dragEl,n=m.putSortable,e=n||this.sortable;e.captureAnimationState(),o.parentNode&&o.parentNode.removeChild(o),e.animateAll()},drop:Io};Ie(Vi,{pluginName:"removeOnSpill"});var Ne;function Ma(){function b(){this.defaults={swapClass:"sortable-swap-highlight"}}return b.prototype={dragStart:function(o){var n=o.dragEl;Ne=n},dragOverValid:function(o){var n=o.completed,e=o.target,h=o.onMove,t=o.activeSortable,l=o.changed,a=o.cancel;if(t.options.swap){var s=this.sortable.el,f=this.options;if(e&&e!==s){var i=Ne;h(e)!==!1?(Xt(e,f.swapClass,!0),Ne=e):Ne=null,i&&i!==Ne&&Xt(i,f.swapClass,!1)}l(),n(!0),a()}},drop:function(o){var n=o.activeSortable,e=o.putSortable,h=o.dragEl,t=e||this.sortable,l=this.options;Ne&&Xt(Ne,l.swapClass,!1),Ne&&(l.swap||e&&e.options.swap)&&h!==Ne&&(t.captureAnimationState(),t!==n&&n.captureAnimationState(),_a(h,Ne),t.animateAll(),t!==n&&n.animateAll())},nulling:function(){Ne=null}},Ie(b,{pluginName:"swap",eventProperties:function(){return{swapItem:Ne}}})}function _a(b,m){var o=b.parentNode,n=m.parentNode,e,h;!o||!n||o.isEqualNode(m)||n.isEqualNode(b)||(e=ee(b),h=ee(m),o.isEqualNode(n)&&e<h&&h++,o.insertBefore(m,o.children[e]),n.insertBefore(b,n.children[h]))}var wt=[],we=[],Mn,Ce,_n=!1,Te=!1,xn=!1,Qt,kn,gr;function ka(){function b(m){for(var o in this)o.charAt(0)==="_"&&typeof this[o]=="function"&&(this[o]=this[o].bind(this));m.options.supportPointer?_t(document,"pointerup",this._deselectMultiDrag):(_t(document,"mouseup",this._deselectMultiDrag),_t(document,"touchend",this._deselectMultiDrag)),_t(document,"keydown",this._checkKeyDown),_t(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,setData:function(e,h){var t="";wt.length&&Ce===m?wt.forEach(function(l,a){t+=(a?", ":"")+l.textContent}):t=h.textContent,e.setData("Text",t)}}}return b.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(o){var n=o.dragEl;Qt=n},delayEnded:function(){this.isMultiDrag=~wt.indexOf(Qt)},setupClone:function(o){var n=o.sortable,e=o.cancel;if(this.isMultiDrag){for(var h=0;h<wt.length;h++)we.push(Gi(wt[h])),we[h].sortableIndex=wt[h].sortableIndex,we[h].draggable=!1,we[h].style["will-change"]="",Xt(we[h],this.options.selectedClass,!1),wt[h]===Qt&&Xt(we[h],this.options.chosenClass,!1);n._hideClone(),e()}},clone:function(o){var n=o.sortable,e=o.rootEl,h=o.dispatchSortableEvent,t=o.cancel;this.isMultiDrag&&(this.options.removeCloneOnHide||wt.length&&Ce===n&&(so(!0,e),h("clone"),t()))},showClone:function(o){var n=o.cloneNowShown,e=o.rootEl,h=o.cancel;this.isMultiDrag&&(so(!1,e),we.forEach(function(t){vt(t,"display","")}),n(),gr=!1,h())},hideClone:function(o){var n=this;o.sortable;var e=o.cloneNowHidden,h=o.cancel;this.isMultiDrag&&(we.forEach(function(t){vt(t,"display","none"),n.options.removeCloneOnHide&&t.parentNode&&t.parentNode.removeChild(t)}),e(),gr=!0,h())},dragStartGlobal:function(o){o.sortable,!this.isMultiDrag&&Ce&&Ce.multiDrag._deselectMultiDrag(),wt.forEach(function(n){n.sortableIndex=ee(n)}),wt=wt.sort(function(n,e){return n.sortableIndex-e.sortableIndex}),xn=!0},dragStarted:function(o){var n=this,e=o.sortable;if(this.isMultiDrag){if(this.options.sort&&(e.captureAnimationState(),this.options.animation)){wt.forEach(function(t){t!==Qt&&vt(t,"position","absolute")});var h=$t(Qt,!1,!0,!0);wt.forEach(function(t){t!==Qt&&io(t,h)}),Te=!0,_n=!0}e.animateAll(function(){Te=!1,_n=!1,n.options.animation&&wt.forEach(function(t){Si(t)}),n.options.sort&&yr()})}},dragOver:function(o){var n=o.target,e=o.completed,h=o.cancel;Te&&~wt.indexOf(n)&&(e(!1),h())},revert:function(o){var n=o.fromSortable,e=o.rootEl,h=o.sortable,t=o.dragRect;wt.length>1&&(wt.forEach(function(l){h.addAnimationState({target:l,rect:Te?$t(l):t}),Si(l),l.fromRect=t,n.removeAnimationState(l)}),Te=!1,Ba(!this.options.removeCloneOnHide,e))},dragOverCompleted:function(o){var n=o.sortable,e=o.isOwner,h=o.insertion,t=o.activeSortable,l=o.parentEl,a=o.putSortable,s=this.options;if(h){if(e&&t._hideClone(),_n=!1,s.animation&&wt.length>1&&(Te||!e&&!t.options.sort&&!a)){var f=$t(Qt,!1,!0,!0);wt.forEach(function(r){r!==Qt&&(io(r,f),l.appendChild(r))}),Te=!0}if(!e)if(Te||yr(),wt.length>1){var i=gr;t._showClone(n),t.options.animation&&!gr&&i&&we.forEach(function(r){t.addAnimationState({target:r,rect:kn}),r.fromRect=kn,r.thisAnimationDuration=null})}else t._showClone(n)}},dragOverAnimationCapture:function(o){var n=o.dragRect,e=o.isOwner,h=o.activeSortable;if(wt.forEach(function(l){l.thisAnimationDuration=null}),h.options.animation&&!e&&h.multiDrag.isMultiDrag){kn=Ie({},n);var t=vn(Qt,!0);kn.top-=t.f,kn.left-=t.e}},dragOverAnimationComplete:function(){Te&&(Te=!1,yr())},drop:function(o){var n=o.originalEvent,e=o.rootEl,h=o.parentEl,t=o.sortable,l=o.dispatchSortableEvent,a=o.oldIndex,s=o.putSortable,f=s||this.sortable;if(n){var i=this.options,r=h.children;if(!xn)if(i.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),Xt(Qt,i.selectedClass,!~wt.indexOf(Qt)),~wt.indexOf(Qt))wt.splice(wt.indexOf(Qt),1),Mn=null,Fn({sortable:t,rootEl:e,name:"deselect",targetEl:Qt});else{if(wt.push(Qt),Fn({sortable:t,rootEl:e,name:"select",targetEl:Qt}),n.shiftKey&&Mn&&t.el.contains(Mn)){var u=ee(Mn),y=ee(Qt);if(~u&&~y&&u!==y){var g,p;for(y>u?(p=u,g=y):(p=y,g=u+1);p<g;p++)~wt.indexOf(r[p])||(Xt(r[p],i.selectedClass,!0),wt.push(r[p]),Fn({sortable:t,rootEl:e,name:"select",targetEl:r[p]}))}}else Mn=Qt;Ce=f}if(xn&&this.isMultiDrag){if(Te=!1,(h[ye].options.sort||h!==e)&&wt.length>1){var d=$t(Qt),c=ee(Qt,":not(."+this.options.selectedClass+")");if(!_n&&i.animation&&(Qt.thisAnimationDuration=null),f.captureAnimationState(),!_n&&(i.animation&&(Qt.fromRect=d,wt.forEach(function(v){if(v.thisAnimationDuration=null,v!==Qt){var E=Te?$t(v):d;v.fromRect=E,f.addAnimationState({target:v,rect:E})}})),yr(),wt.forEach(function(v){r[c]?h.insertBefore(v,r[c]):h.appendChild(v),c++}),a===ee(Qt))){var O=!1;wt.forEach(function(v){if(v.sortableIndex!==ee(v)){O=!0;return}}),O&&l("update")}wt.forEach(function(v){Si(v)}),f.animateAll()}Ce=f}(e===h||s&&s.lastPutMode!=="clone")&&we.forEach(function(v){v.parentNode&&v.parentNode.removeChild(v)})}},nullingGlobal:function(){this.isMultiDrag=xn=!1,we.length=0},destroyGlobal:function(){this._deselectMultiDrag(),Lt(document,"pointerup",this._deselectMultiDrag),Lt(document,"mouseup",this._deselectMultiDrag),Lt(document,"touchend",this._deselectMultiDrag),Lt(document,"keydown",this._checkKeyDown),Lt(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(o){if(!(typeof xn!="undefined"&&xn)&&Ce===this.sortable&&!(o&&je(o.target,this.options.draggable,this.sortable.el,!1))&&!(o&&o.button!==0))for(;wt.length;){var n=wt[0];Xt(n,this.options.selectedClass,!1),wt.shift(),Fn({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:n})}},_checkKeyDown:function(o){o.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(o){o.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},Ie(b,{pluginName:"multiDrag",utils:{select:function(o){var n=o.parentNode[ye];!n||!n.options.multiDrag||~wt.indexOf(o)||(Ce&&Ce!==n&&(Ce.multiDrag._deselectMultiDrag(),Ce=n),Xt(o,n.options.selectedClass,!0),wt.push(o))},deselect:function(o){var n=o.parentNode[ye],e=wt.indexOf(o);!n||!n.options.multiDrag||!~e||(Xt(o,n.options.selectedClass,!1),wt.splice(e,1))}},eventProperties:function(){var o=this,n=[],e=[];return wt.forEach(function(h){n.push({multiDragElement:h,index:h.sortableIndex});var t;Te&&h!==Qt?t=-1:Te?t=ee(h,":not(."+o.options.selectedClass+")"):t=ee(h),e.push({multiDragElement:h,index:t})}),{items:la(wt),clones:[].concat(we),oldIndicies:n,newIndicies:e}},optionListeners:{multiDragKey:function(o){return o=o.toLowerCase(),o==="ctrl"?o="Control":o.length>1&&(o=o.charAt(0).toUpperCase()+o.substr(1)),o}}})}function Ba(b,m){wt.forEach(function(o,n){var e=m.children[o.sortableIndex+(b?Number(n):0)];e?m.insertBefore(o,e):m.appendChild(o)})}function so(b,m){we.forEach(function(o,n){var e=m.children[o.sortableIndex+(b?Number(n):0)];e?m.insertBefore(o,e):m.appendChild(o)})}function yr(){wt.forEach(function(b){b!==Qt&&b.parentNode&&b.parentNode.removeChild(b)})}Tt.mount(new ja);Tt.mount(Vi,zi);const Fa=Object.freeze(Object.defineProperty({__proto__:null,MultiDrag:ka,Sortable:Tt,Swap:Ma,default:Tt},Symbol.toStringTag,{value:"Module"})),Ua=yo(Fa);(function(b,m){(function(n,e){b.exports=e(ra,Ua)})(typeof self!="undefined"?self:ue,function(o,n){return function(e){var h={};function t(l){if(h[l])return h[l].exports;var a=h[l]={i:l,l:!1,exports:{}};return e[l].call(a.exports,a,a.exports,t),a.l=!0,a.exports}return t.m=e,t.c=h,t.d=function(l,a,s){t.o(l,a)||Object.defineProperty(l,a,{enumerable:!0,get:s})},t.r=function(l){typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(l,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(l,"__esModule",{value:!0})},t.t=function(l,a){if(a&1&&(l=t(l)),a&8||a&4&&typeof l=="object"&&l&&l.__esModule)return l;var s=Object.create(null);if(t.r(s),Object.defineProperty(s,"default",{enumerable:!0,value:l}),a&2&&typeof l!="string")for(var f in l)t.d(s,f,function(i){return l[i]}.bind(null,f));return s},t.n=function(l){var a=l&&l.__esModule?function(){return l.default}:function(){return l};return t.d(a,"a",a),a},t.o=function(l,a){return Object.prototype.hasOwnProperty.call(l,a)},t.p="",t(t.s="fb15")}({"00ee":function(e,h,t){var l=t("b622"),a=l("toStringTag"),s={};s[a]="z",e.exports=String(s)==="[object z]"},"0366":function(e,h,t){var l=t("1c0b");e.exports=function(a,s,f){if(l(a),s===void 0)return a;switch(f){case 0:return function(){return a.call(s)};case 1:return function(i){return a.call(s,i)};case 2:return function(i,r){return a.call(s,i,r)};case 3:return function(i,r,u){return a.call(s,i,r,u)}}return function(){return a.apply(s,arguments)}}},"057f":function(e,h,t){var l=t("fc6a"),a=t("241c").f,s={}.toString,f=typeof window=="object"&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],i=function(r){try{return a(r)}catch(u){return f.slice()}};e.exports.f=function(u){return f&&s.call(u)=="[object Window]"?i(u):a(l(u))}},"06cf":function(e,h,t){var l=t("83ab"),a=t("d1e7"),s=t("5c6c"),f=t("fc6a"),i=t("c04e"),r=t("5135"),u=t("0cfb"),y=Object.getOwnPropertyDescriptor;h.f=l?y:function(p,d){if(p=f(p),d=i(d,!0),u)try{return y(p,d)}catch(c){}if(r(p,d))return s(!a.f.call(p,d),p[d])}},"0cfb":function(e,h,t){var l=t("83ab"),a=t("d039"),s=t("cc12");e.exports=!l&&!a(function(){return Object.defineProperty(s("div"),"a",{get:function(){return 7}}).a!=7})},"13d5":function(e,h,t){var l=t("23e7"),a=t("d58f").left,s=t("a640"),f=t("ae40"),i=s("reduce"),r=f("reduce",{1:0});l({target:"Array",proto:!0,forced:!i||!r},{reduce:function(y){return a(this,y,arguments.length,arguments.length>1?arguments[1]:void 0)}})},"14c3":function(e,h,t){var l=t("c6b6"),a=t("9263");e.exports=function(s,f){var i=s.exec;if(typeof i=="function"){var r=i.call(s,f);if(typeof r!="object")throw TypeError("RegExp exec method returned something other than an Object or null");return r}if(l(s)!=="RegExp")throw TypeError("RegExp#exec called on incompatible receiver");return a.call(s,f)}},"159b":function(e,h,t){var l=t("da84"),a=t("fdbc"),s=t("17c2"),f=t("9112");for(var i in a){var r=l[i],u=r&&r.prototype;if(u&&u.forEach!==s)try{f(u,"forEach",s)}catch(y){u.forEach=s}}},"17c2":function(e,h,t){var l=t("b727").forEach,a=t("a640"),s=t("ae40"),f=a("forEach"),i=s("forEach");e.exports=!f||!i?function(u){return l(this,u,arguments.length>1?arguments[1]:void 0)}:[].forEach},"1be4":function(e,h,t){var l=t("d066");e.exports=l("document","documentElement")},"1c0b":function(e,h){e.exports=function(t){if(typeof t!="function")throw TypeError(String(t)+" is not a function");return t}},"1c7e":function(e,h,t){var l=t("b622"),a=l("iterator"),s=!1;try{var f=0,i={next:function(){return{done:!!f++}},return:function(){s=!0}};i[a]=function(){return this},Array.from(i,function(){throw 2})}catch(r){}e.exports=function(r,u){if(!u&&!s)return!1;var y=!1;try{var g={};g[a]=function(){return{next:function(){return{done:y=!0}}}},r(g)}catch(p){}return y}},"1d80":function(e,h){e.exports=function(t){if(t==null)throw TypeError("Can't call method on "+t);return t}},"1dde":function(e,h,t){var l=t("d039"),a=t("b622"),s=t("2d00"),f=a("species");e.exports=function(i){return s>=51||!l(function(){var r=[],u=r.constructor={};return u[f]=function(){return{foo:1}},r[i](Boolean).foo!==1})}},"23cb":function(e,h,t){var l=t("a691"),a=Math.max,s=Math.min;e.exports=function(f,i){var r=l(f);return r<0?a(r+i,0):s(r,i)}},"23e7":function(e,h,t){var l=t("da84"),a=t("06cf").f,s=t("9112"),f=t("6eeb"),i=t("ce4e"),r=t("e893"),u=t("94ca");e.exports=function(y,g){var p=y.target,d=y.global,c=y.stat,O,v,E,T,P,I;if(d?v=l:c?v=l[p]||i(p,{}):v=(l[p]||{}).prototype,v)for(E in g){if(P=g[E],y.noTargetGet?(I=a(v,E),T=I&&I.value):T=v[E],O=u(d?E:p+(c?".":"#")+E,y.forced),!O&&T!==void 0){if(typeof P==typeof T)continue;r(P,T)}(y.sham||T&&T.sham)&&s(P,"sham",!0),f(v,E,P,y)}}},"241c":function(e,h,t){var l=t("ca84"),a=t("7839"),s=a.concat("length","prototype");h.f=Object.getOwnPropertyNames||function(i){return l(i,s)}},"25f0":function(e,h,t){var l=t("6eeb"),a=t("825a"),s=t("d039"),f=t("ad6d"),i="toString",r=RegExp.prototype,u=r[i],y=s(function(){return u.call({source:"a",flags:"b"})!="/a/b"}),g=u.name!=i;(y||g)&&l(RegExp.prototype,i,function(){var d=a(this),c=String(d.source),O=d.flags,v=String(O===void 0&&d instanceof RegExp&&!("flags"in r)?f.call(d):O);return"/"+c+"/"+v},{unsafe:!0})},"2ca0":function(e,h,t){var l=t("23e7"),a=t("06cf").f,s=t("50c4"),f=t("5a34"),i=t("1d80"),r=t("ab13"),u=t("c430"),y="".startsWith,g=Math.min,p=r("startsWith"),d=!u&&!p&&!!function(){var c=a(String.prototype,"startsWith");return c&&!c.writable}();l({target:"String",proto:!0,forced:!d&&!p},{startsWith:function(O){var v=String(i(this));f(O);var E=s(g(arguments.length>1?arguments[1]:void 0,v.length)),T=String(O);return y?y.call(v,T,E):v.slice(E,E+T.length)===T}})},"2d00":function(e,h,t){var l=t("da84"),a=t("342f"),s=l.process,f=s&&s.versions,i=f&&f.v8,r,u;i?(r=i.split("."),u=r[0]+r[1]):a&&(r=a.match(/Edge\/(\d+)/),(!r||r[1]>=74)&&(r=a.match(/Chrome\/(\d+)/),r&&(u=r[1]))),e.exports=u&&+u},"342f":function(e,h,t){var l=t("d066");e.exports=l("navigator","userAgent")||""},"35a1":function(e,h,t){var l=t("f5df"),a=t("3f8c"),s=t("b622"),f=s("iterator");e.exports=function(i){if(i!=null)return i[f]||i["@@iterator"]||a[l(i)]}},"37e8":function(e,h,t){var l=t("83ab"),a=t("9bf2"),s=t("825a"),f=t("df75");e.exports=l?Object.defineProperties:function(r,u){s(r);for(var y=f(u),g=y.length,p=0,d;g>p;)a.f(r,d=y[p++],u[d]);return r}},"3bbe":function(e,h,t){var l=t("861d");e.exports=function(a){if(!l(a)&&a!==null)throw TypeError("Can't set "+String(a)+" as a prototype");return a}},"3ca3":function(e,h,t){var l=t("6547").charAt,a=t("69f3"),s=t("7dd0"),f="String Iterator",i=a.set,r=a.getterFor(f);s(String,"String",function(u){i(this,{type:f,string:String(u),index:0})},function(){var y=r(this),g=y.string,p=y.index,d;return p>=g.length?{value:void 0,done:!0}:(d=l(g,p),y.index+=d.length,{value:d,done:!1})})},"3f8c":function(e,h){e.exports={}},4160:function(e,h,t){var l=t("23e7"),a=t("17c2");l({target:"Array",proto:!0,forced:[].forEach!=a},{forEach:a})},"428f":function(e,h,t){var l=t("da84");e.exports=l},"44ad":function(e,h,t){var l=t("d039"),a=t("c6b6"),s="".split;e.exports=l(function(){return!Object("z").propertyIsEnumerable(0)})?function(f){return a(f)=="String"?s.call(f,""):Object(f)}:Object},"44d2":function(e,h,t){var l=t("b622"),a=t("7c73"),s=t("9bf2"),f=l("unscopables"),i=Array.prototype;i[f]==null&&s.f(i,f,{configurable:!0,value:a(null)}),e.exports=function(r){i[f][r]=!0}},"44e7":function(e,h,t){var l=t("861d"),a=t("c6b6"),s=t("b622"),f=s("match");e.exports=function(i){var r;return l(i)&&((r=i[f])!==void 0?!!r:a(i)=="RegExp")}},4930:function(e,h,t){var l=t("d039");e.exports=!!Object.getOwnPropertySymbols&&!l(function(){return!String(Symbol())})},"4d64":function(e,h,t){var l=t("fc6a"),a=t("50c4"),s=t("23cb"),f=function(i){return function(r,u,y){var g=l(r),p=a(g.length),d=s(y,p),c;if(i&&u!=u){for(;p>d;)if(c=g[d++],c!=c)return!0}else for(;p>d;d++)if((i||d in g)&&g[d]===u)return i||d||0;return!i&&-1}};e.exports={includes:f(!0),indexOf:f(!1)}},"4de4":function(e,h,t){var l=t("23e7"),a=t("b727").filter,s=t("1dde"),f=t("ae40"),i=s("filter"),r=f("filter");l({target:"Array",proto:!0,forced:!i||!r},{filter:function(y){return a(this,y,arguments.length>1?arguments[1]:void 0)}})},"4df4":function(e,h,t){var l=t("0366"),a=t("7b0b"),s=t("9bdd"),f=t("e95a"),i=t("50c4"),r=t("8418"),u=t("35a1");e.exports=function(g){var p=a(g),d=typeof this=="function"?this:Array,c=arguments.length,O=c>1?arguments[1]:void 0,v=O!==void 0,E=u(p),T=0,P,I,D,w,S,A;if(v&&(O=l(O,c>2?arguments[2]:void 0,2)),E!=null&&!(d==Array&&f(E)))for(w=E.call(p),S=w.next,I=new d;!(D=S.call(w)).done;T++)A=v?s(w,O,[D.value,T],!0):D.value,r(I,T,A);else for(P=i(p.length),I=new d(P);P>T;T++)A=v?O(p[T],T):p[T],r(I,T,A);return I.length=T,I}},"4fad":function(e,h,t){var l=t("23e7"),a=t("6f53").entries;l({target:"Object",stat:!0},{entries:function(f){return a(f)}})},"50c4":function(e,h,t){var l=t("a691"),a=Math.min;e.exports=function(s){return s>0?a(l(s),9007199254740991):0}},5135:function(e,h){var t={}.hasOwnProperty;e.exports=function(l,a){return t.call(l,a)}},5319:function(e,h,t){var l=t("d784"),a=t("825a"),s=t("7b0b"),f=t("50c4"),i=t("a691"),r=t("1d80"),u=t("8aa5"),y=t("14c3"),g=Math.max,p=Math.min,d=Math.floor,c=/\$([$&'`]|\d\d?|<[^>]*>)/g,O=/\$([$&'`]|\d\d?)/g,v=function(E){return E===void 0?E:String(E)};l("replace",2,function(E,T,P,I){var D=I.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,w=I.REPLACE_KEEPS_$0,S=D?"$":"$0";return[function(R,C){var B=r(this),U=R==null?void 0:R[E];return U!==void 0?U.call(R,B,C):T.call(String(B),R,C)},function(N,R){if(!D&&w||typeof R=="string"&&R.indexOf(S)===-1){var C=P(T,N,this,R);if(C.done)return C.value}var B=a(N),U=String(this),z=typeof R=="function";z||(R=String(R));var K=B.global;if(K){var k=B.unicode;B.lastIndex=0}for(var L=[];;){var _=y(B,U);if(_===null||(L.push(_),!K))break;var F=String(_[0]);F===""&&(B.lastIndex=u(U,f(B.lastIndex),k))}for(var Q="",q=0,M=0;M<L.length;M++){_=L[M];for(var H=String(_[0]),W=g(p(i(_.index),U.length),0),$=[],et=1;et<_.length;et++)$.push(v(_[et]));var ut=_.groups;if(z){var pt=[H].concat($,W,U);ut!==void 0&&pt.push(ut);var dt=String(R.apply(void 0,pt))}else dt=A(H,U,W,$,ut,R);W>=q&&(Q+=U.slice(q,W)+dt,q=W+H.length)}return Q+U.slice(q)}];function A(N,R,C,B,U,z){var K=C+N.length,k=B.length,L=O;return U!==void 0&&(U=s(U),L=c),T.call(z,L,function(_,F){var Q;switch(F.charAt(0)){case"$":return"$";case"&":return N;case"`":return R.slice(0,C);case"'":return R.slice(K);case"<":Q=U[F.slice(1,-1)];break;default:var q=+F;if(q===0)return _;if(q>k){var M=d(q/10);return M===0?_:M<=k?B[M-1]===void 0?F.charAt(1):B[M-1]+F.charAt(1):_}Q=B[q-1]}return Q===void 0?"":Q})}})},5692:function(e,h,t){var l=t("c430"),a=t("c6cd");(e.exports=function(s,f){return a[s]||(a[s]=f!==void 0?f:{})})("versions",[]).push({version:"3.6.5",mode:l?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},"56ef":function(e,h,t){var l=t("d066"),a=t("241c"),s=t("7418"),f=t("825a");e.exports=l("Reflect","ownKeys")||function(r){var u=a.f(f(r)),y=s.f;return y?u.concat(y(r)):u}},"5a34":function(e,h,t){var l=t("44e7");e.exports=function(a){if(l(a))throw TypeError("The method doesn't accept regular expressions");return a}},"5c6c":function(e,h){e.exports=function(t,l){return{enumerable:!(t&1),configurable:!(t&2),writable:!(t&4),value:l}}},"5db7":function(e,h,t){var l=t("23e7"),a=t("a2bf"),s=t("7b0b"),f=t("50c4"),i=t("1c0b"),r=t("65f0");l({target:"Array",proto:!0},{flatMap:function(y){var g=s(this),p=f(g.length),d;return i(y),d=r(g,0),d.length=a(d,g,g,p,0,1,y,arguments.length>1?arguments[1]:void 0),d}})},6547:function(e,h,t){var l=t("a691"),a=t("1d80"),s=function(f){return function(i,r){var u=String(a(i)),y=l(r),g=u.length,p,d;return y<0||y>=g?f?"":void 0:(p=u.charCodeAt(y),p<55296||p>56319||y+1===g||(d=u.charCodeAt(y+1))<56320||d>57343?f?u.charAt(y):p:f?u.slice(y,y+2):(p-55296<<10)+(d-56320)+65536)}};e.exports={codeAt:s(!1),charAt:s(!0)}},"65f0":function(e,h,t){var l=t("861d"),a=t("e8b5"),s=t("b622"),f=s("species");e.exports=function(i,r){var u;return a(i)&&(u=i.constructor,typeof u=="function"&&(u===Array||a(u.prototype))?u=void 0:l(u)&&(u=u[f],u===null&&(u=void 0))),new(u===void 0?Array:u)(r===0?0:r)}},"69f3":function(e,h,t){var l=t("7f9a"),a=t("da84"),s=t("861d"),f=t("9112"),i=t("5135"),r=t("f772"),u=t("d012"),y=a.WeakMap,g,p,d,c=function(D){return d(D)?p(D):g(D,{})},O=function(D){return function(w){var S;if(!s(w)||(S=p(w)).type!==D)throw TypeError("Incompatible receiver, "+D+" required");return S}};if(l){var v=new y,E=v.get,T=v.has,P=v.set;g=function(D,w){return P.call(v,D,w),w},p=function(D){return E.call(v,D)||{}},d=function(D){return T.call(v,D)}}else{var I=r("state");u[I]=!0,g=function(D,w){return f(D,I,w),w},p=function(D){return i(D,I)?D[I]:{}},d=function(D){return i(D,I)}}e.exports={set:g,get:p,has:d,enforce:c,getterFor:O}},"6eeb":function(e,h,t){var l=t("da84"),a=t("9112"),s=t("5135"),f=t("ce4e"),i=t("8925"),r=t("69f3"),u=r.get,y=r.enforce,g=String(String).split("String");(e.exports=function(p,d,c,O){var v=O?!!O.unsafe:!1,E=O?!!O.enumerable:!1,T=O?!!O.noTargetGet:!1;if(typeof c=="function"&&(typeof d=="string"&&!s(c,"name")&&a(c,"name",d),y(c).source=g.join(typeof d=="string"?d:"")),p===l){E?p[d]=c:f(d,c);return}else v?!T&&p[d]&&(E=!0):delete p[d];E?p[d]=c:a(p,d,c)})(Function.prototype,"toString",function(){return typeof this=="function"&&u(this).source||i(this)})},"6f53":function(e,h,t){var l=t("83ab"),a=t("df75"),s=t("fc6a"),f=t("d1e7").f,i=function(r){return function(u){for(var y=s(u),g=a(y),p=g.length,d=0,c=[],O;p>d;)O=g[d++],(!l||f.call(y,O))&&c.push(r?[O,y[O]]:y[O]);return c}};e.exports={entries:i(!0),values:i(!1)}},"73d9":function(e,h,t){var l=t("44d2");l("flatMap")},7418:function(e,h){h.f=Object.getOwnPropertySymbols},"746f":function(e,h,t){var l=t("428f"),a=t("5135"),s=t("e538"),f=t("9bf2").f;e.exports=function(i){var r=l.Symbol||(l.Symbol={});a(r,i)||f(r,i,{value:s.f(i)})}},7839:function(e,h){e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},"7b0b":function(e,h,t){var l=t("1d80");e.exports=function(a){return Object(l(a))}},"7c73":function(e,h,t){var l=t("825a"),a=t("37e8"),s=t("7839"),f=t("d012"),i=t("1be4"),r=t("cc12"),u=t("f772"),y=">",g="<",p="prototype",d="script",c=u("IE_PROTO"),O=function(){},v=function(D){return g+d+y+D+g+"/"+d+y},E=function(D){D.write(v("")),D.close();var w=D.parentWindow.Object;return D=null,w},T=function(){var D=r("iframe"),w="java"+d+":",S;return D.style.display="none",i.appendChild(D),D.src=String(w),S=D.contentWindow.document,S.open(),S.write(v("document.F=Object")),S.close(),S.F},P,I=function(){try{P=document.domain&&new ActiveXObject("htmlfile")}catch(w){}I=P?E(P):T();for(var D=s.length;D--;)delete I[p][s[D]];return I()};f[c]=!0,e.exports=Object.create||function(w,S){var A;return w!==null?(O[p]=l(w),A=new O,O[p]=null,A[c]=w):A=I(),S===void 0?A:a(A,S)}},"7dd0":function(e,h,t){var l=t("23e7"),a=t("9ed3"),s=t("e163"),f=t("d2bb"),i=t("d44e"),r=t("9112"),u=t("6eeb"),y=t("b622"),g=t("c430"),p=t("3f8c"),d=t("ae93"),c=d.IteratorPrototype,O=d.BUGGY_SAFARI_ITERATORS,v=y("iterator"),E="keys",T="values",P="entries",I=function(){return this};e.exports=function(D,w,S,A,N,R,C){a(S,w,A);var B=function(M){if(M===N&&L)return L;if(!O&&M in K)return K[M];switch(M){case E:return function(){return new S(this,M)};case T:return function(){return new S(this,M)};case P:return function(){return new S(this,M)}}return function(){return new S(this)}},U=w+" Iterator",z=!1,K=D.prototype,k=K[v]||K["@@iterator"]||N&&K[N],L=!O&&k||B(N),_=w=="Array"&&K.entries||k,F,Q,q;if(_&&(F=s(_.call(new D)),c!==Object.prototype&&F.next&&(!g&&s(F)!==c&&(f?f(F,c):typeof F[v]!="function"&&r(F,v,I)),i(F,U,!0,!0),g&&(p[U]=I))),N==T&&k&&k.name!==T&&(z=!0,L=function(){return k.call(this)}),(!g||C)&&K[v]!==L&&r(K,v,L),p[w]=L,N)if(Q={values:B(T),keys:R?L:B(E),entries:B(P)},C)for(q in Q)(O||z||!(q in K))&&u(K,q,Q[q]);else l({target:w,proto:!0,forced:O||z},Q);return Q}},"7f9a":function(e,h,t){var l=t("da84"),a=t("8925"),s=l.WeakMap;e.exports=typeof s=="function"&&/native code/.test(a(s))},"825a":function(e,h,t){var l=t("861d");e.exports=function(a){if(!l(a))throw TypeError(String(a)+" is not an object");return a}},"83ab":function(e,h,t){var l=t("d039");e.exports=!l(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7})},8418:function(e,h,t){var l=t("c04e"),a=t("9bf2"),s=t("5c6c");e.exports=function(f,i,r){var u=l(i);u in f?a.f(f,u,s(0,r)):f[u]=r}},"861d":function(e,h){e.exports=function(t){return typeof t=="object"?t!==null:typeof t=="function"}},8875:function(e,h,t){var l,a,s;(function(f,i){a=[],l=i,s=typeof l=="function"?l.apply(h,a):l,s!==void 0&&(e.exports=s)})(typeof self!="undefined"?self:this,function(){function f(){var i=Object.getOwnPropertyDescriptor(document,"currentScript");if(!i&&"currentScript"in document&&document.currentScript||i&&i.get!==f&&document.currentScript)return document.currentScript;try{throw new Error}catch(P){var r=/.*at [^(]*\((.*):(.+):(.+)\)$/ig,u=/@([^@]*):(\d+):(\d+)\s*$/ig,y=r.exec(P.stack)||u.exec(P.stack),g=y&&y[1]||!1,p=y&&y[2]||!1,d=document.location.href.replace(document.location.hash,""),c,O,v,E=document.getElementsByTagName("script");g===d&&(c=document.documentElement.outerHTML,O=new RegExp("(?:[^\\n]+?\\n){0,"+(p-2)+"}[^<]*<script>([\\d\\D]*?)<\\/script>[\\d\\D]*","i"),v=c.replace(O,"$1").trim());for(var T=0;T<E.length;T++)if(E[T].readyState==="interactive"||E[T].src===g||g===d&&E[T].innerHTML&&E[T].innerHTML.trim()===v)return E[T];return null}}return f})},8925:function(e,h,t){var l=t("c6cd"),a=Function.toString;typeof l.inspectSource!="function"&&(l.inspectSource=function(s){return a.call(s)}),e.exports=l.inspectSource},"8aa5":function(e,h,t){var l=t("6547").charAt;e.exports=function(a,s,f){return s+(f?l(a,s).length:1)}},"8bbf":function(e,h){e.exports=o},"90e3":function(e,h){var t=0,l=Math.random();e.exports=function(a){return"Symbol("+String(a===void 0?"":a)+")_"+(++t+l).toString(36)}},9112:function(e,h,t){var l=t("83ab"),a=t("9bf2"),s=t("5c6c");e.exports=l?function(f,i,r){return a.f(f,i,s(1,r))}:function(f,i,r){return f[i]=r,f}},9263:function(e,h,t){var l=t("ad6d"),a=t("9f7f"),s=RegExp.prototype.exec,f=String.prototype.replace,i=s,r=function(){var p=/a/,d=/b*/g;return s.call(p,"a"),s.call(d,"a"),p.lastIndex!==0||d.lastIndex!==0}(),u=a.UNSUPPORTED_Y||a.BROKEN_CARET,y=/()??/.exec("")[1]!==void 0,g=r||y||u;g&&(i=function(d){var c=this,O,v,E,T,P=u&&c.sticky,I=l.call(c),D=c.source,w=0,S=d;return P&&(I=I.replace("y",""),I.indexOf("g")===-1&&(I+="g"),S=String(d).slice(c.lastIndex),c.lastIndex>0&&(!c.multiline||c.multiline&&d[c.lastIndex-1]!==`
`)&&(D="(?: "+D+")",S=" "+S,w++),v=new RegExp("^(?:"+D+")",I)),y&&(v=new RegExp("^"+D+"$(?!\\s)",I)),r&&(O=c.lastIndex),E=s.call(P?v:c,S),P?E?(E.input=E.input.slice(w),E[0]=E[0].slice(w),E.index=c.lastIndex,c.lastIndex+=E[0].length):c.lastIndex=0:r&&E&&(c.lastIndex=c.global?E.index+E[0].length:O),y&&E&&E.length>1&&f.call(E[0],v,function(){for(T=1;T<arguments.length-2;T++)arguments[T]===void 0&&(E[T]=void 0)}),E}),e.exports=i},"94ca":function(e,h,t){var l=t("d039"),a=/#|\.prototype\./,s=function(y,g){var p=i[f(y)];return p==u?!0:p==r?!1:typeof g=="function"?l(g):!!g},f=s.normalize=function(y){return String(y).replace(a,".").toLowerCase()},i=s.data={},r=s.NATIVE="N",u=s.POLYFILL="P";e.exports=s},"99af":function(e,h,t){var l=t("23e7"),a=t("d039"),s=t("e8b5"),f=t("861d"),i=t("7b0b"),r=t("50c4"),u=t("8418"),y=t("65f0"),g=t("1dde"),p=t("b622"),d=t("2d00"),c=p("isConcatSpreadable"),O=9007199254740991,v="Maximum allowed index exceeded",E=d>=51||!a(function(){var D=[];return D[c]=!1,D.concat()[0]!==D}),T=g("concat"),P=function(D){if(!f(D))return!1;var w=D[c];return w!==void 0?!!w:s(D)},I=!E||!T;l({target:"Array",proto:!0,forced:I},{concat:function(w){var S=i(this),A=y(S,0),N=0,R,C,B,U,z;for(R=-1,B=arguments.length;R<B;R++)if(z=R===-1?S:arguments[R],P(z)){if(U=r(z.length),N+U>O)throw TypeError(v);for(C=0;C<U;C++,N++)C in z&&u(A,N,z[C])}else{if(N>=O)throw TypeError(v);u(A,N++,z)}return A.length=N,A}})},"9bdd":function(e,h,t){var l=t("825a");e.exports=function(a,s,f,i){try{return i?s(l(f)[0],f[1]):s(f)}catch(u){var r=a.return;throw r!==void 0&&l(r.call(a)),u}}},"9bf2":function(e,h,t){var l=t("83ab"),a=t("0cfb"),s=t("825a"),f=t("c04e"),i=Object.defineProperty;h.f=l?i:function(u,y,g){if(s(u),y=f(y,!0),s(g),a)try{return i(u,y,g)}catch(p){}if("get"in g||"set"in g)throw TypeError("Accessors not supported");return"value"in g&&(u[y]=g.value),u}},"9ed3":function(e,h,t){var l=t("ae93").IteratorPrototype,a=t("7c73"),s=t("5c6c"),f=t("d44e"),i=t("3f8c"),r=function(){return this};e.exports=function(u,y,g){var p=y+" Iterator";return u.prototype=a(l,{next:s(1,g)}),f(u,p,!1,!0),i[p]=r,u}},"9f7f":function(e,h,t){var l=t("d039");function a(s,f){return RegExp(s,f)}h.UNSUPPORTED_Y=l(function(){var s=a("a","y");return s.lastIndex=2,s.exec("abcd")!=null}),h.BROKEN_CARET=l(function(){var s=a("^r","gy");return s.lastIndex=2,s.exec("str")!=null})},a2bf:function(e,h,t){var l=t("e8b5"),a=t("50c4"),s=t("0366"),f=function(i,r,u,y,g,p,d,c){for(var O=g,v=0,E=d?s(d,c,3):!1,T;v<y;){if(v in u){if(T=E?E(u[v],v,r):u[v],p>0&&l(T))O=f(i,r,T,a(T.length),O,p-1)-1;else{if(O>=9007199254740991)throw TypeError("Exceed the acceptable array length");i[O]=T}O++}v++}return O};e.exports=f},a352:function(e,h){e.exports=n},a434:function(e,h,t){var l=t("23e7"),a=t("23cb"),s=t("a691"),f=t("50c4"),i=t("7b0b"),r=t("65f0"),u=t("8418"),y=t("1dde"),g=t("ae40"),p=y("splice"),d=g("splice",{ACCESSORS:!0,0:0,1:2}),c=Math.max,O=Math.min,v=9007199254740991,E="Maximum allowed length exceeded";l({target:"Array",proto:!0,forced:!p||!d},{splice:function(P,I){var D=i(this),w=f(D.length),S=a(P,w),A=arguments.length,N,R,C,B,U,z;if(A===0?N=R=0:A===1?(N=0,R=w-S):(N=A-2,R=O(c(s(I),0),w-S)),w+N-R>v)throw TypeError(E);for(C=r(D,R),B=0;B<R;B++)U=S+B,U in D&&u(C,B,D[U]);if(C.length=R,N<R){for(B=S;B<w-R;B++)U=B+R,z=B+N,U in D?D[z]=D[U]:delete D[z];for(B=w;B>w-R+N;B--)delete D[B-1]}else if(N>R)for(B=w-R;B>S;B--)U=B+R-1,z=B+N-1,U in D?D[z]=D[U]:delete D[z];for(B=0;B<N;B++)D[B+S]=arguments[B+2];return D.length=w-R+N,C}})},a4d3:function(e,h,t){var l=t("23e7"),a=t("da84"),s=t("d066"),f=t("c430"),i=t("83ab"),r=t("4930"),u=t("fdbf"),y=t("d039"),g=t("5135"),p=t("e8b5"),d=t("861d"),c=t("825a"),O=t("7b0b"),v=t("fc6a"),E=t("c04e"),T=t("5c6c"),P=t("7c73"),I=t("df75"),D=t("241c"),w=t("057f"),S=t("7418"),A=t("06cf"),N=t("9bf2"),R=t("d1e7"),C=t("9112"),B=t("6eeb"),U=t("5692"),z=t("f772"),K=t("d012"),k=t("90e3"),L=t("b622"),_=t("e538"),F=t("746f"),Q=t("d44e"),q=t("69f3"),M=t("b727").forEach,H=z("hidden"),W="Symbol",$="prototype",et=L("toPrimitive"),ut=q.set,pt=q.getterFor(W),dt=Object[$],gt=a.Symbol,Nt=s("JSON","stringify"),qt=A.f,kt=N.f,Z=w.f,tt=R.f,it=U("symbols"),ot=U("op-symbols"),nt=U("string-to-symbol-registry"),xt=U("symbol-to-string-registry"),bt=U("wks"),St=a.QObject,Zt=!St||!St[$]||!St[$].findChild,Yt=i&&y(function(){return P(kt({},"a",{get:function(){return kt(this,"a",{value:7}).a}})).a!=7})?function(Ot,st,yt){var Dt=qt(dt,st);Dt&&delete dt[st],kt(Ot,st,yt),Dt&&Ot!==dt&&kt(dt,st,Dt)}:kt,jt=function(Ot,st){var yt=it[Ot]=P(gt[$]);return ut(yt,{type:W,tag:Ot,description:st}),i||(yt.description=st),yt},Y=u?function(Ot){return typeof Ot=="symbol"}:function(Ot){return Object(Ot)instanceof gt},V=function(st,yt,Dt){st===dt&&V(ot,yt,Dt),c(st);var Rt=E(yt,!0);return c(Dt),g(it,Rt)?(Dt.enumerable?(g(st,H)&&st[H][Rt]&&(st[H][Rt]=!1),Dt=P(Dt,{enumerable:T(0,!1)})):(g(st,H)||kt(st,H,T(1,{})),st[H][Rt]=!0),Yt(st,Rt,Dt)):kt(st,Rt,Dt)},X=function(st,yt){c(st);var Dt=v(yt),Rt=I(Dt).concat(Mt(Dt));return M(Rt,function(re){(!i||ct.call(Dt,re))&&V(st,re,Dt[re])}),st},rt=function(st,yt){return yt===void 0?P(st):X(P(st),yt)},ct=function(st){var yt=E(st,!0),Dt=tt.call(this,yt);return this===dt&&g(it,yt)&&!g(ot,yt)?!1:Dt||!g(this,yt)||!g(it,yt)||g(this,H)&&this[H][yt]?Dt:!0},Et=function(st,yt){var Dt=v(st),Rt=E(yt,!0);if(!(Dt===dt&&g(it,Rt)&&!g(ot,Rt))){var re=qt(Dt,Rt);return re&&g(it,Rt)&&!(g(Dt,H)&&Dt[H][Rt])&&(re.enumerable=!0),re}},At=function(st){var yt=Z(v(st)),Dt=[];return M(yt,function(Rt){!g(it,Rt)&&!g(K,Rt)&&Dt.push(Rt)}),Dt},Mt=function(st){var yt=st===dt,Dt=Z(yt?ot:v(st)),Rt=[];return M(Dt,function(re){g(it,re)&&(!yt||g(dt,re))&&Rt.push(it[re])}),Rt};if(r||(gt=function(){if(this instanceof gt)throw TypeError("Symbol is not a constructor");var st=!arguments.length||arguments[0]===void 0?void 0:String(arguments[0]),yt=k(st),Dt=function(Rt){this===dt&&Dt.call(ot,Rt),g(this,H)&&g(this[H],yt)&&(this[H][yt]=!1),Yt(this,yt,T(1,Rt))};return i&&Zt&&Yt(dt,yt,{configurable:!0,set:Dt}),jt(yt,st)},B(gt[$],"toString",function(){return pt(this).tag}),B(gt,"withoutSetter",function(Ot){return jt(k(Ot),Ot)}),R.f=ct,N.f=V,A.f=Et,D.f=w.f=At,S.f=Mt,_.f=function(Ot){return jt(L(Ot),Ot)},i&&(kt(gt[$],"description",{configurable:!0,get:function(){return pt(this).description}}),f||B(dt,"propertyIsEnumerable",ct,{unsafe:!0}))),l({global:!0,wrap:!0,forced:!r,sham:!r},{Symbol:gt}),M(I(bt),function(Ot){F(Ot)}),l({target:W,stat:!0,forced:!r},{for:function(Ot){var st=String(Ot);if(g(nt,st))return nt[st];var yt=gt(st);return nt[st]=yt,xt[yt]=st,yt},keyFor:function(st){if(!Y(st))throw TypeError(st+" is not a symbol");if(g(xt,st))return xt[st]},useSetter:function(){Zt=!0},useSimple:function(){Zt=!1}}),l({target:"Object",stat:!0,forced:!r,sham:!i},{create:rt,defineProperty:V,defineProperties:X,getOwnPropertyDescriptor:Et}),l({target:"Object",stat:!0,forced:!r},{getOwnPropertyNames:At,getOwnPropertySymbols:Mt}),l({target:"Object",stat:!0,forced:y(function(){S.f(1)})},{getOwnPropertySymbols:function(st){return S.f(O(st))}}),Nt){var Gt=!r||y(function(){var Ot=gt();return Nt([Ot])!="[null]"||Nt({a:Ot})!="{}"||Nt(Object(Ot))!="{}"});l({target:"JSON",stat:!0,forced:Gt},{stringify:function(st,yt,Dt){for(var Rt=[st],re=1,De;arguments.length>re;)Rt.push(arguments[re++]);if(De=yt,!(!d(yt)&&st===void 0||Y(st)))return p(yt)||(yt=function(an,_e){if(typeof De=="function"&&(_e=De.call(this,an,_e)),!Y(_e))return _e}),Rt[1]=yt,Nt.apply(null,Rt)}})}gt[$][et]||C(gt[$],et,gt[$].valueOf),Q(gt,W),K[H]=!0},a630:function(e,h,t){var l=t("23e7"),a=t("4df4"),s=t("1c7e"),f=!s(function(i){Array.from(i)});l({target:"Array",stat:!0,forced:f},{from:a})},a640:function(e,h,t){var l=t("d039");e.exports=function(a,s){var f=[][a];return!!f&&l(function(){f.call(null,s||function(){throw 1},1)})}},a691:function(e,h){var t=Math.ceil,l=Math.floor;e.exports=function(a){return isNaN(a=+a)?0:(a>0?l:t)(a)}},ab13:function(e,h,t){var l=t("b622"),a=l("match");e.exports=function(s){var f=/./;try{"/./"[s](f)}catch(i){try{return f[a]=!1,"/./"[s](f)}catch(r){}}return!1}},ac1f:function(e,h,t){var l=t("23e7"),a=t("9263");l({target:"RegExp",proto:!0,forced:/./.exec!==a},{exec:a})},ad6d:function(e,h,t){var l=t("825a");e.exports=function(){var a=l(this),s="";return a.global&&(s+="g"),a.ignoreCase&&(s+="i"),a.multiline&&(s+="m"),a.dotAll&&(s+="s"),a.unicode&&(s+="u"),a.sticky&&(s+="y"),s}},ae40:function(e,h,t){var l=t("83ab"),a=t("d039"),s=t("5135"),f=Object.defineProperty,i={},r=function(u){throw u};e.exports=function(u,y){if(s(i,u))return i[u];y||(y={});var g=[][u],p=s(y,"ACCESSORS")?y.ACCESSORS:!1,d=s(y,0)?y[0]:r,c=s(y,1)?y[1]:void 0;return i[u]=!!g&&!a(function(){if(p&&!l)return!0;var O={length:-1};p?f(O,1,{enumerable:!0,get:r}):O[1]=1,g.call(O,d,c)})}},ae93:function(e,h,t){var l=t("e163"),a=t("9112"),s=t("5135"),f=t("b622"),i=t("c430"),r=f("iterator"),u=!1,y=function(){return this},g,p,d;[].keys&&(d=[].keys(),"next"in d?(p=l(l(d)),p!==Object.prototype&&(g=p)):u=!0),g==null&&(g={}),!i&&!s(g,r)&&a(g,r,y),e.exports={IteratorPrototype:g,BUGGY_SAFARI_ITERATORS:u}},b041:function(e,h,t){var l=t("00ee"),a=t("f5df");e.exports=l?{}.toString:function(){return"[object "+a(this)+"]"}},b0c0:function(e,h,t){var l=t("83ab"),a=t("9bf2").f,s=Function.prototype,f=s.toString,i=/^\s*function ([^ (]*)/,r="name";l&&!(r in s)&&a(s,r,{configurable:!0,get:function(){try{return f.call(this).match(i)[1]}catch(u){return""}}})},b622:function(e,h,t){var l=t("da84"),a=t("5692"),s=t("5135"),f=t("90e3"),i=t("4930"),r=t("fdbf"),u=a("wks"),y=l.Symbol,g=r?y:y&&y.withoutSetter||f;e.exports=function(p){return s(u,p)||(i&&s(y,p)?u[p]=y[p]:u[p]=g("Symbol."+p)),u[p]}},b64b:function(e,h,t){var l=t("23e7"),a=t("7b0b"),s=t("df75"),f=t("d039"),i=f(function(){s(1)});l({target:"Object",stat:!0,forced:i},{keys:function(u){return s(a(u))}})},b727:function(e,h,t){var l=t("0366"),a=t("44ad"),s=t("7b0b"),f=t("50c4"),i=t("65f0"),r=[].push,u=function(y){var g=y==1,p=y==2,d=y==3,c=y==4,O=y==6,v=y==5||O;return function(E,T,P,I){for(var D=s(E),w=a(D),S=l(T,P,3),A=f(w.length),N=0,R=I||i,C=g?R(E,A):p?R(E,0):void 0,B,U;A>N;N++)if((v||N in w)&&(B=w[N],U=S(B,N,D),y)){if(g)C[N]=U;else if(U)switch(y){case 3:return!0;case 5:return B;case 6:return N;case 2:r.call(C,B)}else if(c)return!1}return O?-1:d||c?c:C}};e.exports={forEach:u(0),map:u(1),filter:u(2),some:u(3),every:u(4),find:u(5),findIndex:u(6)}},c04e:function(e,h,t){var l=t("861d");e.exports=function(a,s){if(!l(a))return a;var f,i;if(s&&typeof(f=a.toString)=="function"&&!l(i=f.call(a))||typeof(f=a.valueOf)=="function"&&!l(i=f.call(a))||!s&&typeof(f=a.toString)=="function"&&!l(i=f.call(a)))return i;throw TypeError("Can't convert object to primitive value")}},c430:function(e,h){e.exports=!1},c6b6:function(e,h){var t={}.toString;e.exports=function(l){return t.call(l).slice(8,-1)}},c6cd:function(e,h,t){var l=t("da84"),a=t("ce4e"),s="__core-js_shared__",f=l[s]||a(s,{});e.exports=f},c740:function(e,h,t){var l=t("23e7"),a=t("b727").findIndex,s=t("44d2"),f=t("ae40"),i="findIndex",r=!0,u=f(i);i in[]&&Array(1)[i](function(){r=!1}),l({target:"Array",proto:!0,forced:r||!u},{findIndex:function(g){return a(this,g,arguments.length>1?arguments[1]:void 0)}}),s(i)},c8ba:function(e,h){var t;t=function(){return this}();try{t=t||new Function("return this")()}catch(l){typeof window=="object"&&(t=window)}e.exports=t},c975:function(e,h,t){var l=t("23e7"),a=t("4d64").indexOf,s=t("a640"),f=t("ae40"),i=[].indexOf,r=!!i&&1/[1].indexOf(1,-0)<0,u=s("indexOf"),y=f("indexOf",{ACCESSORS:!0,1:0});l({target:"Array",proto:!0,forced:r||!u||!y},{indexOf:function(p){return r?i.apply(this,arguments)||0:a(this,p,arguments.length>1?arguments[1]:void 0)}})},ca84:function(e,h,t){var l=t("5135"),a=t("fc6a"),s=t("4d64").indexOf,f=t("d012");e.exports=function(i,r){var u=a(i),y=0,g=[],p;for(p in u)!l(f,p)&&l(u,p)&&g.push(p);for(;r.length>y;)l(u,p=r[y++])&&(~s(g,p)||g.push(p));return g}},caad:function(e,h,t){var l=t("23e7"),a=t("4d64").includes,s=t("44d2"),f=t("ae40"),i=f("indexOf",{ACCESSORS:!0,1:0});l({target:"Array",proto:!0,forced:!i},{includes:function(u){return a(this,u,arguments.length>1?arguments[1]:void 0)}}),s("includes")},cc12:function(e,h,t){var l=t("da84"),a=t("861d"),s=l.document,f=a(s)&&a(s.createElement);e.exports=function(i){return f?s.createElement(i):{}}},ce4e:function(e,h,t){var l=t("da84"),a=t("9112");e.exports=function(s,f){try{a(l,s,f)}catch(i){l[s]=f}return f}},d012:function(e,h){e.exports={}},d039:function(e,h){e.exports=function(t){try{return!!t()}catch(l){return!0}}},d066:function(e,h,t){var l=t("428f"),a=t("da84"),s=function(f){return typeof f=="function"?f:void 0};e.exports=function(f,i){return arguments.length<2?s(l[f])||s(a[f]):l[f]&&l[f][i]||a[f]&&a[f][i]}},d1e7:function(e,h,t){var l={}.propertyIsEnumerable,a=Object.getOwnPropertyDescriptor,s=a&&!l.call({1:2},1);h.f=s?function(i){var r=a(this,i);return!!r&&r.enumerable}:l},d28b:function(e,h,t){var l=t("746f");l("iterator")},d2bb:function(e,h,t){var l=t("825a"),a=t("3bbe");e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var s=!1,f={},i;try{i=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set,i.call(f,[]),s=f instanceof Array}catch(r){}return function(u,y){return l(u),a(y),s?i.call(u,y):u.__proto__=y,u}}():void 0)},d3b7:function(e,h,t){var l=t("00ee"),a=t("6eeb"),s=t("b041");l||a(Object.prototype,"toString",s,{unsafe:!0})},d44e:function(e,h,t){var l=t("9bf2").f,a=t("5135"),s=t("b622"),f=s("toStringTag");e.exports=function(i,r,u){i&&!a(i=u?i:i.prototype,f)&&l(i,f,{configurable:!0,value:r})}},d58f:function(e,h,t){var l=t("1c0b"),a=t("7b0b"),s=t("44ad"),f=t("50c4"),i=function(r){return function(u,y,g,p){l(y);var d=a(u),c=s(d),O=f(d.length),v=r?O-1:0,E=r?-1:1;if(g<2)for(;;){if(v in c){p=c[v],v+=E;break}if(v+=E,r?v<0:O<=v)throw TypeError("Reduce of empty array with no initial value")}for(;r?v>=0:O>v;v+=E)v in c&&(p=y(p,c[v],v,d));return p}};e.exports={left:i(!1),right:i(!0)}},d784:function(e,h,t){t("ac1f");var l=t("6eeb"),a=t("d039"),s=t("b622"),f=t("9263"),i=t("9112"),r=s("species"),u=!a(function(){var c=/./;return c.exec=function(){var O=[];return O.groups={a:"7"},O},"".replace(c,"$<a>")!=="7"}),y=function(){return"a".replace(/./,"$0")==="$0"}(),g=s("replace"),p=function(){return/./[g]?/./[g]("a","$0")==="":!1}(),d=!a(function(){var c=/(?:)/,O=c.exec;c.exec=function(){return O.apply(this,arguments)};var v="ab".split(c);return v.length!==2||v[0]!=="a"||v[1]!=="b"});e.exports=function(c,O,v,E){var T=s(c),P=!a(function(){var N={};return N[T]=function(){return 7},""[c](N)!=7}),I=P&&!a(function(){var N=!1,R=/a/;return c==="split"&&(R={},R.constructor={},R.constructor[r]=function(){return R},R.flags="",R[T]=/./[T]),R.exec=function(){return N=!0,null},R[T](""),!N});if(!P||!I||c==="replace"&&!(u&&y&&!p)||c==="split"&&!d){var D=/./[T],w=v(T,""[c],function(N,R,C,B,U){return R.exec===f?P&&!U?{done:!0,value:D.call(R,C,B)}:{done:!0,value:N.call(C,R,B)}:{done:!1}},{REPLACE_KEEPS_$0:y,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:p}),S=w[0],A=w[1];l(String.prototype,c,S),l(RegExp.prototype,T,O==2?function(N,R){return A.call(N,this,R)}:function(N){return A.call(N,this)})}E&&i(RegExp.prototype[T],"sham",!0)}},d81d:function(e,h,t){var l=t("23e7"),a=t("b727").map,s=t("1dde"),f=t("ae40"),i=s("map"),r=f("map");l({target:"Array",proto:!0,forced:!i||!r},{map:function(y){return a(this,y,arguments.length>1?arguments[1]:void 0)}})},da84:function(e,h,t){(function(l){var a=function(s){return s&&s.Math==Math&&s};e.exports=a(typeof globalThis=="object"&&globalThis)||a(typeof window=="object"&&window)||a(typeof self=="object"&&self)||a(typeof l=="object"&&l)||Function("return this")()}).call(this,t("c8ba"))},dbb4:function(e,h,t){var l=t("23e7"),a=t("83ab"),s=t("56ef"),f=t("fc6a"),i=t("06cf"),r=t("8418");l({target:"Object",stat:!0,sham:!a},{getOwnPropertyDescriptors:function(y){for(var g=f(y),p=i.f,d=s(g),c={},O=0,v,E;d.length>O;)E=p(g,v=d[O++]),E!==void 0&&r(c,v,E);return c}})},dbf1:function(e,h,t){(function(l){t.d(h,"a",function(){return s});function a(){return typeof window!="undefined"?window.console:l.console}var s=a()}).call(this,t("c8ba"))},ddb0:function(e,h,t){var l=t("da84"),a=t("fdbc"),s=t("e260"),f=t("9112"),i=t("b622"),r=i("iterator"),u=i("toStringTag"),y=s.values;for(var g in a){var p=l[g],d=p&&p.prototype;if(d){if(d[r]!==y)try{f(d,r,y)}catch(O){d[r]=y}if(d[u]||f(d,u,g),a[g]){for(var c in s)if(d[c]!==s[c])try{f(d,c,s[c])}catch(O){d[c]=s[c]}}}}},df75:function(e,h,t){var l=t("ca84"),a=t("7839");e.exports=Object.keys||function(f){return l(f,a)}},e01a:function(e,h,t){var l=t("23e7"),a=t("83ab"),s=t("da84"),f=t("5135"),i=t("861d"),r=t("9bf2").f,u=t("e893"),y=s.Symbol;if(a&&typeof y=="function"&&(!("description"in y.prototype)||y().description!==void 0)){var g={},p=function(){var T=arguments.length<1||arguments[0]===void 0?void 0:String(arguments[0]),P=this instanceof p?new y(T):T===void 0?y():y(T);return T===""&&(g[P]=!0),P};u(p,y);var d=p.prototype=y.prototype;d.constructor=p;var c=d.toString,O=String(y("test"))=="Symbol(test)",v=/^Symbol\((.*)\)[^)]+$/;r(d,"description",{configurable:!0,get:function(){var T=i(this)?this.valueOf():this,P=c.call(T);if(f(g,T))return"";var I=O?P.slice(7,-1):P.replace(v,"$1");return I===""?void 0:I}}),l({global:!0,forced:!0},{Symbol:p})}},e163:function(e,h,t){var l=t("5135"),a=t("7b0b"),s=t("f772"),f=t("e177"),i=s("IE_PROTO"),r=Object.prototype;e.exports=f?Object.getPrototypeOf:function(u){return u=a(u),l(u,i)?u[i]:typeof u.constructor=="function"&&u instanceof u.constructor?u.constructor.prototype:u instanceof Object?r:null}},e177:function(e,h,t){var l=t("d039");e.exports=!l(function(){function a(){}return a.prototype.constructor=null,Object.getPrototypeOf(new a)!==a.prototype})},e260:function(e,h,t){var l=t("fc6a"),a=t("44d2"),s=t("3f8c"),f=t("69f3"),i=t("7dd0"),r="Array Iterator",u=f.set,y=f.getterFor(r);e.exports=i(Array,"Array",function(g,p){u(this,{type:r,target:l(g),index:0,kind:p})},function(){var g=y(this),p=g.target,d=g.kind,c=g.index++;return!p||c>=p.length?(g.target=void 0,{value:void 0,done:!0}):d=="keys"?{value:c,done:!1}:d=="values"?{value:p[c],done:!1}:{value:[c,p[c]],done:!1}},"values"),s.Arguments=s.Array,a("keys"),a("values"),a("entries")},e439:function(e,h,t){var l=t("23e7"),a=t("d039"),s=t("fc6a"),f=t("06cf").f,i=t("83ab"),r=a(function(){f(1)}),u=!i||r;l({target:"Object",stat:!0,forced:u,sham:!i},{getOwnPropertyDescriptor:function(g,p){return f(s(g),p)}})},e538:function(e,h,t){var l=t("b622");h.f=l},e893:function(e,h,t){var l=t("5135"),a=t("56ef"),s=t("06cf"),f=t("9bf2");e.exports=function(i,r){for(var u=a(r),y=f.f,g=s.f,p=0;p<u.length;p++){var d=u[p];l(i,d)||y(i,d,g(r,d))}}},e8b5:function(e,h,t){var l=t("c6b6");e.exports=Array.isArray||function(s){return l(s)=="Array"}},e95a:function(e,h,t){var l=t("b622"),a=t("3f8c"),s=l("iterator"),f=Array.prototype;e.exports=function(i){return i!==void 0&&(a.Array===i||f[s]===i)}},f5df:function(e,h,t){var l=t("00ee"),a=t("c6b6"),s=t("b622"),f=s("toStringTag"),i=a(function(){return arguments}())=="Arguments",r=function(u,y){try{return u[y]}catch(g){}};e.exports=l?a:function(u){var y,g,p;return u===void 0?"Undefined":u===null?"Null":typeof(g=r(y=Object(u),f))=="string"?g:i?a(y):(p=a(y))=="Object"&&typeof y.callee=="function"?"Arguments":p}},f772:function(e,h,t){var l=t("5692"),a=t("90e3"),s=l("keys");e.exports=function(f){return s[f]||(s[f]=a(f))}},fb15:function(e,h,t){if(t.r(h),typeof window!="undefined"){var l=window.document.currentScript;{var a=t("8875");l=a(),"currentScript"in document||Object.defineProperty(document,"currentScript",{get:a})}var s=l&&l.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);s&&(t.p=s[1])}t("99af"),t("4de4"),t("4160"),t("c975"),t("d81d"),t("a434"),t("159b"),t("a4d3"),t("e439"),t("dbb4"),t("b64b");function f(Y,V,X){return V in Y?Object.defineProperty(Y,V,{value:X,enumerable:!0,configurable:!0,writable:!0}):Y[V]=X,Y}function i(Y,V){var X=Object.keys(Y);if(Object.getOwnPropertySymbols){var rt=Object.getOwnPropertySymbols(Y);V&&(rt=rt.filter(function(ct){return Object.getOwnPropertyDescriptor(Y,ct).enumerable})),X.push.apply(X,rt)}return X}function r(Y){for(var V=1;V<arguments.length;V++){var X=arguments[V]!=null?arguments[V]:{};V%2?i(Object(X),!0).forEach(function(rt){f(Y,rt,X[rt])}):Object.getOwnPropertyDescriptors?Object.defineProperties(Y,Object.getOwnPropertyDescriptors(X)):i(Object(X)).forEach(function(rt){Object.defineProperty(Y,rt,Object.getOwnPropertyDescriptor(X,rt))})}return Y}function u(Y){if(Array.isArray(Y))return Y}t("e01a"),t("d28b"),t("e260"),t("d3b7"),t("3ca3"),t("ddb0");function y(Y,V){if(!(typeof Symbol=="undefined"||!(Symbol.iterator in Object(Y)))){var X=[],rt=!0,ct=!1,Et=void 0;try{for(var At=Y[Symbol.iterator](),Mt;!(rt=(Mt=At.next()).done)&&(X.push(Mt.value),!(V&&X.length===V));rt=!0);}catch(Gt){ct=!0,Et=Gt}finally{try{!rt&&At.return!=null&&At.return()}finally{if(ct)throw Et}}return X}}t("a630"),t("fb6a"),t("b0c0"),t("25f0");function g(Y,V){(V==null||V>Y.length)&&(V=Y.length);for(var X=0,rt=new Array(V);X<V;X++)rt[X]=Y[X];return rt}function p(Y,V){if(Y){if(typeof Y=="string")return g(Y,V);var X=Object.prototype.toString.call(Y).slice(8,-1);if(X==="Object"&&Y.constructor&&(X=Y.constructor.name),X==="Map"||X==="Set")return Array.from(Y);if(X==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(X))return g(Y,V)}}function d(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function c(Y,V){return u(Y)||y(Y,V)||p(Y,V)||d()}function O(Y){if(Array.isArray(Y))return g(Y)}function v(Y){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(Y))return Array.from(Y)}function E(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function T(Y){return O(Y)||v(Y)||p(Y)||E()}var P=t("a352"),I=t.n(P);function D(Y){Y.parentElement!==null&&Y.parentElement.removeChild(Y)}function w(Y,V,X){var rt=X===0?Y.children[0]:Y.children[X-1].nextSibling;Y.insertBefore(V,rt)}var S=t("dbf1");t("13d5"),t("4fad"),t("ac1f"),t("5319");function A(Y){var V=Object.create(null);return function(rt){var ct=V[rt];return ct||(V[rt]=Y(rt))}}var N=/-(\w)/g,R=A(function(Y){return Y.replace(N,function(V,X){return X.toUpperCase()})});t("5db7"),t("73d9");var C=["Start","Add","Remove","Update","End"],B=["Choose","Unchoose","Sort","Filter","Clone"],U=["Move"],z=[U,C,B].flatMap(function(Y){return Y}).map(function(Y){return"on".concat(Y)}),K={manage:U,manageAndEmit:C,emit:B};function k(Y){return z.indexOf(Y)!==-1}t("caad"),t("2ca0");var L=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","link","main","map","mark","math","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rb","rp","rt","rtc","ruby","s","samp","script","section","select","slot","small","source","span","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr"];function _(Y){return L.includes(Y)}function F(Y){return["transition-group","TransitionGroup"].includes(Y)}function Q(Y){return["id","class","role","style"].includes(Y)||Y.startsWith("data-")||Y.startsWith("aria-")||Y.startsWith("on")}function q(Y){return Y.reduce(function(V,X){var rt=c(X,2),ct=rt[0],Et=rt[1];return V[ct]=Et,V},{})}function M(Y){var V=Y.$attrs,X=Y.componentData,rt=X===void 0?{}:X,ct=q(Object.entries(V).filter(function(Et){var At=c(Et,2),Mt=At[0];return At[1],Q(Mt)}));return r(r({},ct),rt)}function H(Y){var V=Y.$attrs,X=Y.callBackBuilder,rt=q(W(V));Object.entries(X).forEach(function(Et){var At=c(Et,2),Mt=At[0],Gt=At[1];K[Mt].forEach(function(Ot){rt["on".concat(Ot)]=Gt(Ot)})});var ct="[data-draggable]".concat(rt.draggable||"");return r(r({},rt),{},{draggable:ct})}function W(Y){return Object.entries(Y).filter(function(V){var X=c(V,2),rt=X[0];return X[1],!Q(rt)}).map(function(V){var X=c(V,2),rt=X[0],ct=X[1];return[R(rt),ct]}).filter(function(V){var X=c(V,2),rt=X[0];return X[1],!k(rt)})}t("c740");function $(Y,V){if(!(Y instanceof V))throw new TypeError("Cannot call a class as a function")}function et(Y,V){for(var X=0;X<V.length;X++){var rt=V[X];rt.enumerable=rt.enumerable||!1,rt.configurable=!0,"value"in rt&&(rt.writable=!0),Object.defineProperty(Y,rt.key,rt)}}function ut(Y,V,X){return V&&et(Y.prototype,V),Y}var pt=function(V){var X=V.el;return X},dt=function(V,X){return V.__draggable_context=X},gt=function(V){return V.__draggable_context},Nt=function(){function Y(V){var X=V.nodes,rt=X.header,ct=X.default,Et=X.footer,At=V.root,Mt=V.realList;$(this,Y),this.defaultNodes=ct,this.children=[].concat(T(rt),T(ct),T(Et)),this.externalComponent=At.externalComponent,this.rootTransition=At.transition,this.tag=At.tag,this.realList=Mt}return ut(Y,[{key:"render",value:function(X,rt){var ct=this.tag,Et=this.children,At=this._isRootComponent,Mt=At?{default:function(){return Et}}:Et;return X(ct,rt,Mt)}},{key:"updated",value:function(){var X=this.defaultNodes,rt=this.realList;X.forEach(function(ct,Et){dt(pt(ct),{element:rt[Et],index:Et})})}},{key:"getUnderlyingVm",value:function(X){return gt(X)}},{key:"getVmIndexFromDomIndex",value:function(X,rt){var ct=this.defaultNodes,Et=ct.length,At=rt.children,Mt=At.item(X);if(Mt===null)return Et;var Gt=gt(Mt);if(Gt)return Gt.index;if(Et===0)return 0;var Ot=pt(ct[0]),st=T(At).findIndex(function(yt){return yt===Ot});return X<st?0:Et}},{key:"_isRootComponent",get:function(){return this.externalComponent||this.rootTransition}}]),Y}(),qt=t("8bbf");function kt(Y,V){var X=Y[V];return X?X():[]}function Z(Y){var V=Y.$slots,X=Y.realList,rt=Y.getKey,ct=X||[],Et=["header","footer"].map(function(yt){return kt(V,yt)}),At=c(Et,2),Mt=At[0],Gt=At[1],Ot=V.item;if(!Ot)throw new Error("draggable element must have an item slot");var st=ct.flatMap(function(yt,Dt){return Ot({element:yt,index:Dt}).map(function(Rt){return Rt.key=rt(yt),Rt.props=r(r({},Rt.props||{}),{},{"data-draggable":!0}),Rt})});if(st.length!==ct.length)throw new Error("Item slot must have only one child");return{header:Mt,footer:Gt,default:st}}function tt(Y){var V=F(Y),X=!_(Y)&&!V;return{transition:V,externalComponent:X,tag:X?Object(qt.resolveComponent)(Y):V?qt.TransitionGroup:Y}}function it(Y){var V=Y.$slots,X=Y.tag,rt=Y.realList,ct=Y.getKey,Et=Z({$slots:V,realList:rt,getKey:ct}),At=tt(X);return new Nt({nodes:Et,root:At,realList:rt})}function ot(Y,V){var X=this;Object(qt.nextTick)(function(){return X.$emit(Y.toLowerCase(),V)})}function nt(Y){var V=this;return function(X,rt){if(V.realList!==null)return V["onDrag".concat(Y)](X,rt)}}function xt(Y){var V=this,X=nt.call(this,Y);return function(rt,ct){X.call(V,rt,ct),ot.call(V,Y,rt)}}var bt=null,St={list:{type:Array,required:!1,default:null},modelValue:{type:Array,required:!1,default:null},itemKey:{type:[String,Function],required:!0},clone:{type:Function,default:function(V){return V}},tag:{type:String,default:"div"},move:{type:Function,default:null},componentData:{type:Object,required:!1,default:null}},Zt=["update:modelValue","change"].concat(T([].concat(T(K.manageAndEmit),T(K.emit)).map(function(Y){return Y.toLowerCase()}))),Yt=Object(qt.defineComponent)({name:"draggable",inheritAttrs:!1,props:St,emits:Zt,data:function(){return{error:!1}},render:function(){try{this.error=!1;var V=this.$slots,X=this.$attrs,rt=this.tag,ct=this.componentData,Et=this.realList,At=this.getKey,Mt=it({$slots:V,tag:rt,realList:Et,getKey:At});this.componentStructure=Mt;var Gt=M({$attrs:X,componentData:ct});return Mt.render(qt.h,Gt)}catch(Ot){return this.error=!0,Object(qt.h)("pre",{style:{color:"red"}},Ot.stack)}},created:function(){this.list!==null&&this.modelValue!==null&&S.a.error("modelValue and list props are mutually exclusive! Please set one or another.")},mounted:function(){var V=this;if(!this.error){var X=this.$attrs,rt=this.$el,ct=this.componentStructure;ct.updated();var Et=H({$attrs:X,callBackBuilder:{manageAndEmit:function(Gt){return xt.call(V,Gt)},emit:function(Gt){return ot.bind(V,Gt)},manage:function(Gt){return nt.call(V,Gt)}}}),At=rt.nodeType===1?rt:rt.parentElement;this._sortable=new I.a(At,Et),this.targetDomElement=At,At.__draggable_component__=this}},updated:function(){this.componentStructure.updated()},beforeUnmount:function(){this._sortable!==void 0&&this._sortable.destroy()},computed:{realList:function(){var V=this.list;return V||this.modelValue},getKey:function(){var V=this.itemKey;return typeof V=="function"?V:function(X){return X[V]}}},watch:{$attrs:{handler:function(V){var X=this._sortable;X&&W(V).forEach(function(rt){var ct=c(rt,2),Et=ct[0],At=ct[1];X.option(Et,At)})},deep:!0}},methods:{getUnderlyingVm:function(V){return this.componentStructure.getUnderlyingVm(V)||null},getUnderlyingPotencialDraggableComponent:function(V){return V.__draggable_component__},emitChanges:function(V){var X=this;Object(qt.nextTick)(function(){return X.$emit("change",V)})},alterList:function(V){if(this.list){V(this.list);return}var X=T(this.modelValue);V(X),this.$emit("update:modelValue",X)},spliceList:function(){var V=arguments,X=function(ct){return ct.splice.apply(ct,T(V))};this.alterList(X)},updatePosition:function(V,X){var rt=function(Et){return Et.splice(X,0,Et.splice(V,1)[0])};this.alterList(rt)},getRelatedContextFromMoveEvent:function(V){var X=V.to,rt=V.related,ct=this.getUnderlyingPotencialDraggableComponent(X);if(!ct)return{component:ct};var Et=ct.realList,At={list:Et,component:ct};if(X!==rt&&Et){var Mt=ct.getUnderlyingVm(rt)||{};return r(r({},Mt),At)}return At},getVmIndexFromDomIndex:function(V){return this.componentStructure.getVmIndexFromDomIndex(V,this.targetDomElement)},onDragStart:function(V){this.context=this.getUnderlyingVm(V.item),V.item._underlying_vm_=this.clone(this.context.element),bt=V.item},onDragAdd:function(V){var X=V.item._underlying_vm_;if(X!==void 0){D(V.item);var rt=this.getVmIndexFromDomIndex(V.newIndex);this.spliceList(rt,0,X);var ct={element:X,newIndex:rt};this.emitChanges({added:ct})}},onDragRemove:function(V){if(w(this.$el,V.item,V.oldIndex),V.pullMode==="clone"){D(V.clone);return}var X=this.context,rt=X.index,ct=X.element;this.spliceList(rt,1);var Et={element:ct,oldIndex:rt};this.emitChanges({removed:Et})},onDragUpdate:function(V){D(V.item),w(V.from,V.item,V.oldIndex);var X=this.context.index,rt=this.getVmIndexFromDomIndex(V.newIndex);this.updatePosition(X,rt);var ct={element:this.context.element,oldIndex:X,newIndex:rt};this.emitChanges({moved:ct})},computeFutureIndex:function(V,X){if(!V.element)return 0;var rt=T(X.to.children).filter(function(Mt){return Mt.style.display!=="none"}),ct=rt.indexOf(X.related),Et=V.component.getVmIndexFromDomIndex(ct),At=rt.indexOf(bt)!==-1;return At||!X.willInsertAfter?Et:Et+1},onDragMove:function(V,X){var rt=this.move,ct=this.realList;if(!rt||!ct)return!0;var Et=this.getRelatedContextFromMoveEvent(V),At=this.computeFutureIndex(Et,V),Mt=r(r({},this.context),{},{futureIndex:At}),Gt=r(r({},V),{},{relatedContext:Et,draggedContext:Mt});return rt(Gt,X)},onDragEnd:function(){bt=null}}}),jt=Yt;h.default=jt},fb6a:function(e,h,t){var l=t("23e7"),a=t("861d"),s=t("e8b5"),f=t("23cb"),i=t("50c4"),r=t("fc6a"),u=t("8418"),y=t("b622"),g=t("1dde"),p=t("ae40"),d=g("slice"),c=p("slice",{ACCESSORS:!0,0:0,1:2}),O=y("species"),v=[].slice,E=Math.max;l({target:"Array",proto:!0,forced:!d||!c},{slice:function(P,I){var D=r(this),w=i(D.length),S=f(P,w),A=f(I===void 0?w:I,w),N,R,C;if(s(D)&&(N=D.constructor,typeof N=="function"&&(N===Array||s(N.prototype))?N=void 0:a(N)&&(N=N[O],N===null&&(N=void 0)),N===Array||N===void 0))return v.call(D,S,A);for(R=new(N===void 0?Array:N)(E(A-S,0)),C=0;S<A;S++,C++)S in D&&u(R,C,D[S]);return R.length=C,R}})},fc6a:function(e,h,t){var l=t("44ad"),a=t("1d80");e.exports=function(s){return l(a(s))}},fdbc:function(e,h){e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},fdbf:function(e,h,t){var l=t("4930");e.exports=l&&!Symbol.sham&&typeof Symbol.iterator=="symbol"}}).default})})(mo);var qa=mo.exports;const Ha=qi(qa),Ka=jr({__name:"QuestionList",props:{questions:{type:Array,required:!0},loading:{type:Boolean,required:!1,default:!1}},emits:["update:questions","question-select","question-edit","question-delete","questions-reorder","score-change","add-question"],setup(b,{expose:m,emit:o}){m();const n=b,e=o,h=ne([]),t=ne(null),l=ne(!1),a=ne(null),s=Wn(()=>n.questions),f=Wn(()=>n.questions.length>0&&h.value.length===n.questions.length),i={single_choice:"单选题",multiple_choice:"多选题",true_false:"判断题",fill_blank:"填空题",short_answer:"简答题",essay:"解答题",calculation:"计算题",application:"应用题",analysis:"分析题",comprehensive:"综合题"},r={easy:"容易",medium:"中等",hard:"困难",very_hard:"很难"},u=S=>i[S]||S,y=S=>r[S]||S,g=S=>n.questions.findIndex(A=>A.id===S),p=(S,A)=>{A?h.value.push(S):h.value=h.value.filter(N=>N!==S),e("question-select",h.value)},d=()=>{f.value?h.value=[]:h.value=n.questions.map(S=>S.id),e("question-select",h.value)},c=()=>We(this,null,function*(){if(h.value.length!==0)try{yield ji.confirm(`确定要删除选中的 ${h.value.length} 道题目吗？`,"批量删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}),h.value.forEach(S=>{e("question-delete",S)}),h.value=[],le.success("批量删除成功")}catch(S){}}),O=S=>{t.value=S,e("question-edit",S)},v=S=>We(this,null,function*(){try{yield ji.confirm("确定要删除这道题目吗？","删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}),e("question-delete",S),h.value=h.value.filter(A=>A!==S),le.success("题目删除成功")}catch(A){}}),E=S=>{a.value=S,l.value=!0},T=()=>{l.value=!1,a.value=null},P=()=>{const S=n.questions.map((A,N)=>Oi(Se({},A),{order:N+1}));e("questions-reorder",S)},I=(S,A)=>{A&&A>0&&e("score-change",S,A)},D=()=>{e("add-question")};hn(()=>n.questions,S=>{const A=S.map(N=>N.id);h.value=h.value.filter(N=>A.includes(N))},{deep:!0,immediate:!1});const w={props:n,emit:e,selectedQuestions:h,editingQuestion:t,previewVisible:l,previewQuestion:a,questionList:s,isAllSelected:f,questionTypeLabels:i,difficultyLabels:r,getQuestionTypeLabel:u,getDifficultyLabel:y,getQuestionIndex:g,handleQuestionSelect:p,handleSelectAll:d,handleBatchDelete:c,handleEditQuestion:O,handleDeleteQuestion:v,handlePreviewQuestion:E,handlePreviewClose:T,handleDragChange:P,handleScoreChange:I,handleAddQuestion:D,get Menu(){return ea},get Edit(){return ta},get View(){return Jo},get Delete(){return Zo},get draggable(){return Ha}};return Object.defineProperty(w,"__isScriptSetup",{enumerable:!1,value:!0}),w}}),Ga={class:"question-list"},za={class:"list-header"},Va={class:"header-info"},Qa={class:"header-actions"},Wa={class:"list-content"},$a={key:0,class:"empty-state"},Ya={key:1,class:"question-items"},Xa={class:"item-controls"},Za={class:"drag-handle"},Ja={class:"question-number"},tl={class:"item-content"},el={class:"content-header"},nl={class:"question-tags"},rl={class:"question-actions"},il={class:"content-body"},ol=["innerHTML"],al={key:0,class:"question-options"},ll={class:"question-answer"},ul={key:1,class:"question-explanation"},sl=["innerHTML"],fl={class:"content-meta"},cl={class:"meta-info"},dl={class:"score-editor"},hl={key:0,class:"preview-content"},vl={class:"preview-header"},pl={class:"preview-tags"},gl={class:"preview-body"},yl=["innerHTML"],ml={key:0,class:"preview-options"},bl={class:"preview-answer"},Ol={key:1,class:"preview-explanation"},El=["innerHTML"];function Sl(b,m,o,n,e,h){const t=Ft("el-tag"),l=Ft("el-button"),a=Ft("el-empty"),s=Ft("el-checkbox"),f=Ft("el-icon"),i=Ft("el-button-group"),r=Ft("el-collapse-item"),u=Ft("el-collapse"),y=Ft("el-input-number"),g=Ft("el-dialog");return Ct(),zt("div",Ga,[ht("div",za,[ht("div",Va,[m[1]||(m[1]=ht("span",{class:"title"},"题目列表",-1)),at(t,{type:"info",size:"small"},{default:ft(()=>[Vt("共 "+Kt(o.questions.length)+" 题",1)]),_:1})]),ht("div",Qa,[at(l,{size:"small",onClick:n.handleSelectAll},{default:ft(()=>[Vt(Kt(n.isAllSelected?"取消全选":"全选"),1)]),_:1}),at(l,{size:"small",type:"danger",onClick:n.handleBatchDelete,disabled:n.selectedQuestions.length===0},{default:ft(()=>[Vt(" 批量删除 ("+Kt(n.selectedQuestions.length)+") ",1)]),_:1},8,["disabled"]),at(l,{size:"small",type:"primary",onClick:n.handleAddQuestion},{default:ft(()=>m[2]||(m[2]=[Vt(" 添加题目 ",-1)])),_:1,__:[2]})])]),ht("div",Wa,[o.questions.length===0?(Ct(),zt("div",$a,[at(a,{description:"暂无题目"},{default:ft(()=>[at(l,{type:"primary",onClick:n.handleAddQuestion},{default:ft(()=>m[3]||(m[3]=[Vt("添加第一道题目",-1)])),_:1,__:[3]})]),_:1})])):(Ct(),zt("div",Ya,[at(n.draggable,{list:n.questionList,"item-key":"id",handle:".drag-handle",onChange:n.handleDragChange,animation:200},{item:ft(({element:p,index:d})=>[ht("div",{class:Vo(["question-item",{selected:n.selectedQuestions.includes(p.id),editing:n.editingQuestion===p.id}])},[ht("div",Xa,[at(s,{"model-value":n.selectedQuestions.includes(p.id),onChange:c=>n.handleQuestionSelect(p.id,c)},null,8,["model-value","onChange"]),ht("div",Za,[at(f,null,{default:ft(()=>[at(n.Menu)]),_:1})]),ht("span",Ja,Kt(d+1),1)]),ht("div",tl,[ht("div",el,[ht("div",nl,[at(t,{size:"small",type:"info"},{default:ft(()=>[Vt(Kt(n.getQuestionTypeLabel(p.tags.questionType)),1)]),_:2},1024),at(t,{size:"small",type:"warning"},{default:ft(()=>[Vt(Kt(n.getDifficultyLabel(p.tags.difficulty)),1)]),_:2},1024),at(t,{size:"small"},{default:ft(()=>[Vt(Kt(p.score)+"分",1)]),_:2},1024)]),ht("div",rl,[at(i,{size:"small"},{default:ft(()=>[at(l,{onClick:c=>n.handleEditQuestion(p.id),size:"small"},{default:ft(()=>[at(f,null,{default:ft(()=>[at(n.Edit)]),_:1})]),_:2},1032,["onClick"]),at(l,{onClick:c=>n.handlePreviewQuestion(p),size:"small"},{default:ft(()=>[at(f,null,{default:ft(()=>[at(n.View)]),_:1})]),_:2},1032,["onClick"]),at(l,{type:"danger",onClick:c=>n.handleDeleteQuestion(p.id),size:"small"},{default:ft(()=>[at(f,null,{default:ft(()=>[at(n.Delete)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)])]),ht("div",il,[ht("div",{class:"question-stem",innerHTML:p.content.stem},null,8,ol),p.content.options&&p.content.options.length>0?(Ct(),zt("div",al,[(Ct(!0),zt($e,null,Ye(p.content.options,(c,O)=>(Ct(),zt("div",{key:O,class:"option-item"},Kt(String.fromCharCode(65+O))+". "+Kt(c),1))),128))])):nn("",!0),ht("div",ll,[m[4]||(m[4]=ht("strong",null,"答案：",-1)),Vt(Kt(p.content.answer),1)]),p.content.explanation?(Ct(),zt("div",ul,[at(u,null,{default:ft(()=>[at(r,{title:"查看解析",name:"explanation"},{default:ft(()=>[ht("div",{innerHTML:p.content.explanation},null,8,sl)]),_:2},1024)]),_:2},1024)])):nn("",!0)]),ht("div",fl,[ht("div",cl,[ht("span",null,"知识点："+Kt(p.tags.knowledgePoint.join("、")),1),ht("span",null,"来源："+Kt(p.tags.sourceType),1)]),ht("div",dl,[m[5]||(m[5]=ht("span",null,"分值：",-1)),at(y,{"model-value":p.score,min:1,max:100,size:"small",style:{width:"80px"},onChange:c=>n.handleScoreChange(p.id,c)},null,8,["model-value","onChange"])])])])],2)]),_:1},8,["list"])]))]),at(g,{modelValue:n.previewVisible,"onUpdate:modelValue":m[0]||(m[0]=p=>n.previewVisible=p),title:"题目预览",width:"60%","before-close":n.handlePreviewClose},{default:ft(()=>[n.previewQuestion?(Ct(),zt("div",hl,[ht("div",vl,[ht("h3",null,"第 "+Kt(n.getQuestionIndex(n.previewQuestion.id)+1)+" 题 ("+Kt(n.previewQuestion.score)+"分)",1),ht("div",pl,[at(t,{type:"info"},{default:ft(()=>[Vt(Kt(n.getQuestionTypeLabel(n.previewQuestion.tags.questionType)),1)]),_:1}),at(t,{type:"warning"},{default:ft(()=>[Vt(Kt(n.getDifficultyLabel(n.previewQuestion.tags.difficulty)),1)]),_:1})])]),ht("div",gl,[ht("div",{class:"preview-stem",innerHTML:n.previewQuestion.content.stem},null,8,yl),n.previewQuestion.content.options?(Ct(),zt("div",ml,[(Ct(!0),zt($e,null,Ye(n.previewQuestion.content.options,(p,d)=>(Ct(),zt("div",{key:d,class:"preview-option"},Kt(String.fromCharCode(65+d))+". "+Kt(p),1))),128))])):nn("",!0),ht("div",bl,[m[6]||(m[6]=ht("strong",null,"答案：",-1)),Vt(Kt(n.previewQuestion.content.answer),1)]),n.previewQuestion.content.explanation?(Ct(),zt("div",Ol,[m[7]||(m[7]=ht("strong",null,"解析：",-1)),ht("div",{innerHTML:n.previewQuestion.content.explanation},null,8,El)])):nn("",!0)])])):nn("",!0)]),_:1},8,["modelValue"])])}const xl=Hi(Ka,[["render",Sl],["__scopeId","data-v-71d296a2"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/components/ui/QuestionList.vue"]]);var Do={exports:{}};/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, Jason Chen
 * Copyright (c) 2013, salesforce.com
 */(function(b,m){(function(n,e){b.exports=e()})(typeof self!="undefined"?self:ue,function(){return function(o){var n={};function e(h){if(n[h])return n[h].exports;var t=n[h]={i:h,l:!1,exports:{}};return o[h].call(t.exports,t,t.exports,e),t.l=!0,t.exports}return e.m=o,e.c=n,e.d=function(h,t,l){e.o(h,t)||Object.defineProperty(h,t,{configurable:!1,enumerable:!0,get:l})},e.n=function(h){var t=h&&h.__esModule?function(){return h.default}:function(){return h};return e.d(t,"a",t),t},e.o=function(h,t){return Object.prototype.hasOwnProperty.call(h,t)},e.p="",e(e.s=109)}([function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=e(17),t=e(18),l=e(19),a=e(45),s=e(46),f=e(47),i=e(48),r=e(49),u=e(12),y=e(32),g=e(33),p=e(31),d=e(1),c={Scope:d.Scope,create:d.create,find:d.find,query:d.query,register:d.register,Container:h.default,Format:t.default,Leaf:l.default,Embed:i.default,Scroll:a.default,Block:f.default,Inline:s.default,Text:r.default,Attributor:{Attribute:u.default,Class:y.default,Style:g.default,Store:p.default}};n.default=c},function(o,n,e){var h=this&&this.__extends||function(){var p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,c){d.__proto__=c}||function(d,c){for(var O in c)c.hasOwnProperty(O)&&(d[O]=c[O])};return function(d,c){p(d,c);function O(){this.constructor=d}d.prototype=c===null?Object.create(c):(O.prototype=c.prototype,new O)}}();Object.defineProperty(n,"__esModule",{value:!0});var t=function(p){h(d,p);function d(c){var O=this;return c="[Parchment] "+c,O=p.call(this,c)||this,O.message=c,O.name=O.constructor.name,O}return d}(Error);n.ParchmentError=t;var l={},a={},s={},f={};n.DATA_KEY="__blot";var i;(function(p){p[p.TYPE=3]="TYPE",p[p.LEVEL=12]="LEVEL",p[p.ATTRIBUTE=13]="ATTRIBUTE",p[p.BLOT=14]="BLOT",p[p.INLINE=7]="INLINE",p[p.BLOCK=11]="BLOCK",p[p.BLOCK_BLOT=10]="BLOCK_BLOT",p[p.INLINE_BLOT=6]="INLINE_BLOT",p[p.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",p[p.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",p[p.ANY=15]="ANY"})(i=n.Scope||(n.Scope={}));function r(p,d){var c=y(p);if(c==null)throw new t("Unable to create "+p+" blot");var O=c,v=p instanceof Node||p.nodeType===Node.TEXT_NODE?p:O.create(d);return new O(v,d)}n.create=r;function u(p,d){return d===void 0&&(d=!1),p==null?null:p[n.DATA_KEY]!=null?p[n.DATA_KEY].blot:d?u(p.parentNode,d):null}n.find=u;function y(p,d){d===void 0&&(d=i.ANY);var c;if(typeof p=="string")c=f[p]||l[p];else if(p instanceof Text||p.nodeType===Node.TEXT_NODE)c=f.text;else if(typeof p=="number")p&i.LEVEL&i.BLOCK?c=f.block:p&i.LEVEL&i.INLINE&&(c=f.inline);else if(p instanceof HTMLElement){var O=(p.getAttribute("class")||"").split(/\s+/);for(var v in O)if(c=a[O[v]],c)break;c=c||s[p.tagName]}return c==null?null:d&i.LEVEL&c.scope&&d&i.TYPE&c.scope?c:null}n.query=y;function g(){for(var p=[],d=0;d<arguments.length;d++)p[d]=arguments[d];if(p.length>1)return p.map(function(v){return g(v)});var c=p[0];if(typeof c.blotName!="string"&&typeof c.attrName!="string")throw new t("Invalid definition");if(c.blotName==="abstract")throw new t("Cannot register abstract class");if(f[c.blotName||c.attrName]=c,typeof c.keyName=="string")l[c.keyName]=c;else if(c.className!=null&&(a[c.className]=c),c.tagName!=null){Array.isArray(c.tagName)?c.tagName=c.tagName.map(function(v){return v.toUpperCase()}):c.tagName=c.tagName.toUpperCase();var O=Array.isArray(c.tagName)?c.tagName:[c.tagName];O.forEach(function(v){(s[v]==null||c.className==null)&&(s[v]=c)})}return c}n.register=g},function(o,n,e){var h=e(51),t=e(11),l=e(3),a=e(20),s="\0",f=function(i){Array.isArray(i)?this.ops=i:i!=null&&Array.isArray(i.ops)?this.ops=i.ops:this.ops=[]};f.prototype.insert=function(i,r){var u={};return i.length===0?this:(u.insert=i,r!=null&&typeof r=="object"&&Object.keys(r).length>0&&(u.attributes=r),this.push(u))},f.prototype.delete=function(i){return i<=0?this:this.push({delete:i})},f.prototype.retain=function(i,r){if(i<=0)return this;var u={retain:i};return r!=null&&typeof r=="object"&&Object.keys(r).length>0&&(u.attributes=r),this.push(u)},f.prototype.push=function(i){var r=this.ops.length,u=this.ops[r-1];if(i=l(!0,{},i),typeof u=="object"){if(typeof i.delete=="number"&&typeof u.delete=="number")return this.ops[r-1]={delete:u.delete+i.delete},this;if(typeof u.delete=="number"&&i.insert!=null&&(r-=1,u=this.ops[r-1],typeof u!="object"))return this.ops.unshift(i),this;if(t(i.attributes,u.attributes)){if(typeof i.insert=="string"&&typeof u.insert=="string")return this.ops[r-1]={insert:u.insert+i.insert},typeof i.attributes=="object"&&(this.ops[r-1].attributes=i.attributes),this;if(typeof i.retain=="number"&&typeof u.retain=="number")return this.ops[r-1]={retain:u.retain+i.retain},typeof i.attributes=="object"&&(this.ops[r-1].attributes=i.attributes),this}}return r===this.ops.length?this.ops.push(i):this.ops.splice(r,0,i),this},f.prototype.chop=function(){var i=this.ops[this.ops.length-1];return i&&i.retain&&!i.attributes&&this.ops.pop(),this},f.prototype.filter=function(i){return this.ops.filter(i)},f.prototype.forEach=function(i){this.ops.forEach(i)},f.prototype.map=function(i){return this.ops.map(i)},f.prototype.partition=function(i){var r=[],u=[];return this.forEach(function(y){var g=i(y)?r:u;g.push(y)}),[r,u]},f.prototype.reduce=function(i,r){return this.ops.reduce(i,r)},f.prototype.changeLength=function(){return this.reduce(function(i,r){return r.insert?i+a.length(r):r.delete?i-r.delete:i},0)},f.prototype.length=function(){return this.reduce(function(i,r){return i+a.length(r)},0)},f.prototype.slice=function(i,r){i=i||0,typeof r!="number"&&(r=1/0);for(var u=[],y=a.iterator(this.ops),g=0;g<r&&y.hasNext();){var p;g<i?p=y.next(i-g):(p=y.next(r-g),u.push(p)),g+=a.length(p)}return new f(u)},f.prototype.compose=function(i){var r=a.iterator(this.ops),u=a.iterator(i.ops),y=[],g=u.peek();if(g!=null&&typeof g.retain=="number"&&g.attributes==null){for(var p=g.retain;r.peekType()==="insert"&&r.peekLength()<=p;)p-=r.peekLength(),y.push(r.next());g.retain-p>0&&u.next(g.retain-p)}for(var d=new f(y);r.hasNext()||u.hasNext();)if(u.peekType()==="insert")d.push(u.next());else if(r.peekType()==="delete")d.push(r.next());else{var c=Math.min(r.peekLength(),u.peekLength()),O=r.next(c),v=u.next(c);if(typeof v.retain=="number"){var E={};typeof O.retain=="number"?E.retain=c:E.insert=O.insert;var T=a.attributes.compose(O.attributes,v.attributes,typeof O.retain=="number");if(T&&(E.attributes=T),d.push(E),!u.hasNext()&&t(d.ops[d.ops.length-1],E)){var P=new f(r.rest());return d.concat(P).chop()}}else typeof v.delete=="number"&&typeof O.retain=="number"&&d.push(v)}return d.chop()},f.prototype.concat=function(i){var r=new f(this.ops.slice());return i.ops.length>0&&(r.push(i.ops[0]),r.ops=r.ops.concat(i.ops.slice(1))),r},f.prototype.diff=function(i,r){if(this.ops===i.ops)return new f;var u=[this,i].map(function(c){return c.map(function(O){if(O.insert!=null)return typeof O.insert=="string"?O.insert:s;var v=c===i?"on":"with";throw new Error("diff() called "+v+" non-document")}).join("")}),y=new f,g=h(u[0],u[1],r),p=a.iterator(this.ops),d=a.iterator(i.ops);return g.forEach(function(c){for(var O=c[1].length;O>0;){var v=0;switch(c[0]){case h.INSERT:v=Math.min(d.peekLength(),O),y.push(d.next(v));break;case h.DELETE:v=Math.min(O,p.peekLength()),p.next(v),y.delete(v);break;case h.EQUAL:v=Math.min(p.peekLength(),d.peekLength(),O);var E=p.next(v),T=d.next(v);t(E.insert,T.insert)?y.retain(v,a.attributes.diff(E.attributes,T.attributes)):y.push(T).delete(v);break}O-=v}}),y.chop()},f.prototype.eachLine=function(i,r){r=r||`
`;for(var u=a.iterator(this.ops),y=new f,g=0;u.hasNext();){if(u.peekType()!=="insert")return;var p=u.peek(),d=a.length(p)-u.peekLength(),c=typeof p.insert=="string"?p.insert.indexOf(r,d)-d:-1;if(c<0)y.push(u.next());else if(c>0)y.push(u.next(c));else{if(i(y,u.next(1).attributes||{},g)===!1)return;g+=1,y=new f}}y.length()>0&&i(y,{},g)},f.prototype.transform=function(i,r){if(r=!!r,typeof i=="number")return this.transformPosition(i,r);for(var u=a.iterator(this.ops),y=a.iterator(i.ops),g=new f;u.hasNext()||y.hasNext();)if(u.peekType()==="insert"&&(r||y.peekType()!=="insert"))g.retain(a.length(u.next()));else if(y.peekType()==="insert")g.push(y.next());else{var p=Math.min(u.peekLength(),y.peekLength()),d=u.next(p),c=y.next(p);if(d.delete)continue;c.delete?g.push(c):g.retain(p,a.attributes.transform(d.attributes,c.attributes,r))}return g.chop()},f.prototype.transformPosition=function(i,r){r=!!r;for(var u=a.iterator(this.ops),y=0;u.hasNext()&&y<=i;){var g=u.peekLength(),p=u.peekType();if(u.next(),p==="delete"){i-=Math.min(g,i-y);continue}else p==="insert"&&(y<i||!r)&&(i+=g);y+=g}return i},o.exports=f},function(o,n){var e=Object.prototype.hasOwnProperty,h=Object.prototype.toString,t=Object.defineProperty,l=Object.getOwnPropertyDescriptor,a=function(u){return typeof Array.isArray=="function"?Array.isArray(u):h.call(u)==="[object Array]"},s=function(u){if(!u||h.call(u)!=="[object Object]")return!1;var y=e.call(u,"constructor"),g=u.constructor&&u.constructor.prototype&&e.call(u.constructor.prototype,"isPrototypeOf");if(u.constructor&&!y&&!g)return!1;var p;for(p in u);return typeof p=="undefined"||e.call(u,p)},f=function(u,y){t&&y.name==="__proto__"?t(u,y.name,{enumerable:!0,configurable:!0,value:y.newValue,writable:!0}):u[y.name]=y.newValue},i=function(u,y){if(y==="__proto__")if(e.call(u,y)){if(l)return l(u,y).value}else return;return u[y]};o.exports=function r(){var u,y,g,p,d,c,O=arguments[0],v=1,E=arguments.length,T=!1;for(typeof O=="boolean"&&(T=O,O=arguments[1]||{},v=2),(O==null||typeof O!="object"&&typeof O!="function")&&(O={});v<E;++v)if(u=arguments[v],u!=null)for(y in u)g=i(O,y),p=i(u,y),O!==p&&(T&&p&&(s(p)||(d=a(p)))?(d?(d=!1,c=g&&a(g)?g:[]):c=g&&s(g)?g:{},f(O,{name:y,newValue:r(T,c,p)})):typeof p!="undefined"&&f(O,{name:y,newValue:p}));return O}},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=n.BlockEmbed=n.bubbleFormats=void 0;var h=function(){function S(A,N){for(var R=0;R<N.length;R++){var C=N[R];C.enumerable=C.enumerable||!1,C.configurable=!0,"value"in C&&(C.writable=!0),Object.defineProperty(A,C.key,C)}}return function(A,N,R){return N&&S(A.prototype,N),R&&S(A,R),A}}(),t=function S(A,N,R){A===null&&(A=Function.prototype);var C=Object.getOwnPropertyDescriptor(A,N);if(C===void 0){var B=Object.getPrototypeOf(A);return B===null?void 0:S(B,N,R)}else{if("value"in C)return C.value;var U=C.get;return U===void 0?void 0:U.call(R)}},l=e(3),a=O(l),s=e(2),f=O(s),i=e(0),r=O(i),u=e(16),y=O(u),g=e(6),p=O(g),d=e(7),c=O(d);function O(S){return S&&S.__esModule?S:{default:S}}function v(S,A){if(!(S instanceof A))throw new TypeError("Cannot call a class as a function")}function E(S,A){if(!S)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return A&&(typeof A=="object"||typeof A=="function")?A:S}function T(S,A){if(typeof A!="function"&&A!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof A);S.prototype=Object.create(A&&A.prototype,{constructor:{value:S,enumerable:!1,writable:!0,configurable:!0}}),A&&(Object.setPrototypeOf?Object.setPrototypeOf(S,A):S.__proto__=A)}var P=1,I=function(S){T(A,S);function A(){return v(this,A),E(this,(A.__proto__||Object.getPrototypeOf(A)).apply(this,arguments))}return h(A,[{key:"attach",value:function(){t(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"attach",this).call(this),this.attributes=new r.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return new f.default().insert(this.value(),(0,a.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(R,C){var B=r.default.query(R,r.default.Scope.BLOCK_ATTRIBUTE);B!=null&&this.attributes.attribute(B,C)}},{key:"formatAt",value:function(R,C,B,U){this.format(B,U)}},{key:"insertAt",value:function(R,C,B){if(typeof C=="string"&&C.endsWith(`
`)){var U=r.default.create(D.blotName);this.parent.insertBefore(U,R===0?this:this.next),U.insertAt(0,C.slice(0,-1))}else t(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"insertAt",this).call(this,R,C,B)}}]),A}(r.default.Embed);I.scope=r.default.Scope.BLOCK_BLOT;var D=function(S){T(A,S);function A(N){v(this,A);var R=E(this,(A.__proto__||Object.getPrototypeOf(A)).call(this,N));return R.cache={},R}return h(A,[{key:"delta",value:function(){return this.cache.delta==null&&(this.cache.delta=this.descendants(r.default.Leaf).reduce(function(R,C){return C.length()===0?R:R.insert(C.value(),w(C))},new f.default).insert(`
`,w(this))),this.cache.delta}},{key:"deleteAt",value:function(R,C){t(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"deleteAt",this).call(this,R,C),this.cache={}}},{key:"formatAt",value:function(R,C,B,U){C<=0||(r.default.query(B,r.default.Scope.BLOCK)?R+C===this.length()&&this.format(B,U):t(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"formatAt",this).call(this,R,Math.min(C,this.length()-R-1),B,U),this.cache={})}},{key:"insertAt",value:function(R,C,B){if(B!=null)return t(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"insertAt",this).call(this,R,C,B);if(C.length!==0){var U=C.split(`
`),z=U.shift();z.length>0&&(R<this.length()-1||this.children.tail==null?t(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"insertAt",this).call(this,Math.min(R,this.length()-1),z):this.children.tail.insertAt(this.children.tail.length(),z),this.cache={});var K=this;U.reduce(function(k,L){return K=K.split(k,!0),K.insertAt(0,L),L.length},R+z.length)}}},{key:"insertBefore",value:function(R,C){var B=this.children.head;t(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"insertBefore",this).call(this,R,C),B instanceof y.default&&B.remove(),this.cache={}}},{key:"length",value:function(){return this.cache.length==null&&(this.cache.length=t(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"length",this).call(this)+P),this.cache.length}},{key:"moveChildren",value:function(R,C){t(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"moveChildren",this).call(this,R,C),this.cache={}}},{key:"optimize",value:function(R){t(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"optimize",this).call(this,R),this.cache={}}},{key:"path",value:function(R){return t(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"path",this).call(this,R,!0)}},{key:"removeChild",value:function(R){t(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"removeChild",this).call(this,R),this.cache={}}},{key:"split",value:function(R){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(C&&(R===0||R>=this.length()-P)){var B=this.clone();return R===0?(this.parent.insertBefore(B,this),this):(this.parent.insertBefore(B,this.next),B)}else{var U=t(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"split",this).call(this,R,C);return this.cache={},U}}}]),A}(r.default.Block);D.blotName="block",D.tagName="P",D.defaultChild="break",D.allowedChildren=[p.default,r.default.Embed,c.default];function w(S){var A=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return S==null||(typeof S.formats=="function"&&(A=(0,a.default)(A,S.formats())),S.parent==null||S.parent.blotName=="scroll"||S.parent.statics.scope!==S.statics.scope)?A:w(S.parent,A)}n.bubbleFormats=w,n.BlockEmbed=I,n.default=D},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=n.overload=n.expandConfig=void 0;var h=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(K){return typeof K}:function(K){return K&&typeof Symbol=="function"&&K.constructor===Symbol&&K!==Symbol.prototype?"symbol":typeof K},t=function(){function K(k,L){var _=[],F=!0,Q=!1,q=void 0;try{for(var M=k[Symbol.iterator](),H;!(F=(H=M.next()).done)&&(_.push(H.value),!(L&&_.length===L));F=!0);}catch(W){Q=!0,q=W}finally{try{!F&&M.return&&M.return()}finally{if(Q)throw q}}return _}return function(k,L){if(Array.isArray(k))return k;if(Symbol.iterator in Object(k))return K(k,L);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),l=function(){function K(k,L){for(var _=0;_<L.length;_++){var F=L[_];F.enumerable=F.enumerable||!1,F.configurable=!0,"value"in F&&(F.writable=!0),Object.defineProperty(k,F.key,F)}}return function(k,L,_){return L&&K(k.prototype,L),_&&K(k,_),k}}();e(50);var a=e(2),s=w(a),f=e(14),i=w(f),r=e(8),u=w(r),y=e(9),g=w(y),p=e(0),d=w(p),c=e(15),O=w(c),v=e(3),E=w(v),T=e(10),P=w(T),I=e(34),D=w(I);function w(K){return K&&K.__esModule?K:{default:K}}function S(K,k,L){return k in K?Object.defineProperty(K,k,{value:L,enumerable:!0,configurable:!0,writable:!0}):K[k]=L,K}function A(K,k){if(!(K instanceof k))throw new TypeError("Cannot call a class as a function")}var N=(0,P.default)("quill"),R=function(){l(K,null,[{key:"debug",value:function(L){L===!0&&(L="log"),P.default.level(L)}},{key:"find",value:function(L){return L.__quill||d.default.find(L)}},{key:"import",value:function(L){return this.imports[L]==null&&N.error("Cannot import "+L+". Are you sure it was registered?"),this.imports[L]}},{key:"register",value:function(L,_){var F=this,Q=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(typeof L!="string"){var q=L.attrName||L.blotName;typeof q=="string"?this.register("formats/"+q,L,_):Object.keys(L).forEach(function(M){F.register(M,L[M],_)})}else this.imports[L]!=null&&!Q&&N.warn("Overwriting "+L+" with",_),this.imports[L]=_,(L.startsWith("blots/")||L.startsWith("formats/"))&&_.blotName!=="abstract"?d.default.register(_):L.startsWith("modules")&&typeof _.register=="function"&&_.register()}}]);function K(k){var L=this,_=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(A(this,K),this.options=C(k,_),this.container=this.options.container,this.container==null)return N.error("Invalid Quill container",k);this.options.debug&&K.debug(this.options.debug);var F=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",this.container.__quill=this,this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new u.default,this.scroll=d.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new i.default(this.scroll),this.selection=new O.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(u.default.events.EDITOR_CHANGE,function(q){q===u.default.events.TEXT_CHANGE&&L.root.classList.toggle("ql-blank",L.editor.isBlank())}),this.emitter.on(u.default.events.SCROLL_UPDATE,function(q,M){var H=L.selection.lastRange,W=H&&H.length===0?H.index:void 0;B.call(L,function(){return L.editor.update(null,M,W)},q)});var Q=this.clipboard.convert(`<div class='ql-editor' style="white-space: normal;">`+F+"<p><br></p></div>");this.setContents(Q),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}return l(K,[{key:"addContainer",value:function(L){var _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof L=="string"){var F=L;L=document.createElement("div"),L.classList.add(F)}return this.container.insertBefore(L,_),L}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(L,_,F){var Q=this,q=U(L,_,F),M=t(q,4);return L=M[0],_=M[1],F=M[3],B.call(this,function(){return Q.editor.deleteText(L,_)},F,L,-1*_)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(L),this.container.classList.toggle("ql-disabled",!L)}},{key:"focus",value:function(){var L=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=L,this.scrollIntoView()}},{key:"format",value:function(L,_){var F=this,Q=arguments.length>2&&arguments[2]!==void 0?arguments[2]:u.default.sources.API;return B.call(this,function(){var q=F.getSelection(!0),M=new s.default;if(q==null)return M;if(d.default.query(L,d.default.Scope.BLOCK))M=F.editor.formatLine(q.index,q.length,S({},L,_));else{if(q.length===0)return F.selection.format(L,_),M;M=F.editor.formatText(q.index,q.length,S({},L,_))}return F.setSelection(q,u.default.sources.SILENT),M},Q)}},{key:"formatLine",value:function(L,_,F,Q,q){var M=this,H=void 0,W=U(L,_,F,Q,q),$=t(W,4);return L=$[0],_=$[1],H=$[2],q=$[3],B.call(this,function(){return M.editor.formatLine(L,_,H)},q,L,0)}},{key:"formatText",value:function(L,_,F,Q,q){var M=this,H=void 0,W=U(L,_,F,Q,q),$=t(W,4);return L=$[0],_=$[1],H=$[2],q=$[3],B.call(this,function(){return M.editor.formatText(L,_,H)},q,L,0)}},{key:"getBounds",value:function(L){var _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,F=void 0;typeof L=="number"?F=this.selection.getBounds(L,_):F=this.selection.getBounds(L.index,L.length);var Q=this.container.getBoundingClientRect();return{bottom:F.bottom-Q.top,height:F.height,left:F.left-Q.left,right:F.right-Q.left,top:F.top-Q.top,width:F.width}}},{key:"getContents",value:function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,_=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-L,F=U(L,_),Q=t(F,2);return L=Q[0],_=Q[1],this.editor.getContents(L,_)}},{key:"getFormat",value:function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),_=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof L=="number"?this.editor.getFormat(L,_):this.editor.getFormat(L.index,L.length)}},{key:"getIndex",value:function(L){return L.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(L){return this.scroll.leaf(L)}},{key:"getLine",value:function(L){return this.scroll.line(L)}},{key:"getLines",value:function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,_=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof L!="number"?this.scroll.lines(L.index,L.length):this.scroll.lines(L,_)}},{key:"getModule",value:function(L){return this.theme.modules[L]}},{key:"getSelection",value:function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return L&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,_=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-L,F=U(L,_),Q=t(F,2);return L=Q[0],_=Q[1],this.editor.getText(L,_)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(L,_,F){var Q=this,q=arguments.length>3&&arguments[3]!==void 0?arguments[3]:K.sources.API;return B.call(this,function(){return Q.editor.insertEmbed(L,_,F)},q,L)}},{key:"insertText",value:function(L,_,F,Q,q){var M=this,H=void 0,W=U(L,0,F,Q,q),$=t(W,4);return L=$[0],H=$[2],q=$[3],B.call(this,function(){return M.editor.insertText(L,_,H)},q,L,_.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(L,_,F){this.clipboard.dangerouslyPasteHTML(L,_,F)}},{key:"removeFormat",value:function(L,_,F){var Q=this,q=U(L,_,F),M=t(q,4);return L=M[0],_=M[1],F=M[3],B.call(this,function(){return Q.editor.removeFormat(L,_)},F,L)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(L){var _=this,F=arguments.length>1&&arguments[1]!==void 0?arguments[1]:u.default.sources.API;return B.call(this,function(){L=new s.default(L);var Q=_.getLength(),q=_.editor.deleteText(0,Q),M=_.editor.applyDelta(L),H=M.ops[M.ops.length-1];H!=null&&typeof H.insert=="string"&&H.insert[H.insert.length-1]===`
`&&(_.editor.deleteText(_.getLength()-1,1),M.delete(1));var W=q.compose(M);return W},F)}},{key:"setSelection",value:function(L,_,F){if(L==null)this.selection.setRange(null,_||K.sources.API);else{var Q=U(L,_,F),q=t(Q,4);L=q[0],_=q[1],F=q[3],this.selection.setRange(new c.Range(L,_),F),F!==u.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(L){var _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:u.default.sources.API,F=new s.default().insert(L);return this.setContents(F,_)}},{key:"update",value:function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:u.default.sources.USER,_=this.scroll.update(L);return this.selection.update(L),_}},{key:"updateContents",value:function(L){var _=this,F=arguments.length>1&&arguments[1]!==void 0?arguments[1]:u.default.sources.API;return B.call(this,function(){return L=new s.default(L),_.editor.applyDelta(L,F)},F,!0)}}]),K}();R.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},R.events=u.default.events,R.sources=u.default.sources,R.version="1.3.7",R.imports={delta:s.default,parchment:d.default,"core/module":g.default,"core/theme":D.default};function C(K,k){if(k=(0,E.default)(!0,{container:K,modules:{clipboard:!0,keyboard:!0,history:!0}},k),!k.theme||k.theme===R.DEFAULTS.theme)k.theme=D.default;else if(k.theme=R.import("themes/"+k.theme),k.theme==null)throw new Error("Invalid theme "+k.theme+". Did you register it?");var L=(0,E.default)(!0,{},k.theme.DEFAULTS);[L,k].forEach(function(Q){Q.modules=Q.modules||{},Object.keys(Q.modules).forEach(function(q){Q.modules[q]===!0&&(Q.modules[q]={})})});var _=Object.keys(L.modules).concat(Object.keys(k.modules)),F=_.reduce(function(Q,q){var M=R.import("modules/"+q);return M==null?N.error("Cannot load "+q+" module. Are you sure you registered it?"):Q[q]=M.DEFAULTS||{},Q},{});return k.modules!=null&&k.modules.toolbar&&k.modules.toolbar.constructor!==Object&&(k.modules.toolbar={container:k.modules.toolbar}),k=(0,E.default)(!0,{},R.DEFAULTS,{modules:F},L,k),["bounds","container","scrollingContainer"].forEach(function(Q){typeof k[Q]=="string"&&(k[Q]=document.querySelector(k[Q]))}),k.modules=Object.keys(k.modules).reduce(function(Q,q){return k.modules[q]&&(Q[q]=k.modules[q]),Q},{}),k}function B(K,k,L,_){if(this.options.strict&&!this.isEnabled()&&k===u.default.sources.USER)return new s.default;var F=L==null?null:this.getSelection(),Q=this.editor.delta,q=K();if(F!=null&&(L===!0&&(L=F.index),_==null?F=z(F,q,k):_!==0&&(F=z(F,L,_,k)),this.setSelection(F,u.default.sources.SILENT)),q.length()>0){var M,H=[u.default.events.TEXT_CHANGE,q,Q,k];if((M=this.emitter).emit.apply(M,[u.default.events.EDITOR_CHANGE].concat(H)),k!==u.default.sources.SILENT){var W;(W=this.emitter).emit.apply(W,H)}}return q}function U(K,k,L,_,F){var Q={};return typeof K.index=="number"&&typeof K.length=="number"?typeof k!="number"?(F=_,_=L,L=k,k=K.length,K=K.index):(k=K.length,K=K.index):typeof k!="number"&&(F=_,_=L,L=k,k=0),(typeof L=="undefined"?"undefined":h(L))==="object"?(Q=L,F=_):typeof L=="string"&&(_!=null?Q[L]=_:F=L),F=F||u.default.sources.API,[K,k,Q,F]}function z(K,k,L,_){if(K==null)return null;var F=void 0,Q=void 0;if(k instanceof s.default){var q=[K.index,K.index+K.length].map(function($){return k.transformPosition($,_!==u.default.sources.USER)}),M=t(q,2);F=M[0],Q=M[1]}else{var H=[K.index,K.index+K.length].map(function($){return $<k||$===k&&_===u.default.sources.USER?$:L>=0?$+L:Math.max(k,$+L)}),W=t(H,2);F=W[0],Q=W[1]}return new c.Range(F,Q-F)}n.expandConfig=C,n.overload=U,n.default=R},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function p(d,c){for(var O=0;O<c.length;O++){var v=c[O];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(d,v.key,v)}}return function(d,c,O){return c&&p(d.prototype,c),O&&p(d,O),d}}(),t=function p(d,c,O){d===null&&(d=Function.prototype);var v=Object.getOwnPropertyDescriptor(d,c);if(v===void 0){var E=Object.getPrototypeOf(d);return E===null?void 0:p(E,c,O)}else{if("value"in v)return v.value;var T=v.get;return T===void 0?void 0:T.call(O)}},l=e(7),a=i(l),s=e(0),f=i(s);function i(p){return p&&p.__esModule?p:{default:p}}function r(p,d){if(!(p instanceof d))throw new TypeError("Cannot call a class as a function")}function u(p,d){if(!p)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return d&&(typeof d=="object"||typeof d=="function")?d:p}function y(p,d){if(typeof d!="function"&&d!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof d);p.prototype=Object.create(d&&d.prototype,{constructor:{value:p,enumerable:!1,writable:!0,configurable:!0}}),d&&(Object.setPrototypeOf?Object.setPrototypeOf(p,d):p.__proto__=d)}var g=function(p){y(d,p);function d(){return r(this,d),u(this,(d.__proto__||Object.getPrototypeOf(d)).apply(this,arguments))}return h(d,[{key:"formatAt",value:function(O,v,E,T){if(d.compare(this.statics.blotName,E)<0&&f.default.query(E,f.default.Scope.BLOT)){var P=this.isolate(O,v);T&&P.wrap(E,T)}else t(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"formatAt",this).call(this,O,v,E,T)}},{key:"optimize",value:function(O){if(t(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"optimize",this).call(this,O),this.parent instanceof d&&d.compare(this.statics.blotName,this.parent.statics.blotName)>0){var v=this.parent.isolate(this.offset(),this.length());this.moveChildren(v),v.wrap(this)}}}],[{key:"compare",value:function(O,v){var E=d.order.indexOf(O),T=d.order.indexOf(v);return E>=0||T>=0?E-T:O===v?0:O<v?-1:1}}]),d}(f.default.Inline);g.allowedChildren=[g,f.default.Embed,a.default],g.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],n.default=g},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=e(0),t=l(h);function l(r){return r&&r.__esModule?r:{default:r}}function a(r,u){if(!(r instanceof u))throw new TypeError("Cannot call a class as a function")}function s(r,u){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u&&(typeof u=="object"||typeof u=="function")?u:r}function f(r,u){if(typeof u!="function"&&u!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof u);r.prototype=Object.create(u&&u.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),u&&(Object.setPrototypeOf?Object.setPrototypeOf(r,u):r.__proto__=u)}var i=function(r){f(u,r);function u(){return a(this,u),s(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}return u}(t.default.Text);n.default=i},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function c(O,v){for(var E=0;E<v.length;E++){var T=v[E];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(O,T.key,T)}}return function(O,v,E){return v&&c(O.prototype,v),E&&c(O,E),O}}(),t=function c(O,v,E){O===null&&(O=Function.prototype);var T=Object.getOwnPropertyDescriptor(O,v);if(T===void 0){var P=Object.getPrototypeOf(O);return P===null?void 0:c(P,v,E)}else{if("value"in T)return T.value;var I=T.get;return I===void 0?void 0:I.call(E)}},l=e(54),a=i(l),s=e(10),f=i(s);function i(c){return c&&c.__esModule?c:{default:c}}function r(c,O){if(!(c instanceof O))throw new TypeError("Cannot call a class as a function")}function u(c,O){if(!c)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return O&&(typeof O=="object"||typeof O=="function")?O:c}function y(c,O){if(typeof O!="function"&&O!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof O);c.prototype=Object.create(O&&O.prototype,{constructor:{value:c,enumerable:!1,writable:!0,configurable:!0}}),O&&(Object.setPrototypeOf?Object.setPrototypeOf(c,O):c.__proto__=O)}var g=(0,f.default)("quill:events"),p=["selectionchange","mousedown","mouseup","click"];p.forEach(function(c){document.addEventListener(c,function(){for(var O=arguments.length,v=Array(O),E=0;E<O;E++)v[E]=arguments[E];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(T){if(T.__quill&&T.__quill.emitter){var P;(P=T.__quill.emitter).handleDOM.apply(P,v)}})})});var d=function(c){y(O,c);function O(){r(this,O);var v=u(this,(O.__proto__||Object.getPrototypeOf(O)).call(this));return v.listeners={},v.on("error",g.error),v}return h(O,[{key:"emit",value:function(){g.log.apply(g,arguments),t(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(E){for(var T=arguments.length,P=Array(T>1?T-1:0),I=1;I<T;I++)P[I-1]=arguments[I];(this.listeners[E.type]||[]).forEach(function(D){var w=D.node,S=D.handler;(E.target===w||w.contains(E.target))&&S.apply(void 0,[E].concat(P))})}},{key:"listenDOM",value:function(E,T,P){this.listeners[E]||(this.listeners[E]=[]),this.listeners[E].push({node:T,handler:P})}}]),O}(a.default);d.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},d.sources={API:"api",SILENT:"silent",USER:"user"},n.default=d},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});function h(l,a){if(!(l instanceof a))throw new TypeError("Cannot call a class as a function")}var t=function l(a){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};h(this,l),this.quill=a,this.options=s};t.DEFAULTS={},n.default=t},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=["error","warn","log","info"],t="warn";function l(s){if(h.indexOf(s)<=h.indexOf(t)){for(var f,i=arguments.length,r=Array(i>1?i-1:0),u=1;u<i;u++)r[u-1]=arguments[u];(f=console)[s].apply(f,r)}}function a(s){return h.reduce(function(f,i){return f[i]=l.bind(console,i,s),f},{})}l.level=a.level=function(s){t=s},n.default=a},function(o,n,e){var h=Array.prototype.slice,t=e(52),l=e(53),a=o.exports=function(r,u,y){return y||(y={}),r===u?!0:r instanceof Date&&u instanceof Date?r.getTime()===u.getTime():!r||!u||typeof r!="object"&&typeof u!="object"?y.strict?r===u:r==u:i(r,u,y)};function s(r){return r==null}function f(r){return!(!r||typeof r!="object"||typeof r.length!="number"||typeof r.copy!="function"||typeof r.slice!="function"||r.length>0&&typeof r[0]!="number")}function i(r,u,y){var g,p;if(s(r)||s(u)||r.prototype!==u.prototype)return!1;if(l(r))return l(u)?(r=h.call(r),u=h.call(u),a(r,u,y)):!1;if(f(r)){if(!f(u)||r.length!==u.length)return!1;for(g=0;g<r.length;g++)if(r[g]!==u[g])return!1;return!0}try{var d=t(r),c=t(u)}catch(O){return!1}if(d.length!=c.length)return!1;for(d.sort(),c.sort(),g=d.length-1;g>=0;g--)if(d[g]!=c[g])return!1;for(g=d.length-1;g>=0;g--)if(p=d[g],!a(r[p],u[p],y))return!1;return typeof r==typeof u}},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=e(1),t=function(){function l(a,s,f){f===void 0&&(f={}),this.attrName=a,this.keyName=s;var i=h.Scope.TYPE&h.Scope.ATTRIBUTE;f.scope!=null?this.scope=f.scope&h.Scope.LEVEL|i:this.scope=h.Scope.ATTRIBUTE,f.whitelist!=null&&(this.whitelist=f.whitelist)}return l.keys=function(a){return[].map.call(a.attributes,function(s){return s.name})},l.prototype.add=function(a,s){return this.canAdd(a,s)?(a.setAttribute(this.keyName,s),!0):!1},l.prototype.canAdd=function(a,s){var f=h.query(a,h.Scope.BLOT&(this.scope|h.Scope.TYPE));return f==null?!1:this.whitelist==null?!0:typeof s=="string"?this.whitelist.indexOf(s.replace(/["']/g,""))>-1:this.whitelist.indexOf(s)>-1},l.prototype.remove=function(a){a.removeAttribute(this.keyName)},l.prototype.value=function(a){var s=a.getAttribute(this.keyName);return this.canAdd(a,s)&&s?s:""},l}();n.default=t},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=n.Code=void 0;var h=function(){function I(D,w){var S=[],A=!0,N=!1,R=void 0;try{for(var C=D[Symbol.iterator](),B;!(A=(B=C.next()).done)&&(S.push(B.value),!(w&&S.length===w));A=!0);}catch(U){N=!0,R=U}finally{try{!A&&C.return&&C.return()}finally{if(N)throw R}}return S}return function(D,w){if(Array.isArray(D))return D;if(Symbol.iterator in Object(D))return I(D,w);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),t=function(){function I(D,w){for(var S=0;S<w.length;S++){var A=w[S];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(D,A.key,A)}}return function(D,w,S){return w&&I(D.prototype,w),S&&I(D,S),D}}(),l=function I(D,w,S){D===null&&(D=Function.prototype);var A=Object.getOwnPropertyDescriptor(D,w);if(A===void 0){var N=Object.getPrototypeOf(D);return N===null?void 0:I(N,w,S)}else{if("value"in A)return A.value;var R=A.get;return R===void 0?void 0:R.call(S)}},a=e(2),s=c(a),f=e(0),i=c(f),r=e(4),u=c(r),y=e(6),g=c(y),p=e(7),d=c(p);function c(I){return I&&I.__esModule?I:{default:I}}function O(I,D){if(!(I instanceof D))throw new TypeError("Cannot call a class as a function")}function v(I,D){if(!I)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return D&&(typeof D=="object"||typeof D=="function")?D:I}function E(I,D){if(typeof D!="function"&&D!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof D);I.prototype=Object.create(D&&D.prototype,{constructor:{value:I,enumerable:!1,writable:!0,configurable:!0}}),D&&(Object.setPrototypeOf?Object.setPrototypeOf(I,D):I.__proto__=D)}var T=function(I){E(D,I);function D(){return O(this,D),v(this,(D.__proto__||Object.getPrototypeOf(D)).apply(this,arguments))}return D}(g.default);T.blotName="code",T.tagName="CODE";var P=function(I){E(D,I);function D(){return O(this,D),v(this,(D.__proto__||Object.getPrototypeOf(D)).apply(this,arguments))}return t(D,[{key:"delta",value:function(){var S=this,A=this.domNode.textContent;return A.endsWith(`
`)&&(A=A.slice(0,-1)),A.split(`
`).reduce(function(N,R){return N.insert(R).insert(`
`,S.formats())},new s.default)}},{key:"format",value:function(S,A){if(!(S===this.statics.blotName&&A)){var N=this.descendant(d.default,this.length()-1),R=h(N,1),C=R[0];C!=null&&C.deleteAt(C.length()-1,1),l(D.prototype.__proto__||Object.getPrototypeOf(D.prototype),"format",this).call(this,S,A)}}},{key:"formatAt",value:function(S,A,N,R){if(A!==0&&!(i.default.query(N,i.default.Scope.BLOCK)==null||N===this.statics.blotName&&R===this.statics.formats(this.domNode))){var C=this.newlineIndex(S);if(!(C<0||C>=S+A)){var B=this.newlineIndex(S,!0)+1,U=C-B+1,z=this.isolate(B,U),K=z.next;z.format(N,R),K instanceof D&&K.formatAt(0,S-B+A-U,N,R)}}}},{key:"insertAt",value:function(S,A,N){if(N==null){var R=this.descendant(d.default,S),C=h(R,2),B=C[0],U=C[1];B.insertAt(U,A)}}},{key:"length",value:function(){var S=this.domNode.textContent.length;return this.domNode.textContent.endsWith(`
`)?S:S+1}},{key:"newlineIndex",value:function(S){var A=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(A)return this.domNode.textContent.slice(0,S).lastIndexOf(`
`);var N=this.domNode.textContent.slice(S).indexOf(`
`);return N>-1?S+N:-1}},{key:"optimize",value:function(S){this.domNode.textContent.endsWith(`
`)||this.appendChild(i.default.create("text",`
`)),l(D.prototype.__proto__||Object.getPrototypeOf(D.prototype),"optimize",this).call(this,S);var A=this.next;A!=null&&A.prev===this&&A.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===A.statics.formats(A.domNode)&&(A.optimize(S),A.moveChildren(this),A.remove())}},{key:"replace",value:function(S){l(D.prototype.__proto__||Object.getPrototypeOf(D.prototype),"replace",this).call(this,S),[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(A){var N=i.default.find(A);N==null?A.parentNode.removeChild(A):N instanceof i.default.Embed?N.remove():N.unwrap()})}}],[{key:"create",value:function(S){var A=l(D.__proto__||Object.getPrototypeOf(D),"create",this).call(this,S);return A.setAttribute("spellcheck",!1),A}},{key:"formats",value:function(){return!0}}]),D}(u.default);P.blotName="code-block",P.tagName="PRE",P.TAB="  ",n.Code=T,n.default=P},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(K){return typeof K}:function(K){return K&&typeof Symbol=="function"&&K.constructor===Symbol&&K!==Symbol.prototype?"symbol":typeof K},t=function(){function K(k,L){var _=[],F=!0,Q=!1,q=void 0;try{for(var M=k[Symbol.iterator](),H;!(F=(H=M.next()).done)&&(_.push(H.value),!(L&&_.length===L));F=!0);}catch(W){Q=!0,q=W}finally{try{!F&&M.return&&M.return()}finally{if(Q)throw q}}return _}return function(k,L){if(Array.isArray(k))return k;if(Symbol.iterator in Object(k))return K(k,L);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),l=function(){function K(k,L){for(var _=0;_<L.length;_++){var F=L[_];F.enumerable=F.enumerable||!1,F.configurable=!0,"value"in F&&(F.writable=!0),Object.defineProperty(k,F.key,F)}}return function(k,L,_){return L&&K(k.prototype,L),_&&K(k,_),k}}(),a=e(2),s=A(a),f=e(20),i=A(f),r=e(0),u=A(r),y=e(13),g=A(y),p=e(24),d=A(p),c=e(4),O=A(c),v=e(16),E=A(v),T=e(21),P=A(T),I=e(11),D=A(I),w=e(3),S=A(w);function A(K){return K&&K.__esModule?K:{default:K}}function N(K,k,L){return k in K?Object.defineProperty(K,k,{value:L,enumerable:!0,configurable:!0,writable:!0}):K[k]=L,K}function R(K,k){if(!(K instanceof k))throw new TypeError("Cannot call a class as a function")}var C=/^[ -~]*$/,B=function(){function K(k){R(this,K),this.scroll=k,this.delta=this.getDelta()}return l(K,[{key:"applyDelta",value:function(L){var _=this,F=!1;this.scroll.update();var Q=this.scroll.length();return this.scroll.batchStart(),L=z(L),L.reduce(function(q,M){var H=M.retain||M.delete||M.insert.length||1,W=M.attributes||{};if(M.insert!=null){if(typeof M.insert=="string"){var $=M.insert;$.endsWith(`
`)&&F&&(F=!1,$=$.slice(0,-1)),q>=Q&&!$.endsWith(`
`)&&(F=!0),_.scroll.insertAt(q,$);var et=_.scroll.line(q),ut=t(et,2),pt=ut[0],dt=ut[1],gt=(0,S.default)({},(0,c.bubbleFormats)(pt));if(pt instanceof O.default){var Nt=pt.descendant(u.default.Leaf,dt),qt=t(Nt,1),kt=qt[0];gt=(0,S.default)(gt,(0,c.bubbleFormats)(kt))}W=i.default.attributes.diff(gt,W)||{}}else if(h(M.insert)==="object"){var Z=Object.keys(M.insert)[0];if(Z==null)return q;_.scroll.insertAt(q,Z,M.insert[Z])}Q+=H}return Object.keys(W).forEach(function(tt){_.scroll.formatAt(q,H,tt,W[tt])}),q+H},0),L.reduce(function(q,M){return typeof M.delete=="number"?(_.scroll.deleteAt(q,M.delete),q):q+(M.retain||M.insert.length||1)},0),this.scroll.batchEnd(),this.update(L)}},{key:"deleteText",value:function(L,_){return this.scroll.deleteAt(L,_),this.update(new s.default().retain(L).delete(_))}},{key:"formatLine",value:function(L,_){var F=this,Q=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.scroll.update(),Object.keys(Q).forEach(function(q){if(!(F.scroll.whitelist!=null&&!F.scroll.whitelist[q])){var M=F.scroll.lines(L,Math.max(_,1)),H=_;M.forEach(function(W){var $=W.length();if(!(W instanceof g.default))W.format(q,Q[q]);else{var et=L-W.offset(F.scroll),ut=W.newlineIndex(et+H)-et+1;W.formatAt(et,ut,q,Q[q])}H-=$})}}),this.scroll.optimize(),this.update(new s.default().retain(L).retain(_,(0,P.default)(Q)))}},{key:"formatText",value:function(L,_){var F=this,Q=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return Object.keys(Q).forEach(function(q){F.scroll.formatAt(L,_,q,Q[q])}),this.update(new s.default().retain(L).retain(_,(0,P.default)(Q)))}},{key:"getContents",value:function(L,_){return this.delta.slice(L,L+_)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(L,_){return L.concat(_.delta())},new s.default)}},{key:"getFormat",value:function(L){var _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,F=[],Q=[];_===0?this.scroll.path(L).forEach(function(M){var H=t(M,1),W=H[0];W instanceof O.default?F.push(W):W instanceof u.default.Leaf&&Q.push(W)}):(F=this.scroll.lines(L,_),Q=this.scroll.descendants(u.default.Leaf,L,_));var q=[F,Q].map(function(M){if(M.length===0)return{};for(var H=(0,c.bubbleFormats)(M.shift());Object.keys(H).length>0;){var W=M.shift();if(W==null)return H;H=U((0,c.bubbleFormats)(W),H)}return H});return S.default.apply(S.default,q)}},{key:"getText",value:function(L,_){return this.getContents(L,_).filter(function(F){return typeof F.insert=="string"}).map(function(F){return F.insert}).join("")}},{key:"insertEmbed",value:function(L,_,F){return this.scroll.insertAt(L,_,F),this.update(new s.default().retain(L).insert(N({},_,F)))}},{key:"insertText",value:function(L,_){var F=this,Q=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return _=_.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(L,_),Object.keys(Q).forEach(function(q){F.scroll.formatAt(L,_.length,q,Q[q])}),this.update(new s.default().retain(L).insert(_,(0,P.default)(Q)))}},{key:"isBlank",value:function(){if(this.scroll.children.length==0)return!0;if(this.scroll.children.length>1)return!1;var L=this.scroll.children.head;return L.statics.blotName!==O.default.blotName||L.children.length>1?!1:L.children.head instanceof E.default}},{key:"removeFormat",value:function(L,_){var F=this.getText(L,_),Q=this.scroll.line(L+_),q=t(Q,2),M=q[0],H=q[1],W=0,$=new s.default;M!=null&&(M instanceof g.default?W=M.newlineIndex(H)-H+1:W=M.length()-H,$=M.delta().slice(H,H+W-1).insert(`
`));var et=this.getContents(L,_+W),ut=et.diff(new s.default().insert(F).concat($)),pt=new s.default().retain(L).concat(ut);return this.applyDelta(pt)}},{key:"update",value:function(L){var _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],F=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,Q=this.delta;if(_.length===1&&_[0].type==="characterData"&&_[0].target.data.match(C)&&u.default.find(_[0].target)){var q=u.default.find(_[0].target),M=(0,c.bubbleFormats)(q),H=q.offset(this.scroll),W=_[0].oldValue.replace(d.default.CONTENTS,""),$=new s.default().insert(W),et=new s.default().insert(q.value()),ut=new s.default().retain(H).concat($.diff(et,F));L=ut.reduce(function(pt,dt){return dt.insert?pt.insert(dt.insert,M):pt.push(dt)},new s.default),this.delta=Q.compose(L)}else this.delta=this.getDelta(),(!L||!(0,D.default)(Q.compose(L),this.delta))&&(L=Q.diff(this.delta,F));return L}}]),K}();function U(K,k){return Object.keys(k).reduce(function(L,_){return K[_]==null||(k[_]===K[_]?L[_]=k[_]:Array.isArray(k[_])?k[_].indexOf(K[_])<0&&(L[_]=k[_].concat([K[_]])):L[_]=[k[_],K[_]]),L},{})}function z(K){return K.reduce(function(k,L){if(L.insert===1){var _=(0,P.default)(L.attributes);return delete _.image,k.insert({image:L.attributes.image},_)}if(L.attributes!=null&&(L.attributes.list===!0||L.attributes.bullet===!0)&&(L=(0,P.default)(L),L.attributes.list?L.attributes.list="ordered":(L.attributes.list="bullet",delete L.attributes.bullet)),typeof L.insert=="string"){var F=L.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return k.insert(F,L.attributes)}return k.push(L)},new s.default)}n.default=B},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=n.Range=void 0;var h=function(){function I(D,w){var S=[],A=!0,N=!1,R=void 0;try{for(var C=D[Symbol.iterator](),B;!(A=(B=C.next()).done)&&(S.push(B.value),!(w&&S.length===w));A=!0);}catch(U){N=!0,R=U}finally{try{!A&&C.return&&C.return()}finally{if(N)throw R}}return S}return function(D,w){if(Array.isArray(D))return D;if(Symbol.iterator in Object(D))return I(D,w);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),t=function(){function I(D,w){for(var S=0;S<w.length;S++){var A=w[S];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(D,A.key,A)}}return function(D,w,S){return w&&I(D.prototype,w),S&&I(D,S),D}}(),l=e(0),a=d(l),s=e(21),f=d(s),i=e(11),r=d(i),u=e(8),y=d(u),g=e(10),p=d(g);function d(I){return I&&I.__esModule?I:{default:I}}function c(I){if(Array.isArray(I)){for(var D=0,w=Array(I.length);D<I.length;D++)w[D]=I[D];return w}else return Array.from(I)}function O(I,D){if(!(I instanceof D))throw new TypeError("Cannot call a class as a function")}var v=(0,p.default)("quill:selection"),E=function I(D){var w=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;O(this,I),this.index=D,this.length=w},T=function(){function I(D,w){var S=this;O(this,I),this.emitter=w,this.scroll=D,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=a.default.create("cursor",this),this.lastRange=this.savedRange=new E(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,function(){S.mouseDown||setTimeout(S.update.bind(S,y.default.sources.USER),1)}),this.emitter.on(y.default.events.EDITOR_CHANGE,function(A,N){A===y.default.events.TEXT_CHANGE&&N.length()>0&&S.update(y.default.sources.SILENT)}),this.emitter.on(y.default.events.SCROLL_BEFORE_UPDATE,function(){if(S.hasFocus()){var A=S.getNativeRange();A!=null&&A.start.node!==S.cursor.textNode&&S.emitter.once(y.default.events.SCROLL_UPDATE,function(){try{S.setNativeRange(A.start.node,A.start.offset,A.end.node,A.end.offset)}catch(N){}})}}),this.emitter.on(y.default.events.SCROLL_OPTIMIZE,function(A,N){if(N.range){var R=N.range,C=R.startNode,B=R.startOffset,U=R.endNode,z=R.endOffset;S.setNativeRange(C,B,U,z)}}),this.update(y.default.sources.SILENT)}return t(I,[{key:"handleComposition",value:function(){var w=this;this.root.addEventListener("compositionstart",function(){w.composing=!0}),this.root.addEventListener("compositionend",function(){if(w.composing=!1,w.cursor.parent){var S=w.cursor.restore();if(!S)return;setTimeout(function(){w.setNativeRange(S.startNode,S.startOffset,S.endNode,S.endOffset)},1)}})}},{key:"handleDragging",value:function(){var w=this;this.emitter.listenDOM("mousedown",document.body,function(){w.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,function(){w.mouseDown=!1,w.update(y.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(w,S){if(!(this.scroll.whitelist!=null&&!this.scroll.whitelist[w])){this.scroll.update();var A=this.getNativeRange();if(!(A==null||!A.native.collapsed||a.default.query(w,a.default.Scope.BLOCK))){if(A.start.node!==this.cursor.textNode){var N=a.default.find(A.start.node,!1);if(N==null)return;if(N instanceof a.default.Leaf){var R=N.split(A.start.offset);N.parent.insertBefore(this.cursor,R)}else N.insertBefore(this.cursor,A.start.node);this.cursor.attach()}this.cursor.format(w,S),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(w){var S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,A=this.scroll.length();w=Math.min(w,A-1),S=Math.min(w+S,A-1)-w;var N=void 0,R=this.scroll.leaf(w),C=h(R,2),B=C[0],U=C[1];if(B==null)return null;var z=B.position(U,!0),K=h(z,2);N=K[0],U=K[1];var k=document.createRange();if(S>0){k.setStart(N,U);var L=this.scroll.leaf(w+S),_=h(L,2);if(B=_[0],U=_[1],B==null)return null;var F=B.position(U,!0),Q=h(F,2);return N=Q[0],U=Q[1],k.setEnd(N,U),k.getBoundingClientRect()}else{var q="left",M=void 0;return N instanceof Text?(U<N.data.length?(k.setStart(N,U),k.setEnd(N,U+1)):(k.setStart(N,U-1),k.setEnd(N,U),q="right"),M=k.getBoundingClientRect()):(M=B.domNode.getBoundingClientRect(),U>0&&(q="right")),{bottom:M.top+M.height,height:M.height,left:M[q],right:M[q],top:M.top,width:0}}}},{key:"getNativeRange",value:function(){var w=document.getSelection();if(w==null||w.rangeCount<=0)return null;var S=w.getRangeAt(0);if(S==null)return null;var A=this.normalizeNative(S);return v.info("getNativeRange",A),A}},{key:"getRange",value:function(){var w=this.getNativeRange();if(w==null)return[null,null];var S=this.normalizedToRange(w);return[S,w]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(w){var S=this,A=[[w.start.node,w.start.offset]];w.native.collapsed||A.push([w.end.node,w.end.offset]);var N=A.map(function(B){var U=h(B,2),z=U[0],K=U[1],k=a.default.find(z,!0),L=k.offset(S.scroll);return K===0?L:k instanceof a.default.Container?L+k.length():L+k.index(z,K)}),R=Math.min(Math.max.apply(Math,c(N)),this.scroll.length()-1),C=Math.min.apply(Math,[R].concat(c(N)));return new E(C,R-C)}},{key:"normalizeNative",value:function(w){if(!P(this.root,w.startContainer)||!w.collapsed&&!P(this.root,w.endContainer))return null;var S={start:{node:w.startContainer,offset:w.startOffset},end:{node:w.endContainer,offset:w.endOffset},native:w};return[S.start,S.end].forEach(function(A){for(var N=A.node,R=A.offset;!(N instanceof Text)&&N.childNodes.length>0;)if(N.childNodes.length>R)N=N.childNodes[R],R=0;else if(N.childNodes.length===R)N=N.lastChild,R=N instanceof Text?N.data.length:N.childNodes.length+1;else break;A.node=N,A.offset=R}),S}},{key:"rangeToNative",value:function(w){var S=this,A=w.collapsed?[w.index]:[w.index,w.index+w.length],N=[],R=this.scroll.length();return A.forEach(function(C,B){C=Math.min(R-1,C);var U=void 0,z=S.scroll.leaf(C),K=h(z,2),k=K[0],L=K[1],_=k.position(L,B!==0),F=h(_,2);U=F[0],L=F[1],N.push(U,L)}),N.length<2&&(N=N.concat(N)),N}},{key:"scrollIntoView",value:function(w){var S=this.lastRange;if(S!=null){var A=this.getBounds(S.index,S.length);if(A!=null){var N=this.scroll.length()-1,R=this.scroll.line(Math.min(S.index,N)),C=h(R,1),B=C[0],U=B;if(S.length>0){var z=this.scroll.line(Math.min(S.index+S.length,N)),K=h(z,1);U=K[0]}if(!(B==null||U==null)){var k=w.getBoundingClientRect();A.top<k.top?w.scrollTop-=k.top-A.top:A.bottom>k.bottom&&(w.scrollTop+=A.bottom-k.bottom)}}}}},{key:"setNativeRange",value:function(w,S){var A=arguments.length>2&&arguments[2]!==void 0?arguments[2]:w,N=arguments.length>3&&arguments[3]!==void 0?arguments[3]:S,R=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(v.info("setNativeRange",w,S,A,N),!(w!=null&&(this.root.parentNode==null||w.parentNode==null||A.parentNode==null))){var C=document.getSelection();if(C!=null)if(w!=null){this.hasFocus()||this.root.focus();var B=(this.getNativeRange()||{}).native;if(B==null||R||w!==B.startContainer||S!==B.startOffset||A!==B.endContainer||N!==B.endOffset){w.tagName=="BR"&&(S=[].indexOf.call(w.parentNode.childNodes,w),w=w.parentNode),A.tagName=="BR"&&(N=[].indexOf.call(A.parentNode.childNodes,A),A=A.parentNode);var U=document.createRange();U.setStart(w,S),U.setEnd(A,N),C.removeAllRanges(),C.addRange(U)}}else C.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(w){var S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,A=arguments.length>2&&arguments[2]!==void 0?arguments[2]:y.default.sources.API;if(typeof S=="string"&&(A=S,S=!1),v.info("setRange",w),w!=null){var N=this.rangeToNative(w);this.setNativeRange.apply(this,c(N).concat([S]))}else this.setNativeRange(null);this.update(A)}},{key:"update",value:function(){var w=arguments.length>0&&arguments[0]!==void 0?arguments[0]:y.default.sources.USER,S=this.lastRange,A=this.getRange(),N=h(A,2),R=N[0],C=N[1];if(this.lastRange=R,this.lastRange!=null&&(this.savedRange=this.lastRange),!(0,r.default)(S,this.lastRange)){var B;!this.composing&&C!=null&&C.native.collapsed&&C.start.node!==this.cursor.textNode&&this.cursor.restore();var U=[y.default.events.SELECTION_CHANGE,(0,f.default)(this.lastRange),(0,f.default)(S),w];if((B=this.emitter).emit.apply(B,[y.default.events.EDITOR_CHANGE].concat(U)),w!==y.default.sources.SILENT){var z;(z=this.emitter).emit.apply(z,U)}}}}]),I}();function P(I,D){try{D.parentNode}catch(w){return!1}return D instanceof Text&&(D=D.parentNode),I.contains(D)}n.Range=E,n.default=T},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function y(g,p){for(var d=0;d<p.length;d++){var c=p[d];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(g,c.key,c)}}return function(g,p,d){return p&&y(g.prototype,p),d&&y(g,d),g}}(),t=function y(g,p,d){g===null&&(g=Function.prototype);var c=Object.getOwnPropertyDescriptor(g,p);if(c===void 0){var O=Object.getPrototypeOf(g);return O===null?void 0:y(O,p,d)}else{if("value"in c)return c.value;var v=c.get;return v===void 0?void 0:v.call(d)}},l=e(0),a=s(l);function s(y){return y&&y.__esModule?y:{default:y}}function f(y,g){if(!(y instanceof g))throw new TypeError("Cannot call a class as a function")}function i(y,g){if(!y)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g&&(typeof g=="object"||typeof g=="function")?g:y}function r(y,g){if(typeof g!="function"&&g!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof g);y.prototype=Object.create(g&&g.prototype,{constructor:{value:y,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(y,g):y.__proto__=g)}var u=function(y){r(g,y);function g(){return f(this,g),i(this,(g.__proto__||Object.getPrototypeOf(g)).apply(this,arguments))}return h(g,[{key:"insertInto",value:function(d,c){d.children.length===0?t(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"insertInto",this).call(this,d,c):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),g}(a.default.Embed);u.blotName="break",u.tagName="BR",n.default=u},function(o,n,e){var h=this&&this.__extends||function(){var i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,u){r.__proto__=u}||function(r,u){for(var y in u)u.hasOwnProperty(y)&&(r[y]=u[y])};return function(r,u){i(r,u);function y(){this.constructor=r}r.prototype=u===null?Object.create(u):(y.prototype=u.prototype,new y)}}();Object.defineProperty(n,"__esModule",{value:!0});var t=e(44),l=e(30),a=e(1),s=function(i){h(r,i);function r(u){var y=i.call(this,u)||this;return y.build(),y}return r.prototype.appendChild=function(u){this.insertBefore(u)},r.prototype.attach=function(){i.prototype.attach.call(this),this.children.forEach(function(u){u.attach()})},r.prototype.build=function(){var u=this;this.children=new t.default,[].slice.call(this.domNode.childNodes).reverse().forEach(function(y){try{var g=f(y);u.insertBefore(g,u.children.head||void 0)}catch(p){if(p instanceof a.ParchmentError)return;throw p}})},r.prototype.deleteAt=function(u,y){if(u===0&&y===this.length())return this.remove();this.children.forEachAt(u,y,function(g,p,d){g.deleteAt(p,d)})},r.prototype.descendant=function(u,y){var g=this.children.find(y),p=g[0],d=g[1];return u.blotName==null&&u(p)||u.blotName!=null&&p instanceof u?[p,d]:p instanceof r?p.descendant(u,d):[null,-1]},r.prototype.descendants=function(u,y,g){y===void 0&&(y=0),g===void 0&&(g=Number.MAX_VALUE);var p=[],d=g;return this.children.forEachAt(y,g,function(c,O,v){(u.blotName==null&&u(c)||u.blotName!=null&&c instanceof u)&&p.push(c),c instanceof r&&(p=p.concat(c.descendants(u,O,d))),d-=v}),p},r.prototype.detach=function(){this.children.forEach(function(u){u.detach()}),i.prototype.detach.call(this)},r.prototype.formatAt=function(u,y,g,p){this.children.forEachAt(u,y,function(d,c,O){d.formatAt(c,O,g,p)})},r.prototype.insertAt=function(u,y,g){var p=this.children.find(u),d=p[0],c=p[1];if(d)d.insertAt(c,y,g);else{var O=g==null?a.create("text",y):a.create(y,g);this.appendChild(O)}},r.prototype.insertBefore=function(u,y){if(this.statics.allowedChildren!=null&&!this.statics.allowedChildren.some(function(g){return u instanceof g}))throw new a.ParchmentError("Cannot insert "+u.statics.blotName+" into "+this.statics.blotName);u.insertInto(this,y)},r.prototype.length=function(){return this.children.reduce(function(u,y){return u+y.length()},0)},r.prototype.moveChildren=function(u,y){this.children.forEach(function(g){u.insertBefore(g,y)})},r.prototype.optimize=function(u){if(i.prototype.optimize.call(this,u),this.children.length===0)if(this.statics.defaultChild!=null){var y=a.create(this.statics.defaultChild);this.appendChild(y),y.optimize(u)}else this.remove()},r.prototype.path=function(u,y){y===void 0&&(y=!1);var g=this.children.find(u,y),p=g[0],d=g[1],c=[[this,u]];return p instanceof r?c.concat(p.path(d,y)):(p!=null&&c.push([p,d]),c)},r.prototype.removeChild=function(u){this.children.remove(u)},r.prototype.replace=function(u){u instanceof r&&u.moveChildren(this),i.prototype.replace.call(this,u)},r.prototype.split=function(u,y){if(y===void 0&&(y=!1),!y){if(u===0)return this;if(u===this.length())return this.next}var g=this.clone();return this.parent.insertBefore(g,this.next),this.children.forEachAt(u,this.length(),function(p,d,c){p=p.split(d,y),g.appendChild(p)}),g},r.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},r.prototype.update=function(u,y){var g=this,p=[],d=[];u.forEach(function(c){c.target===g.domNode&&c.type==="childList"&&(p.push.apply(p,c.addedNodes),d.push.apply(d,c.removedNodes))}),d.forEach(function(c){if(!(c.parentNode!=null&&c.tagName!=="IFRAME"&&document.body.compareDocumentPosition(c)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var O=a.find(c);O!=null&&(O.domNode.parentNode==null||O.domNode.parentNode===g.domNode)&&O.detach()}}),p.filter(function(c){return c.parentNode==g.domNode}).sort(function(c,O){return c===O?0:c.compareDocumentPosition(O)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(c){var O=null;c.nextSibling!=null&&(O=a.find(c.nextSibling));var v=f(c);(v.next!=O||v.next==null)&&(v.parent!=null&&v.parent.removeChild(g),g.insertBefore(v,O||void 0))})},r}(l.default);function f(i){var r=a.find(i);if(r==null)try{r=a.create(i)}catch(u){r=a.create(a.Scope.INLINE),[].slice.call(i.childNodes).forEach(function(y){r.domNode.appendChild(y)}),i.parentNode&&i.parentNode.replaceChild(r.domNode,i),r.attach()}return r}n.default=s},function(o,n,e){var h=this&&this.__extends||function(){var i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,u){r.__proto__=u}||function(r,u){for(var y in u)u.hasOwnProperty(y)&&(r[y]=u[y])};return function(r,u){i(r,u);function y(){this.constructor=r}r.prototype=u===null?Object.create(u):(y.prototype=u.prototype,new y)}}();Object.defineProperty(n,"__esModule",{value:!0});var t=e(12),l=e(31),a=e(17),s=e(1),f=function(i){h(r,i);function r(u){var y=i.call(this,u)||this;return y.attributes=new l.default(y.domNode),y}return r.formats=function(u){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return u.tagName.toLowerCase()},r.prototype.format=function(u,y){var g=s.query(u);g instanceof t.default?this.attributes.attribute(g,y):y&&g!=null&&(u!==this.statics.blotName||this.formats()[u]!==y)&&this.replaceWith(u,y)},r.prototype.formats=function(){var u=this.attributes.values(),y=this.statics.formats(this.domNode);return y!=null&&(u[this.statics.blotName]=y),u},r.prototype.replaceWith=function(u,y){var g=i.prototype.replaceWith.call(this,u,y);return this.attributes.copy(g),g},r.prototype.update=function(u,y){var g=this;i.prototype.update.call(this,u,y),u.some(function(p){return p.target===g.domNode&&p.type==="attributes"})&&this.attributes.build()},r.prototype.wrap=function(u,y){var g=i.prototype.wrap.call(this,u,y);return g instanceof r&&g.statics.scope===this.statics.scope&&this.attributes.move(g),g},r}(a.default);n.default=f},function(o,n,e){var h=this&&this.__extends||function(){var s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,i){f.__proto__=i}||function(f,i){for(var r in i)i.hasOwnProperty(r)&&(f[r]=i[r])};return function(f,i){s(f,i);function r(){this.constructor=f}f.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var t=e(30),l=e(1),a=function(s){h(f,s);function f(){return s!==null&&s.apply(this,arguments)||this}return f.value=function(i){return!0},f.prototype.index=function(i,r){return this.domNode===i||this.domNode.compareDocumentPosition(i)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(r,1):-1},f.prototype.position=function(i,r){var u=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return i>0&&(u+=1),[this.parent.domNode,u]},f.prototype.value=function(){var i;return i={},i[this.statics.blotName]=this.statics.value(this.domNode)||!0,i},f.scope=l.Scope.INLINE_BLOT,f}(t.default);n.default=a},function(o,n,e){var h=e(11),t=e(3),l={attributes:{compose:function(s,f,i){typeof s!="object"&&(s={}),typeof f!="object"&&(f={});var r=t(!0,{},f);i||(r=Object.keys(r).reduce(function(y,g){return r[g]!=null&&(y[g]=r[g]),y},{}));for(var u in s)s[u]!==void 0&&f[u]===void 0&&(r[u]=s[u]);return Object.keys(r).length>0?r:void 0},diff:function(s,f){typeof s!="object"&&(s={}),typeof f!="object"&&(f={});var i=Object.keys(s).concat(Object.keys(f)).reduce(function(r,u){return h(s[u],f[u])||(r[u]=f[u]===void 0?null:f[u]),r},{});return Object.keys(i).length>0?i:void 0},transform:function(s,f,i){if(typeof s!="object")return f;if(typeof f=="object"){if(!i)return f;var r=Object.keys(f).reduce(function(u,y){return s[y]===void 0&&(u[y]=f[y]),u},{});return Object.keys(r).length>0?r:void 0}}},iterator:function(s){return new a(s)},length:function(s){return typeof s.delete=="number"?s.delete:typeof s.retain=="number"?s.retain:typeof s.insert=="string"?s.insert.length:1}};function a(s){this.ops=s,this.index=0,this.offset=0}a.prototype.hasNext=function(){return this.peekLength()<1/0},a.prototype.next=function(s){s||(s=1/0);var f=this.ops[this.index];if(f){var i=this.offset,r=l.length(f);if(s>=r-i?(s=r-i,this.index+=1,this.offset=0):this.offset+=s,typeof f.delete=="number")return{delete:s};var u={};return f.attributes&&(u.attributes=f.attributes),typeof f.retain=="number"?u.retain=s:typeof f.insert=="string"?u.insert=f.insert.substr(i,s):u.insert=f.insert,u}else return{retain:1/0}},a.prototype.peek=function(){return this.ops[this.index]},a.prototype.peekLength=function(){return this.ops[this.index]?l.length(this.ops[this.index])-this.offset:1/0},a.prototype.peekType=function(){return this.ops[this.index]?typeof this.ops[this.index].delete=="number"?"delete":typeof this.ops[this.index].retain=="number"?"retain":"insert":"retain"},a.prototype.rest=function(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);var s=this.offset,f=this.index,i=this.next(),r=this.ops.slice(this.index);return this.offset=s,this.index=f,[i].concat(r)}else return[]},o.exports=l},function(o,n){var e=function(){function h(g,p){return p!=null&&g instanceof p}var t;try{t=Map}catch(g){t=function(){}}var l;try{l=Set}catch(g){l=function(){}}var a;try{a=Promise}catch(g){a=function(){}}function s(g,p,d,c,O){typeof p=="object"&&(d=p.depth,c=p.prototype,O=p.includeNonEnumerable,p=p.circular);var v=[],E=[],T=typeof Buffer!="undefined";typeof p=="undefined"&&(p=!0),typeof d=="undefined"&&(d=1/0);function P(I,D){if(I===null)return null;if(D===0)return I;var w,S;if(typeof I!="object")return I;if(h(I,t))w=new t;else if(h(I,l))w=new l;else if(h(I,a))w=new a(function(k,L){I.then(function(_){k(P(_,D-1))},function(_){L(P(_,D-1))})});else if(s.__isArray(I))w=[];else if(s.__isRegExp(I))w=new RegExp(I.source,y(I)),I.lastIndex&&(w.lastIndex=I.lastIndex);else if(s.__isDate(I))w=new Date(I.getTime());else{if(T&&Buffer.isBuffer(I))return Buffer.allocUnsafe?w=Buffer.allocUnsafe(I.length):w=new Buffer(I.length),I.copy(w),w;h(I,Error)?w=Object.create(I):typeof c=="undefined"?(S=Object.getPrototypeOf(I),w=Object.create(S)):(w=Object.create(c),S=c)}if(p){var A=v.indexOf(I);if(A!=-1)return E[A];v.push(I),E.push(w)}h(I,t)&&I.forEach(function(k,L){var _=P(L,D-1),F=P(k,D-1);w.set(_,F)}),h(I,l)&&I.forEach(function(k){var L=P(k,D-1);w.add(L)});for(var N in I){var R;S&&(R=Object.getOwnPropertyDescriptor(S,N)),!(R&&R.set==null)&&(w[N]=P(I[N],D-1))}if(Object.getOwnPropertySymbols)for(var C=Object.getOwnPropertySymbols(I),N=0;N<C.length;N++){var B=C[N],U=Object.getOwnPropertyDescriptor(I,B);U&&!U.enumerable&&!O||(w[B]=P(I[B],D-1),U.enumerable||Object.defineProperty(w,B,{enumerable:!1}))}if(O)for(var z=Object.getOwnPropertyNames(I),N=0;N<z.length;N++){var K=z[N],U=Object.getOwnPropertyDescriptor(I,K);U&&U.enumerable||(w[K]=P(I[K],D-1),Object.defineProperty(w,K,{enumerable:!1}))}return w}return P(g,d)}s.clonePrototype=function(p){if(p===null)return null;var d=function(){};return d.prototype=p,new d};function f(g){return Object.prototype.toString.call(g)}s.__objToStr=f;function i(g){return typeof g=="object"&&f(g)==="[object Date]"}s.__isDate=i;function r(g){return typeof g=="object"&&f(g)==="[object Array]"}s.__isArray=r;function u(g){return typeof g=="object"&&f(g)==="[object RegExp]"}s.__isRegExp=u;function y(g){var p="";return g.global&&(p+="g"),g.ignoreCase&&(p+="i"),g.multiline&&(p+="m"),p}return s.__getRegExpFlags=y,s}();typeof o=="object"&&o.exports&&(o.exports=e)},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function w(S,A){var N=[],R=!0,C=!1,B=void 0;try{for(var U=S[Symbol.iterator](),z;!(R=(z=U.next()).done)&&(N.push(z.value),!(A&&N.length===A));R=!0);}catch(K){C=!0,B=K}finally{try{!R&&U.return&&U.return()}finally{if(C)throw B}}return N}return function(S,A){if(Array.isArray(S))return S;if(Symbol.iterator in Object(S))return w(S,A);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),t=function(){function w(S,A){for(var N=0;N<A.length;N++){var R=A[N];R.enumerable=R.enumerable||!1,R.configurable=!0,"value"in R&&(R.writable=!0),Object.defineProperty(S,R.key,R)}}return function(S,A,N){return A&&w(S.prototype,A),N&&w(S,N),S}}(),l=function w(S,A,N){S===null&&(S=Function.prototype);var R=Object.getOwnPropertyDescriptor(S,A);if(R===void 0){var C=Object.getPrototypeOf(S);return C===null?void 0:w(C,A,N)}else{if("value"in R)return R.value;var B=R.get;return B===void 0?void 0:B.call(N)}},a=e(0),s=v(a),f=e(8),i=v(f),r=e(4),u=v(r),y=e(16),g=v(y),p=e(13),d=v(p),c=e(25),O=v(c);function v(w){return w&&w.__esModule?w:{default:w}}function E(w,S){if(!(w instanceof S))throw new TypeError("Cannot call a class as a function")}function T(w,S){if(!w)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return S&&(typeof S=="object"||typeof S=="function")?S:w}function P(w,S){if(typeof S!="function"&&S!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof S);w.prototype=Object.create(S&&S.prototype,{constructor:{value:w,enumerable:!1,writable:!0,configurable:!0}}),S&&(Object.setPrototypeOf?Object.setPrototypeOf(w,S):w.__proto__=S)}function I(w){return w instanceof u.default||w instanceof r.BlockEmbed}var D=function(w){P(S,w);function S(A,N){E(this,S);var R=T(this,(S.__proto__||Object.getPrototypeOf(S)).call(this,A));return R.emitter=N.emitter,Array.isArray(N.whitelist)&&(R.whitelist=N.whitelist.reduce(function(C,B){return C[B]=!0,C},{})),R.domNode.addEventListener("DOMNodeInserted",function(){}),R.optimize(),R.enable(),R}return t(S,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(N,R){var C=this.line(N),B=h(C,2),U=B[0],z=B[1],K=this.line(N+R),k=h(K,1),L=k[0];if(l(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"deleteAt",this).call(this,N,R),L!=null&&U!==L&&z>0){if(U instanceof r.BlockEmbed||L instanceof r.BlockEmbed){this.optimize();return}if(U instanceof d.default){var _=U.newlineIndex(U.length(),!0);if(_>-1&&(U=U.split(_+1),U===L)){this.optimize();return}}else if(L instanceof d.default){var F=L.newlineIndex(0);F>-1&&L.split(F+1)}var Q=L.children.head instanceof g.default?null:L.children.head;U.moveChildren(L,Q),U.remove()}this.optimize()}},{key:"enable",value:function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",N)}},{key:"formatAt",value:function(N,R,C,B){this.whitelist!=null&&!this.whitelist[C]||(l(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"formatAt",this).call(this,N,R,C,B),this.optimize())}},{key:"insertAt",value:function(N,R,C){if(!(C!=null&&this.whitelist!=null&&!this.whitelist[R])){if(N>=this.length())if(C==null||s.default.query(R,s.default.Scope.BLOCK)==null){var B=s.default.create(this.statics.defaultChild);this.appendChild(B),C==null&&R.endsWith(`
`)&&(R=R.slice(0,-1)),B.insertAt(0,R,C)}else{var U=s.default.create(R,C);this.appendChild(U)}else l(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"insertAt",this).call(this,N,R,C);this.optimize()}}},{key:"insertBefore",value:function(N,R){if(N.statics.scope===s.default.Scope.INLINE_BLOT){var C=s.default.create(this.statics.defaultChild);C.appendChild(N),N=C}l(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"insertBefore",this).call(this,N,R)}},{key:"leaf",value:function(N){return this.path(N).pop()||[null,-1]}},{key:"line",value:function(N){return N===this.length()?this.line(N-1):this.descendant(I,N)}},{key:"lines",value:function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE,C=function B(U,z,K){var k=[],L=K;return U.children.forEachAt(z,K,function(_,F,Q){I(_)?k.push(_):_ instanceof s.default.Container&&(k=k.concat(B(_,F,L))),L-=Q}),k};return C(this,N,R)}},{key:"optimize",value:function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch!==!0&&(l(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"optimize",this).call(this,N,R),N.length>0&&this.emitter.emit(i.default.events.SCROLL_OPTIMIZE,N,R))}},{key:"path",value:function(N){return l(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"path",this).call(this,N).slice(1)}},{key:"update",value:function(N){if(this.batch!==!0){var R=i.default.sources.USER;typeof N=="string"&&(R=N),Array.isArray(N)||(N=this.observer.takeRecords()),N.length>0&&this.emitter.emit(i.default.events.SCROLL_BEFORE_UPDATE,R,N),l(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"update",this).call(this,N.concat([])),N.length>0&&this.emitter.emit(i.default.events.SCROLL_UPDATE,R,N)}}}]),S}(s.default.Scroll);D.blotName="scroll",D.className="ql-editor",D.tagName="DIV",D.defaultChild="block",D.allowedChildren=[u.default,r.BlockEmbed,O.default],n.default=D},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.SHORTKEY=n.default=void 0;var h=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(M){return typeof M}:function(M){return M&&typeof Symbol=="function"&&M.constructor===Symbol&&M!==Symbol.prototype?"symbol":typeof M},t=function(){function M(H,W){var $=[],et=!0,ut=!1,pt=void 0;try{for(var dt=H[Symbol.iterator](),gt;!(et=(gt=dt.next()).done)&&($.push(gt.value),!(W&&$.length===W));et=!0);}catch(Nt){ut=!0,pt=Nt}finally{try{!et&&dt.return&&dt.return()}finally{if(ut)throw pt}}return $}return function(H,W){if(Array.isArray(H))return H;if(Symbol.iterator in Object(H))return M(H,W);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),l=function(){function M(H,W){for(var $=0;$<W.length;$++){var et=W[$];et.enumerable=et.enumerable||!1,et.configurable=!0,"value"in et&&(et.writable=!0),Object.defineProperty(H,et.key,et)}}return function(H,W,$){return W&&M(H.prototype,W),$&&M(H,$),H}}(),a=e(21),s=w(a),f=e(11),i=w(f),r=e(3),u=w(r),y=e(2),g=w(y),p=e(20),d=w(p),c=e(0),O=w(c),v=e(5),E=w(v),T=e(10),P=w(T),I=e(9),D=w(I);function w(M){return M&&M.__esModule?M:{default:M}}function S(M,H,W){return H in M?Object.defineProperty(M,H,{value:W,enumerable:!0,configurable:!0,writable:!0}):M[H]=W,M}function A(M,H){if(!(M instanceof H))throw new TypeError("Cannot call a class as a function")}function N(M,H){if(!M)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return H&&(typeof H=="object"||typeof H=="function")?H:M}function R(M,H){if(typeof H!="function"&&H!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof H);M.prototype=Object.create(H&&H.prototype,{constructor:{value:M,enumerable:!1,writable:!0,configurable:!0}}),H&&(Object.setPrototypeOf?Object.setPrototypeOf(M,H):M.__proto__=H)}var C=(0,P.default)("quill:keyboard"),B=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",U=function(M){R(H,M),l(H,null,[{key:"match",value:function($,et){return et=q(et),["altKey","ctrlKey","metaKey","shiftKey"].some(function(ut){return!!et[ut]!==$[ut]&&et[ut]!==null})?!1:et.key===($.which||$.keyCode)}}]);function H(W,$){A(this,H);var et=N(this,(H.__proto__||Object.getPrototypeOf(H)).call(this,W,$));return et.bindings={},Object.keys(et.options.bindings).forEach(function(ut){ut==="list autofill"&&W.scroll.whitelist!=null&&!W.scroll.whitelist.list||et.options.bindings[ut]&&et.addBinding(et.options.bindings[ut])}),et.addBinding({key:H.keys.ENTER,shiftKey:null},_),et.addBinding({key:H.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){}),/Firefox/i.test(navigator.userAgent)?(et.addBinding({key:H.keys.BACKSPACE},{collapsed:!0},K),et.addBinding({key:H.keys.DELETE},{collapsed:!0},k)):(et.addBinding({key:H.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},K),et.addBinding({key:H.keys.DELETE},{collapsed:!0,suffix:/^.?$/},k)),et.addBinding({key:H.keys.BACKSPACE},{collapsed:!1},L),et.addBinding({key:H.keys.DELETE},{collapsed:!1},L),et.addBinding({key:H.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},K),et.listen(),et}return l(H,[{key:"addBinding",value:function($){var et=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},ut=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},pt=q($);if(pt==null||pt.key==null)return C.warn("Attempted to add invalid keyboard binding",pt);typeof et=="function"&&(et={handler:et}),typeof ut=="function"&&(ut={handler:ut}),pt=(0,u.default)(pt,et,ut),this.bindings[pt.key]=this.bindings[pt.key]||[],this.bindings[pt.key].push(pt)}},{key:"listen",value:function(){var $=this;this.quill.root.addEventListener("keydown",function(et){if(!et.defaultPrevented){var ut=et.which||et.keyCode,pt=($.bindings[ut]||[]).filter(function(V){return H.match(et,V)});if(pt.length!==0){var dt=$.quill.getSelection();if(!(dt==null||!$.quill.hasFocus())){var gt=$.quill.getLine(dt.index),Nt=t(gt,2),qt=Nt[0],kt=Nt[1],Z=$.quill.getLeaf(dt.index),tt=t(Z,2),it=tt[0],ot=tt[1],nt=dt.length===0?[it,ot]:$.quill.getLeaf(dt.index+dt.length),xt=t(nt,2),bt=xt[0],St=xt[1],Zt=it instanceof O.default.Text?it.value().slice(0,ot):"",Yt=bt instanceof O.default.Text?bt.value().slice(St):"",jt={collapsed:dt.length===0,empty:dt.length===0&&qt.length()<=1,format:$.quill.getFormat(dt),offset:kt,prefix:Zt,suffix:Yt},Y=pt.some(function(V){if(V.collapsed!=null&&V.collapsed!==jt.collapsed||V.empty!=null&&V.empty!==jt.empty||V.offset!=null&&V.offset!==jt.offset)return!1;if(Array.isArray(V.format)){if(V.format.every(function(X){return jt.format[X]==null}))return!1}else if(h(V.format)==="object"&&!Object.keys(V.format).every(function(X){return V.format[X]===!0?jt.format[X]!=null:V.format[X]===!1?jt.format[X]==null:(0,i.default)(V.format[X],jt.format[X])}))return!1;return V.prefix!=null&&!V.prefix.test(jt.prefix)||V.suffix!=null&&!V.suffix.test(jt.suffix)?!1:V.handler.call($,dt,jt)!==!0});Y&&et.preventDefault()}}}})}}]),H}(D.default);U.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},U.DEFAULTS={bindings:{bold:Q("bold"),italic:Q("italic"),underline:Q("underline"),indent:{key:U.keys.TAB,format:["blockquote","indent","list"],handler:function(H,W){if(W.collapsed&&W.offset!==0)return!0;this.quill.format("indent","+1",E.default.sources.USER)}},outdent:{key:U.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(H,W){if(W.collapsed&&W.offset!==0)return!0;this.quill.format("indent","-1",E.default.sources.USER)}},"outdent backspace":{key:U.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(H,W){W.format.indent!=null?this.quill.format("indent","-1",E.default.sources.USER):W.format.list!=null&&this.quill.format("list",!1,E.default.sources.USER)}},"indent code-block":F(!0),"outdent code-block":F(!1),"remove tab":{key:U.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(H){this.quill.deleteText(H.index-1,1,E.default.sources.USER)}},tab:{key:U.keys.TAB,handler:function(H){this.quill.history.cutoff();var W=new g.default().retain(H.index).delete(H.length).insert("	");this.quill.updateContents(W,E.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(H.index+1,E.default.sources.SILENT)}},"list empty enter":{key:U.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(H,W){this.quill.format("list",!1,E.default.sources.USER),W.format.indent&&this.quill.format("indent",!1,E.default.sources.USER)}},"checklist enter":{key:U.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(H){var W=this.quill.getLine(H.index),$=t(W,2),et=$[0],ut=$[1],pt=(0,u.default)({},et.formats(),{list:"checked"}),dt=new g.default().retain(H.index).insert(`
`,pt).retain(et.length()-ut-1).retain(1,{list:"unchecked"});this.quill.updateContents(dt,E.default.sources.USER),this.quill.setSelection(H.index+1,E.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:U.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(H,W){var $=this.quill.getLine(H.index),et=t($,2),ut=et[0],pt=et[1],dt=new g.default().retain(H.index).insert(`
`,W.format).retain(ut.length()-pt-1).retain(1,{header:null});this.quill.updateContents(dt,E.default.sources.USER),this.quill.setSelection(H.index+1,E.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(H,W){var $=W.prefix.length,et=this.quill.getLine(H.index),ut=t(et,2),pt=ut[0],dt=ut[1];if(dt>$)return!0;var gt=void 0;switch(W.prefix.trim()){case"[]":case"[ ]":gt="unchecked";break;case"[x]":gt="checked";break;case"-":case"*":gt="bullet";break;default:gt="ordered"}this.quill.insertText(H.index," ",E.default.sources.USER),this.quill.history.cutoff();var Nt=new g.default().retain(H.index-dt).delete($+1).retain(pt.length()-2-dt).retain(1,{list:gt});this.quill.updateContents(Nt,E.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(H.index-$,E.default.sources.SILENT)}},"code exit":{key:U.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(H){var W=this.quill.getLine(H.index),$=t(W,2),et=$[0],ut=$[1],pt=new g.default().retain(H.index+et.length()-ut-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(pt,E.default.sources.USER)}},"embed left":z(U.keys.LEFT,!1),"embed left shift":z(U.keys.LEFT,!0),"embed right":z(U.keys.RIGHT,!1),"embed right shift":z(U.keys.RIGHT,!0)}};function z(M,H){var W,$=M===U.keys.LEFT?"prefix":"suffix";return W={key:M,shiftKey:H,altKey:null},S(W,$,/^$/),S(W,"handler",function(ut){var pt=ut.index;M===U.keys.RIGHT&&(pt+=ut.length+1);var dt=this.quill.getLeaf(pt),gt=t(dt,1),Nt=gt[0];return Nt instanceof O.default.Embed?(M===U.keys.LEFT?H?this.quill.setSelection(ut.index-1,ut.length+1,E.default.sources.USER):this.quill.setSelection(ut.index-1,E.default.sources.USER):H?this.quill.setSelection(ut.index,ut.length+1,E.default.sources.USER):this.quill.setSelection(ut.index+ut.length+1,E.default.sources.USER),!1):!0}),W}function K(M,H){if(!(M.index===0||this.quill.getLength()<=1)){var W=this.quill.getLine(M.index),$=t(W,1),et=$[0],ut={};if(H.offset===0){var pt=this.quill.getLine(M.index-1),dt=t(pt,1),gt=dt[0];if(gt!=null&&gt.length()>1){var Nt=et.formats(),qt=this.quill.getFormat(M.index-1,1);ut=d.default.attributes.diff(Nt,qt)||{}}}var kt=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(H.prefix)?2:1;this.quill.deleteText(M.index-kt,kt,E.default.sources.USER),Object.keys(ut).length>0&&this.quill.formatLine(M.index-kt,kt,ut,E.default.sources.USER),this.quill.focus()}}function k(M,H){var W=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(H.suffix)?2:1;if(!(M.index>=this.quill.getLength()-W)){var $={},et=0,ut=this.quill.getLine(M.index),pt=t(ut,1),dt=pt[0];if(H.offset>=dt.length()-1){var gt=this.quill.getLine(M.index+1),Nt=t(gt,1),qt=Nt[0];if(qt){var kt=dt.formats(),Z=this.quill.getFormat(M.index,1);$=d.default.attributes.diff(kt,Z)||{},et=qt.length()}}this.quill.deleteText(M.index,W,E.default.sources.USER),Object.keys($).length>0&&this.quill.formatLine(M.index+et-1,W,$,E.default.sources.USER)}}function L(M){var H=this.quill.getLines(M),W={};if(H.length>1){var $=H[0].formats(),et=H[H.length-1].formats();W=d.default.attributes.diff(et,$)||{}}this.quill.deleteText(M,E.default.sources.USER),Object.keys(W).length>0&&this.quill.formatLine(M.index,1,W,E.default.sources.USER),this.quill.setSelection(M.index,E.default.sources.SILENT),this.quill.focus()}function _(M,H){var W=this;M.length>0&&this.quill.scroll.deleteAt(M.index,M.length);var $=Object.keys(H.format).reduce(function(et,ut){return O.default.query(ut,O.default.Scope.BLOCK)&&!Array.isArray(H.format[ut])&&(et[ut]=H.format[ut]),et},{});this.quill.insertText(M.index,`
`,$,E.default.sources.USER),this.quill.setSelection(M.index+1,E.default.sources.SILENT),this.quill.focus(),Object.keys(H.format).forEach(function(et){$[et]==null&&(Array.isArray(H.format[et])||et!=="link"&&W.quill.format(et,H.format[et],E.default.sources.USER))})}function F(M){return{key:U.keys.TAB,shiftKey:!M,format:{"code-block":!0},handler:function(W){var $=O.default.query("code-block"),et=W.index,ut=W.length,pt=this.quill.scroll.descendant($,et),dt=t(pt,2),gt=dt[0],Nt=dt[1];if(gt!=null){var qt=this.quill.getIndex(gt),kt=gt.newlineIndex(Nt,!0)+1,Z=gt.newlineIndex(qt+Nt+ut),tt=gt.domNode.textContent.slice(kt,Z).split(`
`);Nt=0,tt.forEach(function(it,ot){M?(gt.insertAt(kt+Nt,$.TAB),Nt+=$.TAB.length,ot===0?et+=$.TAB.length:ut+=$.TAB.length):it.startsWith($.TAB)&&(gt.deleteAt(kt+Nt,$.TAB.length),Nt-=$.TAB.length,ot===0?et-=$.TAB.length:ut-=$.TAB.length),Nt+=it.length+1}),this.quill.update(E.default.sources.USER),this.quill.setSelection(et,ut,E.default.sources.SILENT)}}}}function Q(M){return{key:M[0].toUpperCase(),shortKey:!0,handler:function(W,$){this.quill.format(M,!$.format[M],E.default.sources.USER)}}}function q(M){if(typeof M=="string"||typeof M=="number")return q({key:M});if((typeof M=="undefined"?"undefined":h(M))==="object"&&(M=(0,s.default)(M,!1)),typeof M.key=="string")if(U.keys[M.key.toUpperCase()]!=null)M.key=U.keys[M.key.toUpperCase()];else if(M.key.length===1)M.key=M.key.toUpperCase().charCodeAt(0);else return null;return M.shortKey&&(M[B]=M.shortKey,delete M.shortKey),M}n.default=U,n.SHORTKEY=B},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function d(c,O){var v=[],E=!0,T=!1,P=void 0;try{for(var I=c[Symbol.iterator](),D;!(E=(D=I.next()).done)&&(v.push(D.value),!(O&&v.length===O));E=!0);}catch(w){T=!0,P=w}finally{try{!E&&I.return&&I.return()}finally{if(T)throw P}}return v}return function(c,O){if(Array.isArray(c))return c;if(Symbol.iterator in Object(c))return d(c,O);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),t=function d(c,O,v){c===null&&(c=Function.prototype);var E=Object.getOwnPropertyDescriptor(c,O);if(E===void 0){var T=Object.getPrototypeOf(c);return T===null?void 0:d(T,O,v)}else{if("value"in E)return E.value;var P=E.get;return P===void 0?void 0:P.call(v)}},l=function(){function d(c,O){for(var v=0;v<O.length;v++){var E=O[v];E.enumerable=E.enumerable||!1,E.configurable=!0,"value"in E&&(E.writable=!0),Object.defineProperty(c,E.key,E)}}return function(c,O,v){return O&&d(c.prototype,O),v&&d(c,v),c}}(),a=e(0),s=r(a),f=e(7),i=r(f);function r(d){return d&&d.__esModule?d:{default:d}}function u(d,c){if(!(d instanceof c))throw new TypeError("Cannot call a class as a function")}function y(d,c){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return c&&(typeof c=="object"||typeof c=="function")?c:d}function g(d,c){if(typeof c!="function"&&c!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof c);d.prototype=Object.create(c&&c.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),c&&(Object.setPrototypeOf?Object.setPrototypeOf(d,c):d.__proto__=c)}var p=function(d){g(c,d),l(c,null,[{key:"value",value:function(){}}]);function c(O,v){u(this,c);var E=y(this,(c.__proto__||Object.getPrototypeOf(c)).call(this,O));return E.selection=v,E.textNode=document.createTextNode(c.CONTENTS),E.domNode.appendChild(E.textNode),E._length=0,E}return l(c,[{key:"detach",value:function(){this.parent!=null&&this.parent.removeChild(this)}},{key:"format",value:function(v,E){if(this._length!==0)return t(c.prototype.__proto__||Object.getPrototypeOf(c.prototype),"format",this).call(this,v,E);for(var T=this,P=0;T!=null&&T.statics.scope!==s.default.Scope.BLOCK_BLOT;)P+=T.offset(T.parent),T=T.parent;T!=null&&(this._length=c.CONTENTS.length,T.optimize(),T.formatAt(P,c.CONTENTS.length,v,E),this._length=0)}},{key:"index",value:function(v,E){return v===this.textNode?0:t(c.prototype.__proto__||Object.getPrototypeOf(c.prototype),"index",this).call(this,v,E)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){t(c.prototype.__proto__||Object.getPrototypeOf(c.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!(this.selection.composing||this.parent==null)){var v=this.textNode,E=this.selection.getNativeRange(),T=void 0,P=void 0,I=void 0;if(E!=null&&E.start.node===v&&E.end.node===v){var D=[v,E.start.offset,E.end.offset];T=D[0],P=D[1],I=D[2]}for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==c.CONTENTS){var w=this.textNode.data.split(c.CONTENTS).join("");this.next instanceof i.default?(T=this.next.domNode,this.next.insertAt(0,w),this.textNode.data=c.CONTENTS):(this.textNode.data=w,this.parent.insertBefore(s.default.create(this.textNode),this),this.textNode=document.createTextNode(c.CONTENTS),this.domNode.appendChild(this.textNode))}if(this.remove(),P!=null){var S=[P,I].map(function(N){return Math.max(0,Math.min(T.data.length,N-1))}),A=h(S,2);return P=A[0],I=A[1],{startNode:T,startOffset:P,endNode:T,endOffset:I}}}}},{key:"update",value:function(v,E){var T=this;if(v.some(function(I){return I.type==="characterData"&&I.target===T.textNode})){var P=this.restore();P&&(E.range=P)}}},{key:"value",value:function(){return""}}]),c}(s.default.Embed);p.blotName="cursor",p.className="ql-cursor",p.tagName="span",p.CONTENTS="\uFEFF",n.default=p},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=e(0),t=s(h),l=e(4),a=s(l);function s(y){return y&&y.__esModule?y:{default:y}}function f(y,g){if(!(y instanceof g))throw new TypeError("Cannot call a class as a function")}function i(y,g){if(!y)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g&&(typeof g=="object"||typeof g=="function")?g:y}function r(y,g){if(typeof g!="function"&&g!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof g);y.prototype=Object.create(g&&g.prototype,{constructor:{value:y,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(y,g):y.__proto__=g)}var u=function(y){r(g,y);function g(){return f(this,g),i(this,(g.__proto__||Object.getPrototypeOf(g)).apply(this,arguments))}return g}(t.default.Container);u.allowedChildren=[a.default,l.BlockEmbed,u],n.default=u},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.ColorStyle=n.ColorClass=n.ColorAttributor=void 0;var h=function(){function p(d,c){for(var O=0;O<c.length;O++){var v=c[O];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(d,v.key,v)}}return function(d,c,O){return c&&p(d.prototype,c),O&&p(d,O),d}}(),t=function p(d,c,O){d===null&&(d=Function.prototype);var v=Object.getOwnPropertyDescriptor(d,c);if(v===void 0){var E=Object.getPrototypeOf(d);return E===null?void 0:p(E,c,O)}else{if("value"in v)return v.value;var T=v.get;return T===void 0?void 0:T.call(O)}},l=e(0),a=s(l);function s(p){return p&&p.__esModule?p:{default:p}}function f(p,d){if(!(p instanceof d))throw new TypeError("Cannot call a class as a function")}function i(p,d){if(!p)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return d&&(typeof d=="object"||typeof d=="function")?d:p}function r(p,d){if(typeof d!="function"&&d!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof d);p.prototype=Object.create(d&&d.prototype,{constructor:{value:p,enumerable:!1,writable:!0,configurable:!0}}),d&&(Object.setPrototypeOf?Object.setPrototypeOf(p,d):p.__proto__=d)}var u=function(p){r(d,p);function d(){return f(this,d),i(this,(d.__proto__||Object.getPrototypeOf(d)).apply(this,arguments))}return h(d,[{key:"value",value:function(O){var v=t(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"value",this).call(this,O);return v.startsWith("rgb(")?(v=v.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),"#"+v.split(",").map(function(E){return("00"+parseInt(E).toString(16)).slice(-2)}).join("")):v}}]),d}(a.default.Attributor.Style),y=new a.default.Attributor.Class("color","ql-color",{scope:a.default.Scope.INLINE}),g=new u("color","color",{scope:a.default.Scope.INLINE});n.ColorAttributor=u,n.ColorClass=y,n.ColorStyle=g},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.sanitize=n.default=void 0;var h=function(){function g(p,d){for(var c=0;c<d.length;c++){var O=d[c];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(p,O.key,O)}}return function(p,d,c){return d&&g(p.prototype,d),c&&g(p,c),p}}(),t=function g(p,d,c){p===null&&(p=Function.prototype);var O=Object.getOwnPropertyDescriptor(p,d);if(O===void 0){var v=Object.getPrototypeOf(p);return v===null?void 0:g(v,d,c)}else{if("value"in O)return O.value;var E=O.get;return E===void 0?void 0:E.call(c)}},l=e(6),a=s(l);function s(g){return g&&g.__esModule?g:{default:g}}function f(g,p){if(!(g instanceof p))throw new TypeError("Cannot call a class as a function")}function i(g,p){if(!g)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return p&&(typeof p=="object"||typeof p=="function")?p:g}function r(g,p){if(typeof p!="function"&&p!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof p);g.prototype=Object.create(p&&p.prototype,{constructor:{value:g,enumerable:!1,writable:!0,configurable:!0}}),p&&(Object.setPrototypeOf?Object.setPrototypeOf(g,p):g.__proto__=p)}var u=function(g){r(p,g);function p(){return f(this,p),i(this,(p.__proto__||Object.getPrototypeOf(p)).apply(this,arguments))}return h(p,[{key:"format",value:function(c,O){if(c!==this.statics.blotName||!O)return t(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"format",this).call(this,c,O);O=this.constructor.sanitize(O),this.domNode.setAttribute("href",O)}}],[{key:"create",value:function(c){var O=t(p.__proto__||Object.getPrototypeOf(p),"create",this).call(this,c);return c=this.sanitize(c),O.setAttribute("href",c),O.setAttribute("rel","noopener noreferrer"),O.setAttribute("target","_blank"),O}},{key:"formats",value:function(c){return c.getAttribute("href")}},{key:"sanitize",value:function(c){return y(c,this.PROTOCOL_WHITELIST)?c:this.SANITIZED_URL}}]),p}(a.default);u.blotName="link",u.tagName="A",u.SANITIZED_URL="about:blank",u.PROTOCOL_WHITELIST=["http","https","mailto","tel"];function y(g,p){var d=document.createElement("a");d.href=g;var c=d.href.slice(0,d.href.indexOf(":"));return p.indexOf(c)>-1}n.default=u,n.sanitize=y},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(p){return typeof p}:function(p){return p&&typeof Symbol=="function"&&p.constructor===Symbol&&p!==Symbol.prototype?"symbol":typeof p},t=function(){function p(d,c){for(var O=0;O<c.length;O++){var v=c[O];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(d,v.key,v)}}return function(d,c,O){return c&&p(d.prototype,c),O&&p(d,O),d}}(),l=e(23),a=i(l),s=e(107),f=i(s);function i(p){return p&&p.__esModule?p:{default:p}}function r(p,d){if(!(p instanceof d))throw new TypeError("Cannot call a class as a function")}var u=0;function y(p,d){p.setAttribute(d,p.getAttribute(d)!=="true")}var g=function(){function p(d){var c=this;r(this,p),this.select=d,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",function(){c.togglePicker()}),this.label.addEventListener("keydown",function(O){switch(O.keyCode){case a.default.keys.ENTER:c.togglePicker();break;case a.default.keys.ESCAPE:c.escape(),O.preventDefault();break}}),this.select.addEventListener("change",this.update.bind(this))}return t(p,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded"),y(this.label,"aria-expanded"),y(this.options,"aria-hidden")}},{key:"buildItem",value:function(c){var O=this,v=document.createElement("span");return v.tabIndex="0",v.setAttribute("role","button"),v.classList.add("ql-picker-item"),c.hasAttribute("value")&&v.setAttribute("data-value",c.getAttribute("value")),c.textContent&&v.setAttribute("data-label",c.textContent),v.addEventListener("click",function(){O.selectItem(v,!0)}),v.addEventListener("keydown",function(E){switch(E.keyCode){case a.default.keys.ENTER:O.selectItem(v,!0),E.preventDefault();break;case a.default.keys.ESCAPE:O.escape(),E.preventDefault();break}}),v}},{key:"buildLabel",value:function(){var c=document.createElement("span");return c.classList.add("ql-picker-label"),c.innerHTML=f.default,c.tabIndex="0",c.setAttribute("role","button"),c.setAttribute("aria-expanded","false"),this.container.appendChild(c),c}},{key:"buildOptions",value:function(){var c=this,O=document.createElement("span");O.classList.add("ql-picker-options"),O.setAttribute("aria-hidden","true"),O.tabIndex="-1",O.id="ql-picker-options-"+u,u+=1,this.label.setAttribute("aria-controls",O.id),this.options=O,[].slice.call(this.select.options).forEach(function(v){var E=c.buildItem(v);O.appendChild(E),v.selected===!0&&c.selectItem(E)}),this.container.appendChild(O)}},{key:"buildPicker",value:function(){var c=this;[].slice.call(this.select.attributes).forEach(function(O){c.container.setAttribute(O.name,O.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}},{key:"escape",value:function(){var c=this;this.close(),setTimeout(function(){return c.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(c){var O=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,v=this.container.querySelector(".ql-selected");if(c!==v&&(v!=null&&v.classList.remove("ql-selected"),c!=null&&(c.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(c.parentNode.children,c),c.hasAttribute("data-value")?this.label.setAttribute("data-value",c.getAttribute("data-value")):this.label.removeAttribute("data-value"),c.hasAttribute("data-label")?this.label.setAttribute("data-label",c.getAttribute("data-label")):this.label.removeAttribute("data-label"),O))){if(typeof Event=="function")this.select.dispatchEvent(new Event("change"));else if((typeof Event=="undefined"?"undefined":h(Event))==="object"){var E=document.createEvent("Event");E.initEvent("change",!0,!0),this.select.dispatchEvent(E)}this.close()}}},{key:"update",value:function(){var c=void 0;if(this.select.selectedIndex>-1){var O=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];c=this.select.options[this.select.selectedIndex],this.selectItem(O)}else this.selectItem(null);var v=c!=null&&c!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",v)}}]),p}();n.default=g},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=e(0),t=C(h),l=e(5),a=C(l),s=e(4),f=C(s),i=e(16),r=C(i),u=e(25),y=C(u),g=e(24),p=C(g),d=e(35),c=C(d),O=e(6),v=C(O),E=e(22),T=C(E),P=e(7),I=C(P),D=e(55),w=C(D),S=e(42),A=C(S),N=e(23),R=C(N);function C(B){return B&&B.__esModule?B:{default:B}}a.default.register({"blots/block":f.default,"blots/block/embed":s.BlockEmbed,"blots/break":r.default,"blots/container":y.default,"blots/cursor":p.default,"blots/embed":c.default,"blots/inline":v.default,"blots/scroll":T.default,"blots/text":I.default,"modules/clipboard":w.default,"modules/history":A.default,"modules/keyboard":R.default}),t.default.register(f.default,r.default,p.default,v.default,T.default,I.default),n.default=a.default},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=e(1),t=function(){function l(a){this.domNode=a,this.domNode[h.DATA_KEY]={blot:this}}return Object.defineProperty(l.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),l.create=function(a){if(this.tagName==null)throw new h.ParchmentError("Blot definition missing tagName");var s;return Array.isArray(this.tagName)?(typeof a=="string"&&(a=a.toUpperCase(),parseInt(a).toString()===a&&(a=parseInt(a))),typeof a=="number"?s=document.createElement(this.tagName[a-1]):this.tagName.indexOf(a)>-1?s=document.createElement(a):s=document.createElement(this.tagName[0])):s=document.createElement(this.tagName),this.className&&s.classList.add(this.className),s},l.prototype.attach=function(){this.parent!=null&&(this.scroll=this.parent.scroll)},l.prototype.clone=function(){var a=this.domNode.cloneNode(!1);return h.create(a)},l.prototype.detach=function(){this.parent!=null&&this.parent.removeChild(this),delete this.domNode[h.DATA_KEY]},l.prototype.deleteAt=function(a,s){var f=this.isolate(a,s);f.remove()},l.prototype.formatAt=function(a,s,f,i){var r=this.isolate(a,s);if(h.query(f,h.Scope.BLOT)!=null&&i)r.wrap(f,i);else if(h.query(f,h.Scope.ATTRIBUTE)!=null){var u=h.create(this.statics.scope);r.wrap(u),u.format(f,i)}},l.prototype.insertAt=function(a,s,f){var i=f==null?h.create("text",s):h.create(s,f),r=this.split(a);this.parent.insertBefore(i,r)},l.prototype.insertInto=function(a,s){s===void 0&&(s=null),this.parent!=null&&this.parent.children.remove(this);var f=null;a.children.insertBefore(this,s),s!=null&&(f=s.domNode),(this.domNode.parentNode!=a.domNode||this.domNode.nextSibling!=f)&&a.domNode.insertBefore(this.domNode,f),this.parent=a,this.attach()},l.prototype.isolate=function(a,s){var f=this.split(a);return f.split(s),f},l.prototype.length=function(){return 1},l.prototype.offset=function(a){return a===void 0&&(a=this.parent),this.parent==null||this==a?0:this.parent.children.offset(this)+this.parent.offset(a)},l.prototype.optimize=function(a){this.domNode[h.DATA_KEY]!=null&&delete this.domNode[h.DATA_KEY].mutations},l.prototype.remove=function(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},l.prototype.replace=function(a){a.parent!=null&&(a.parent.insertBefore(this,a.next),a.remove())},l.prototype.replaceWith=function(a,s){var f=typeof a=="string"?h.create(a,s):a;return f.replace(this),f},l.prototype.split=function(a,s){return a===0?this:this.next},l.prototype.update=function(a,s){},l.prototype.wrap=function(a,s){var f=typeof a=="string"?h.create(a,s):a;return this.parent!=null&&this.parent.insertBefore(f,this.next),f.appendChild(this),f},l.blotName="abstract",l}();n.default=t},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=e(12),t=e(32),l=e(33),a=e(1),s=function(){function f(i){this.attributes={},this.domNode=i,this.build()}return f.prototype.attribute=function(i,r){r?i.add(this.domNode,r)&&(i.value(this.domNode)!=null?this.attributes[i.attrName]=i:delete this.attributes[i.attrName]):(i.remove(this.domNode),delete this.attributes[i.attrName])},f.prototype.build=function(){var i=this;this.attributes={};var r=h.default.keys(this.domNode),u=t.default.keys(this.domNode),y=l.default.keys(this.domNode);r.concat(u).concat(y).forEach(function(g){var p=a.query(g,a.Scope.ATTRIBUTE);p instanceof h.default&&(i.attributes[p.attrName]=p)})},f.prototype.copy=function(i){var r=this;Object.keys(this.attributes).forEach(function(u){var y=r.attributes[u].value(r.domNode);i.format(u,y)})},f.prototype.move=function(i){var r=this;this.copy(i),Object.keys(this.attributes).forEach(function(u){r.attributes[u].remove(r.domNode)}),this.attributes={}},f.prototype.values=function(){var i=this;return Object.keys(this.attributes).reduce(function(r,u){return r[u]=i.attributes[u].value(i.domNode),r},{})},f}();n.default=s},function(o,n,e){var h=this&&this.__extends||function(){var s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,i){f.__proto__=i}||function(f,i){for(var r in i)i.hasOwnProperty(r)&&(f[r]=i[r])};return function(f,i){s(f,i);function r(){this.constructor=f}f.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var t=e(12);function l(s,f){var i=s.getAttribute("class")||"";return i.split(/\s+/).filter(function(r){return r.indexOf(f+"-")===0})}var a=function(s){h(f,s);function f(){return s!==null&&s.apply(this,arguments)||this}return f.keys=function(i){return(i.getAttribute("class")||"").split(/\s+/).map(function(r){return r.split("-").slice(0,-1).join("-")})},f.prototype.add=function(i,r){return this.canAdd(i,r)?(this.remove(i),i.classList.add(this.keyName+"-"+r),!0):!1},f.prototype.remove=function(i){var r=l(i,this.keyName);r.forEach(function(u){i.classList.remove(u)}),i.classList.length===0&&i.removeAttribute("class")},f.prototype.value=function(i){var r=l(i,this.keyName)[0]||"",u=r.slice(this.keyName.length+1);return this.canAdd(i,u)?u:""},f}(t.default);n.default=a},function(o,n,e){var h=this&&this.__extends||function(){var s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,i){f.__proto__=i}||function(f,i){for(var r in i)i.hasOwnProperty(r)&&(f[r]=i[r])};return function(f,i){s(f,i);function r(){this.constructor=f}f.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var t=e(12);function l(s){var f=s.split("-"),i=f.slice(1).map(function(r){return r[0].toUpperCase()+r.slice(1)}).join("");return f[0]+i}var a=function(s){h(f,s);function f(){return s!==null&&s.apply(this,arguments)||this}return f.keys=function(i){return(i.getAttribute("style")||"").split(";").map(function(r){var u=r.split(":");return u[0].trim()})},f.prototype.add=function(i,r){return this.canAdd(i,r)?(i.style[l(this.keyName)]=r,!0):!1},f.prototype.remove=function(i){i.style[l(this.keyName)]="",i.getAttribute("style")||i.removeAttribute("style")},f.prototype.value=function(i){var r=i.style[l(this.keyName)];return this.canAdd(i,r)?r:""},f}(t.default);n.default=a},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function a(s,f){for(var i=0;i<f.length;i++){var r=f[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(s,r.key,r)}}return function(s,f,i){return f&&a(s.prototype,f),i&&a(s,i),s}}();function t(a,s){if(!(a instanceof s))throw new TypeError("Cannot call a class as a function")}var l=function(){function a(s,f){t(this,a),this.quill=s,this.options=f,this.modules={}}return h(a,[{key:"init",value:function(){var f=this;Object.keys(this.options.modules).forEach(function(i){f.modules[i]==null&&f.addModule(i)})}},{key:"addModule",value:function(f){var i=this.quill.constructor.import("modules/"+f);return this.modules[f]=new i(this.quill,this.options.modules[f]||{}),this.modules[f]}}]),a}();l.DEFAULTS={modules:{}},l.themes={default:l},n.default=l},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function d(c,O){for(var v=0;v<O.length;v++){var E=O[v];E.enumerable=E.enumerable||!1,E.configurable=!0,"value"in E&&(E.writable=!0),Object.defineProperty(c,E.key,E)}}return function(c,O,v){return O&&d(c.prototype,O),v&&d(c,v),c}}(),t=function d(c,O,v){c===null&&(c=Function.prototype);var E=Object.getOwnPropertyDescriptor(c,O);if(E===void 0){var T=Object.getPrototypeOf(c);return T===null?void 0:d(T,O,v)}else{if("value"in E)return E.value;var P=E.get;return P===void 0?void 0:P.call(v)}},l=e(0),a=i(l),s=e(7),f=i(s);function i(d){return d&&d.__esModule?d:{default:d}}function r(d,c){if(!(d instanceof c))throw new TypeError("Cannot call a class as a function")}function u(d,c){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return c&&(typeof c=="object"||typeof c=="function")?c:d}function y(d,c){if(typeof c!="function"&&c!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof c);d.prototype=Object.create(c&&c.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),c&&(Object.setPrototypeOf?Object.setPrototypeOf(d,c):d.__proto__=c)}var g="\uFEFF",p=function(d){y(c,d);function c(O){r(this,c);var v=u(this,(c.__proto__||Object.getPrototypeOf(c)).call(this,O));return v.contentNode=document.createElement("span"),v.contentNode.setAttribute("contenteditable",!1),[].slice.call(v.domNode.childNodes).forEach(function(E){v.contentNode.appendChild(E)}),v.leftGuard=document.createTextNode(g),v.rightGuard=document.createTextNode(g),v.domNode.appendChild(v.leftGuard),v.domNode.appendChild(v.contentNode),v.domNode.appendChild(v.rightGuard),v}return h(c,[{key:"index",value:function(v,E){return v===this.leftGuard?0:v===this.rightGuard?1:t(c.prototype.__proto__||Object.getPrototypeOf(c.prototype),"index",this).call(this,v,E)}},{key:"restore",value:function(v){var E=void 0,T=void 0,P=v.data.split(g).join("");if(v===this.leftGuard)if(this.prev instanceof f.default){var I=this.prev.length();this.prev.insertAt(I,P),E={startNode:this.prev.domNode,startOffset:I+P.length}}else T=document.createTextNode(P),this.parent.insertBefore(a.default.create(T),this),E={startNode:T,startOffset:P.length};else v===this.rightGuard&&(this.next instanceof f.default?(this.next.insertAt(0,P),E={startNode:this.next.domNode,startOffset:P.length}):(T=document.createTextNode(P),this.parent.insertBefore(a.default.create(T),this.next),E={startNode:T,startOffset:P.length}));return v.data=g,E}},{key:"update",value:function(v,E){var T=this;v.forEach(function(P){if(P.type==="characterData"&&(P.target===T.leftGuard||P.target===T.rightGuard)){var I=T.restore(P.target);I&&(E.range=I)}})}}]),c}(a.default.Embed);n.default=p},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.AlignStyle=n.AlignClass=n.AlignAttribute=void 0;var h=e(0),t=l(h);function l(r){return r&&r.__esModule?r:{default:r}}var a={scope:t.default.Scope.BLOCK,whitelist:["right","center","justify"]},s=new t.default.Attributor.Attribute("align","align",a),f=new t.default.Attributor.Class("align","ql-align",a),i=new t.default.Attributor.Style("align","text-align",a);n.AlignAttribute=s,n.AlignClass=f,n.AlignStyle=i},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.BackgroundStyle=n.BackgroundClass=void 0;var h=e(0),t=a(h),l=e(26);function a(i){return i&&i.__esModule?i:{default:i}}var s=new t.default.Attributor.Class("background","ql-bg",{scope:t.default.Scope.INLINE}),f=new l.ColorAttributor("background","background-color",{scope:t.default.Scope.INLINE});n.BackgroundClass=s,n.BackgroundStyle=f},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.DirectionStyle=n.DirectionClass=n.DirectionAttribute=void 0;var h=e(0),t=l(h);function l(r){return r&&r.__esModule?r:{default:r}}var a={scope:t.default.Scope.BLOCK,whitelist:["rtl"]},s=new t.default.Attributor.Attribute("direction","dir",a),f=new t.default.Attributor.Class("direction","ql-direction",a),i=new t.default.Attributor.Style("direction","direction",a);n.DirectionAttribute=s,n.DirectionClass=f,n.DirectionStyle=i},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.FontClass=n.FontStyle=void 0;var h=function(){function d(c,O){for(var v=0;v<O.length;v++){var E=O[v];E.enumerable=E.enumerable||!1,E.configurable=!0,"value"in E&&(E.writable=!0),Object.defineProperty(c,E.key,E)}}return function(c,O,v){return O&&d(c.prototype,O),v&&d(c,v),c}}(),t=function d(c,O,v){c===null&&(c=Function.prototype);var E=Object.getOwnPropertyDescriptor(c,O);if(E===void 0){var T=Object.getPrototypeOf(c);return T===null?void 0:d(T,O,v)}else{if("value"in E)return E.value;var P=E.get;return P===void 0?void 0:P.call(v)}},l=e(0),a=s(l);function s(d){return d&&d.__esModule?d:{default:d}}function f(d,c){if(!(d instanceof c))throw new TypeError("Cannot call a class as a function")}function i(d,c){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return c&&(typeof c=="object"||typeof c=="function")?c:d}function r(d,c){if(typeof c!="function"&&c!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof c);d.prototype=Object.create(c&&c.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),c&&(Object.setPrototypeOf?Object.setPrototypeOf(d,c):d.__proto__=c)}var u={scope:a.default.Scope.INLINE,whitelist:["serif","monospace"]},y=new a.default.Attributor.Class("font","ql-font",u),g=function(d){r(c,d);function c(){return f(this,c),i(this,(c.__proto__||Object.getPrototypeOf(c)).apply(this,arguments))}return h(c,[{key:"value",value:function(v){return t(c.prototype.__proto__||Object.getPrototypeOf(c.prototype),"value",this).call(this,v).replace(/["']/g,"")}}]),c}(a.default.Attributor.Style),p=new g("font","font-family",u);n.FontStyle=p,n.FontClass=y},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.SizeStyle=n.SizeClass=void 0;var h=e(0),t=l(h);function l(f){return f&&f.__esModule?f:{default:f}}var a=new t.default.Attributor.Class("size","ql-size",{scope:t.default.Scope.INLINE,whitelist:["small","large","huge"]}),s=new t.default.Attributor.Style("size","font-size",{scope:t.default.Scope.INLINE,whitelist:["10px","18px","32px"]});n.SizeClass=a,n.SizeStyle=s},function(o,n,e){o.exports={align:{"":e(76),center:e(77),right:e(78),justify:e(79)},background:e(80),blockquote:e(81),bold:e(82),clean:e(83),code:e(58),"code-block":e(58),color:e(84),direction:{"":e(85),rtl:e(86)},float:{center:e(87),full:e(88),left:e(89),right:e(90)},formula:e(91),header:{1:e(92),2:e(93)},italic:e(94),image:e(95),indent:{"+1":e(96),"-1":e(97)},link:e(98),list:{ordered:e(99),bullet:e(100),check:e(101)},script:{sub:e(102),super:e(103)},strike:e(104),underline:e(105),video:e(106)}},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.getLastChangeIndex=n.default=void 0;var h=function(){function O(v,E){for(var T=0;T<E.length;T++){var P=E[T];P.enumerable=P.enumerable||!1,P.configurable=!0,"value"in P&&(P.writable=!0),Object.defineProperty(v,P.key,P)}}return function(v,E,T){return E&&O(v.prototype,E),T&&O(v,T),v}}(),t=e(0),l=r(t),a=e(5),s=r(a),f=e(9),i=r(f);function r(O){return O&&O.__esModule?O:{default:O}}function u(O,v){if(!(O instanceof v))throw new TypeError("Cannot call a class as a function")}function y(O,v){if(!O)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:O}function g(O,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);O.prototype=Object.create(v&&v.prototype,{constructor:{value:O,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(O,v):O.__proto__=v)}var p=function(O){g(v,O);function v(E,T){u(this,v);var P=y(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,E,T));return P.lastRecorded=0,P.ignoreChange=!1,P.clear(),P.quill.on(s.default.events.EDITOR_CHANGE,function(I,D,w,S){I!==s.default.events.TEXT_CHANGE||P.ignoreChange||(!P.options.userOnly||S===s.default.sources.USER?P.record(D,w):P.transform(D))}),P.quill.keyboard.addBinding({key:"Z",shortKey:!0},P.undo.bind(P)),P.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},P.redo.bind(P)),/Win/i.test(navigator.platform)&&P.quill.keyboard.addBinding({key:"Y",shortKey:!0},P.redo.bind(P)),P}return h(v,[{key:"change",value:function(T,P){if(this.stack[T].length!==0){var I=this.stack[T].pop();this.stack[P].push(I),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(I[T],s.default.sources.USER),this.ignoreChange=!1;var D=c(I[T]);this.quill.setSelection(D)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(T,P){if(T.ops.length!==0){this.stack.redo=[];var I=this.quill.getContents().diff(P),D=Date.now();if(this.lastRecorded+this.options.delay>D&&this.stack.undo.length>0){var w=this.stack.undo.pop();I=I.compose(w.undo),T=w.redo.compose(T)}else this.lastRecorded=D;this.stack.undo.push({redo:T,undo:I}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(T){this.stack.undo.forEach(function(P){P.undo=T.transform(P.undo,!0),P.redo=T.transform(P.redo,!0)}),this.stack.redo.forEach(function(P){P.undo=T.transform(P.undo,!0),P.redo=T.transform(P.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]),v}(i.default);p.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};function d(O){var v=O.ops[O.ops.length-1];return v==null?!1:v.insert!=null?typeof v.insert=="string"&&v.insert.endsWith(`
`):v.attributes!=null?Object.keys(v.attributes).some(function(E){return l.default.query(E,l.default.Scope.BLOCK)!=null}):!1}function c(O){var v=O.reduce(function(T,P){return T+=P.delete||0,T},0),E=O.length()-v;return d(O)&&(E-=1),E}n.default=p,n.getLastChangeIndex=c},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=n.BaseTooltip=void 0;var h=function(){function _(F,Q){for(var q=0;q<Q.length;q++){var M=Q[q];M.enumerable=M.enumerable||!1,M.configurable=!0,"value"in M&&(M.writable=!0),Object.defineProperty(F,M.key,M)}}return function(F,Q,q){return Q&&_(F.prototype,Q),q&&_(F,q),F}}(),t=function _(F,Q,q){F===null&&(F=Function.prototype);var M=Object.getOwnPropertyDescriptor(F,Q);if(M===void 0){var H=Object.getPrototypeOf(F);return H===null?void 0:_(H,Q,q)}else{if("value"in M)return M.value;var W=M.get;return W===void 0?void 0:W.call(q)}},l=e(3),a=D(l),s=e(2),f=D(s),i=e(8),r=D(i),u=e(23),y=D(u),g=e(34),p=D(g),d=e(59),c=D(d),O=e(60),v=D(O),E=e(28),T=D(E),P=e(61),I=D(P);function D(_){return _&&_.__esModule?_:{default:_}}function w(_,F){if(!(_ instanceof F))throw new TypeError("Cannot call a class as a function")}function S(_,F){if(!_)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return F&&(typeof F=="object"||typeof F=="function")?F:_}function A(_,F){if(typeof F!="function"&&F!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof F);_.prototype=Object.create(F&&F.prototype,{constructor:{value:_,enumerable:!1,writable:!0,configurable:!0}}),F&&(Object.setPrototypeOf?Object.setPrototypeOf(_,F):_.__proto__=F)}var N=[!1,"center","right","justify"],R=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],C=[!1,"serif","monospace"],B=["1","2","3",!1],U=["small",!1,"large","huge"],z=function(_){A(F,_);function F(Q,q){w(this,F);var M=S(this,(F.__proto__||Object.getPrototypeOf(F)).call(this,Q,q)),H=function W($){if(!document.body.contains(Q.root))return document.body.removeEventListener("click",W);M.tooltip!=null&&!M.tooltip.root.contains($.target)&&document.activeElement!==M.tooltip.textbox&&!M.quill.hasFocus()&&M.tooltip.hide(),M.pickers!=null&&M.pickers.forEach(function(et){et.container.contains($.target)||et.close()})};return Q.emitter.listenDOM("click",document.body,H),M}return h(F,[{key:"addModule",value:function(q){var M=t(F.prototype.__proto__||Object.getPrototypeOf(F.prototype),"addModule",this).call(this,q);return q==="toolbar"&&this.extendToolbar(M),M}},{key:"buildButtons",value:function(q,M){q.forEach(function(H){var W=H.getAttribute("class")||"";W.split(/\s+/).forEach(function($){if($.startsWith("ql-")&&($=$.slice(3),M[$]!=null))if($==="direction")H.innerHTML=M[$][""]+M[$].rtl;else if(typeof M[$]=="string")H.innerHTML=M[$];else{var et=H.value||"";et!=null&&M[$][et]&&(H.innerHTML=M[$][et])}})})}},{key:"buildPickers",value:function(q,M){var H=this;this.pickers=q.map(function($){if($.classList.contains("ql-align"))return $.querySelector("option")==null&&L($,N),new v.default($,M.align);if($.classList.contains("ql-background")||$.classList.contains("ql-color")){var et=$.classList.contains("ql-background")?"background":"color";return $.querySelector("option")==null&&L($,R,et==="background"?"#ffffff":"#000000"),new c.default($,M[et])}else return $.querySelector("option")==null&&($.classList.contains("ql-font")?L($,C):$.classList.contains("ql-header")?L($,B):$.classList.contains("ql-size")&&L($,U)),new T.default($)});var W=function(){H.pickers.forEach(function(et){et.update()})};this.quill.on(r.default.events.EDITOR_CHANGE,W)}}]),F}(p.default);z.DEFAULTS=(0,a.default)(!0,{},p.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var F=this,Q=this.container.querySelector("input.ql-image[type=file]");Q==null&&(Q=document.createElement("input"),Q.setAttribute("type","file"),Q.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),Q.classList.add("ql-image"),Q.addEventListener("change",function(){if(Q.files!=null&&Q.files[0]!=null){var q=new FileReader;q.onload=function(M){var H=F.quill.getSelection(!0);F.quill.updateContents(new f.default().retain(H.index).delete(H.length).insert({image:M.target.result}),r.default.sources.USER),F.quill.setSelection(H.index+1,r.default.sources.SILENT),Q.value=""},q.readAsDataURL(Q.files[0])}}),this.container.appendChild(Q)),Q.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});var K=function(_){A(F,_);function F(Q,q){w(this,F);var M=S(this,(F.__proto__||Object.getPrototypeOf(F)).call(this,Q,q));return M.textbox=M.root.querySelector('input[type="text"]'),M.listen(),M}return h(F,[{key:"listen",value:function(){var q=this;this.textbox.addEventListener("keydown",function(M){y.default.match(M,"enter")?(q.save(),M.preventDefault()):y.default.match(M,"escape")&&(q.cancel(),M.preventDefault())})}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var q=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",M=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),M!=null?this.textbox.value=M:q!==this.root.getAttribute("data-mode")&&(this.textbox.value=""),this.position(this.quill.getBounds(this.quill.selection.savedRange)),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+q)||""),this.root.setAttribute("data-mode",q)}},{key:"restoreFocus",value:function(){var q=this.quill.scrollingContainer.scrollTop;this.quill.focus(),this.quill.scrollingContainer.scrollTop=q}},{key:"save",value:function(){var q=this.textbox.value;switch(this.root.getAttribute("data-mode")){case"link":{var M=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",q,r.default.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",q,r.default.sources.USER)),this.quill.root.scrollTop=M;break}case"video":q=k(q);case"formula":{if(!q)break;var H=this.quill.getSelection(!0);if(H!=null){var W=H.index+H.length;this.quill.insertEmbed(W,this.root.getAttribute("data-mode"),q,r.default.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(W+1," ",r.default.sources.USER),this.quill.setSelection(W+2,r.default.sources.USER)}break}}this.textbox.value="",this.hide()}}]),F}(I.default);function k(_){var F=_.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||_.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return F?(F[1]||"https")+"://www.youtube.com/embed/"+F[2]+"?showinfo=0":(F=_.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(F[1]||"https")+"://player.vimeo.com/video/"+F[2]+"/":_}function L(_,F){var Q=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;F.forEach(function(q){var M=document.createElement("option");q===Q?M.setAttribute("selected","selected"):M.setAttribute("value",q),_.appendChild(M)})}n.BaseTooltip=K,n.default=z},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function t(){this.head=this.tail=null,this.length=0}return t.prototype.append=function(){for(var l=[],a=0;a<arguments.length;a++)l[a]=arguments[a];this.insertBefore(l[0],null),l.length>1&&this.append.apply(this,l.slice(1))},t.prototype.contains=function(l){for(var a,s=this.iterator();a=s();)if(a===l)return!0;return!1},t.prototype.insertBefore=function(l,a){l&&(l.next=a,a!=null?(l.prev=a.prev,a.prev!=null&&(a.prev.next=l),a.prev=l,a===this.head&&(this.head=l)):this.tail!=null?(this.tail.next=l,l.prev=this.tail,this.tail=l):(l.prev=null,this.head=this.tail=l),this.length+=1)},t.prototype.offset=function(l){for(var a=0,s=this.head;s!=null;){if(s===l)return a;a+=s.length(),s=s.next}return-1},t.prototype.remove=function(l){this.contains(l)&&(l.prev!=null&&(l.prev.next=l.next),l.next!=null&&(l.next.prev=l.prev),l===this.head&&(this.head=l.next),l===this.tail&&(this.tail=l.prev),this.length-=1)},t.prototype.iterator=function(l){return l===void 0&&(l=this.head),function(){var a=l;return l!=null&&(l=l.next),a}},t.prototype.find=function(l,a){a===void 0&&(a=!1);for(var s,f=this.iterator();s=f();){var i=s.length();if(l<i||a&&l===i&&(s.next==null||s.next.length()!==0))return[s,l];l-=i}return[null,0]},t.prototype.forEach=function(l){for(var a,s=this.iterator();a=s();)l(a)},t.prototype.forEachAt=function(l,a,s){if(!(a<=0))for(var f=this.find(l),i=f[0],r=f[1],u,y=l-r,g=this.iterator(i);(u=g())&&y<l+a;){var p=u.length();l>y?s(u,l-y,Math.min(a,y+p-l)):s(u,0,Math.min(p,l+a-y)),y+=p}},t.prototype.map=function(l){return this.reduce(function(a,s){return a.push(l(s)),a},[])},t.prototype.reduce=function(l,a){for(var s,f=this.iterator();s=f();)a=l(a,s);return a},t}();n.default=h},function(o,n,e){var h=this&&this.__extends||function(){var i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,u){r.__proto__=u}||function(r,u){for(var y in u)u.hasOwnProperty(y)&&(r[y]=u[y])};return function(r,u){i(r,u);function y(){this.constructor=r}r.prototype=u===null?Object.create(u):(y.prototype=u.prototype,new y)}}();Object.defineProperty(n,"__esModule",{value:!0});var t=e(17),l=e(1),a={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},s=100,f=function(i){h(r,i);function r(u){var y=i.call(this,u)||this;return y.scroll=y,y.observer=new MutationObserver(function(g){y.update(g)}),y.observer.observe(y.domNode,a),y.attach(),y}return r.prototype.detach=function(){i.prototype.detach.call(this),this.observer.disconnect()},r.prototype.deleteAt=function(u,y){this.update(),u===0&&y===this.length()?this.children.forEach(function(g){g.remove()}):i.prototype.deleteAt.call(this,u,y)},r.prototype.formatAt=function(u,y,g,p){this.update(),i.prototype.formatAt.call(this,u,y,g,p)},r.prototype.insertAt=function(u,y,g){this.update(),i.prototype.insertAt.call(this,u,y,g)},r.prototype.optimize=function(u,y){var g=this;u===void 0&&(u=[]),y===void 0&&(y={}),i.prototype.optimize.call(this,y);for(var p=[].slice.call(this.observer.takeRecords());p.length>0;)u.push(p.pop());for(var d=function(E,T){T===void 0&&(T=!0),!(E==null||E===g)&&E.domNode.parentNode!=null&&(E.domNode[l.DATA_KEY].mutations==null&&(E.domNode[l.DATA_KEY].mutations=[]),T&&d(E.parent))},c=function(E){E.domNode[l.DATA_KEY]==null||E.domNode[l.DATA_KEY].mutations==null||(E instanceof t.default&&E.children.forEach(c),E.optimize(y))},O=u,v=0;O.length>0;v+=1){if(v>=s)throw new Error("[Parchment] Maximum optimize iterations reached");for(O.forEach(function(E){var T=l.find(E.target,!0);T!=null&&(T.domNode===E.target&&(E.type==="childList"?(d(l.find(E.previousSibling,!1)),[].forEach.call(E.addedNodes,function(P){var I=l.find(P,!1);d(I,!1),I instanceof t.default&&I.children.forEach(function(D){d(D,!1)})})):E.type==="attributes"&&d(T.prev)),d(T))}),this.children.forEach(c),O=[].slice.call(this.observer.takeRecords()),p=O.slice();p.length>0;)u.push(p.pop())}},r.prototype.update=function(u,y){var g=this;y===void 0&&(y={}),u=u||this.observer.takeRecords(),u.map(function(p){var d=l.find(p.target,!0);return d==null?null:d.domNode[l.DATA_KEY].mutations==null?(d.domNode[l.DATA_KEY].mutations=[p],d):(d.domNode[l.DATA_KEY].mutations.push(p),null)}).forEach(function(p){p==null||p===g||p.domNode[l.DATA_KEY]==null||p.update(p.domNode[l.DATA_KEY].mutations||[],y)}),this.domNode[l.DATA_KEY].mutations!=null&&i.prototype.update.call(this,this.domNode[l.DATA_KEY].mutations,y),this.optimize(u,y)},r.blotName="scroll",r.defaultChild="block",r.scope=l.Scope.BLOCK_BLOT,r.tagName="DIV",r}(t.default);n.default=f},function(o,n,e){var h=this&&this.__extends||function(){var f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,r){i.__proto__=r}||function(i,r){for(var u in r)r.hasOwnProperty(u)&&(i[u]=r[u])};return function(i,r){f(i,r);function u(){this.constructor=i}i.prototype=r===null?Object.create(r):(u.prototype=r.prototype,new u)}}();Object.defineProperty(n,"__esModule",{value:!0});var t=e(18),l=e(1);function a(f,i){if(Object.keys(f).length!==Object.keys(i).length)return!1;for(var r in f)if(f[r]!==i[r])return!1;return!0}var s=function(f){h(i,f);function i(){return f!==null&&f.apply(this,arguments)||this}return i.formats=function(r){if(r.tagName!==i.tagName)return f.formats.call(this,r)},i.prototype.format=function(r,u){var y=this;r===this.statics.blotName&&!u?(this.children.forEach(function(g){g instanceof t.default||(g=g.wrap(i.blotName,!0)),y.attributes.copy(g)}),this.unwrap()):f.prototype.format.call(this,r,u)},i.prototype.formatAt=function(r,u,y,g){if(this.formats()[y]!=null||l.query(y,l.Scope.ATTRIBUTE)){var p=this.isolate(r,u);p.format(y,g)}else f.prototype.formatAt.call(this,r,u,y,g)},i.prototype.optimize=function(r){f.prototype.optimize.call(this,r);var u=this.formats();if(Object.keys(u).length===0)return this.unwrap();var y=this.next;y instanceof i&&y.prev===this&&a(u,y.formats())&&(y.moveChildren(this),y.remove())},i.blotName="inline",i.scope=l.Scope.INLINE_BLOT,i.tagName="SPAN",i}(t.default);n.default=s},function(o,n,e){var h=this&&this.__extends||function(){var s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,i){f.__proto__=i}||function(f,i){for(var r in i)i.hasOwnProperty(r)&&(f[r]=i[r])};return function(f,i){s(f,i);function r(){this.constructor=f}f.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var t=e(18),l=e(1),a=function(s){h(f,s);function f(){return s!==null&&s.apply(this,arguments)||this}return f.formats=function(i){var r=l.query(f.blotName).tagName;if(i.tagName!==r)return s.formats.call(this,i)},f.prototype.format=function(i,r){l.query(i,l.Scope.BLOCK)!=null&&(i===this.statics.blotName&&!r?this.replaceWith(f.blotName):s.prototype.format.call(this,i,r))},f.prototype.formatAt=function(i,r,u,y){l.query(u,l.Scope.BLOCK)!=null?this.format(u,y):s.prototype.formatAt.call(this,i,r,u,y)},f.prototype.insertAt=function(i,r,u){if(u==null||l.query(r,l.Scope.INLINE)!=null)s.prototype.insertAt.call(this,i,r,u);else{var y=this.split(i),g=l.create(r,u);y.parent.insertBefore(g,y)}},f.prototype.update=function(i,r){navigator.userAgent.match(/Trident/)?this.build():s.prototype.update.call(this,i,r)},f.blotName="block",f.scope=l.Scope.BLOCK_BLOT,f.tagName="P",f}(t.default);n.default=a},function(o,n,e){var h=this&&this.__extends||function(){var a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,f){s.__proto__=f}||function(s,f){for(var i in f)f.hasOwnProperty(i)&&(s[i]=f[i])};return function(s,f){a(s,f);function i(){this.constructor=s}s.prototype=f===null?Object.create(f):(i.prototype=f.prototype,new i)}}();Object.defineProperty(n,"__esModule",{value:!0});var t=e(19),l=function(a){h(s,a);function s(){return a!==null&&a.apply(this,arguments)||this}return s.formats=function(f){},s.prototype.format=function(f,i){a.prototype.formatAt.call(this,0,this.length(),f,i)},s.prototype.formatAt=function(f,i,r,u){f===0&&i===this.length()?this.format(r,u):a.prototype.formatAt.call(this,f,i,r,u)},s.prototype.formats=function(){return this.statics.formats(this.domNode)},s}(t.default);n.default=l},function(o,n,e){var h=this&&this.__extends||function(){var s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,i){f.__proto__=i}||function(f,i){for(var r in i)i.hasOwnProperty(r)&&(f[r]=i[r])};return function(f,i){s(f,i);function r(){this.constructor=f}f.prototype=i===null?Object.create(i):(r.prototype=i.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var t=e(19),l=e(1),a=function(s){h(f,s);function f(i){var r=s.call(this,i)||this;return r.text=r.statics.value(r.domNode),r}return f.create=function(i){return document.createTextNode(i)},f.value=function(i){var r=i.data;return r.normalize&&(r=r.normalize()),r},f.prototype.deleteAt=function(i,r){this.domNode.data=this.text=this.text.slice(0,i)+this.text.slice(i+r)},f.prototype.index=function(i,r){return this.domNode===i?r:-1},f.prototype.insertAt=function(i,r,u){u==null?(this.text=this.text.slice(0,i)+r+this.text.slice(i),this.domNode.data=this.text):s.prototype.insertAt.call(this,i,r,u)},f.prototype.length=function(){return this.text.length},f.prototype.optimize=function(i){s.prototype.optimize.call(this,i),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof f&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},f.prototype.position=function(i,r){return[this.domNode,i]},f.prototype.split=function(i,r){if(r===void 0&&(r=!1),!r){if(i===0)return this;if(i===this.length())return this.next}var u=l.create(this.domNode.splitText(i));return this.parent.insertBefore(u,this.next),this.text=this.statics.value(this.domNode),u},f.prototype.update=function(i,r){var u=this;i.some(function(y){return y.type==="characterData"&&y.target===u.domNode})&&(this.text=this.statics.value(this.domNode))},f.prototype.value=function(){return this.text},f.blotName="text",f.scope=l.Scope.INLINE_BLOT,f}(t.default);n.default=a},function(o,n,e){var h=document.createElement("div");if(h.classList.toggle("test-class",!1),h.classList.contains("test-class")){var t=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(l,a){return arguments.length>1&&!this.contains(l)==!a?a:t.call(this,l)}}String.prototype.startsWith||(String.prototype.startsWith=function(l,a){return a=a||0,this.substr(a,l.length)===l}),String.prototype.endsWith||(String.prototype.endsWith=function(l,a){var s=this.toString();(typeof a!="number"||!isFinite(a)||Math.floor(a)!==a||a>s.length)&&(a=s.length),a-=l.length;var f=s.indexOf(l,a);return f!==-1&&f===a}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(a){if(this===null)throw new TypeError("Array.prototype.find called on null or undefined");if(typeof a!="function")throw new TypeError("predicate must be a function");for(var s=Object(this),f=s.length>>>0,i=arguments[1],r,u=0;u<f;u++)if(r=s[u],a.call(i,r,u,s))return r}}),document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)})},function(o,n){var e=-1,h=1,t=0;function l(v,E,T){if(v==E)return v?[[t,v]]:[];(T<0||v.length<T)&&(T=null);var P=i(v,E),I=v.substring(0,P);v=v.substring(P),E=E.substring(P),P=r(v,E);var D=v.substring(v.length-P);v=v.substring(0,v.length-P),E=E.substring(0,E.length-P);var w=a(v,E);return I&&w.unshift([t,I]),D&&w.push([t,D]),y(w),T!=null&&(w=d(w,T)),w=c(w),w}function a(v,E){var T;if(!v)return[[h,E]];if(!E)return[[e,v]];var P=v.length>E.length?v:E,I=v.length>E.length?E:v,D=P.indexOf(I);if(D!=-1)return T=[[h,P.substring(0,D)],[t,I],[h,P.substring(D+I.length)]],v.length>E.length&&(T[0][0]=T[2][0]=e),T;if(I.length==1)return[[e,v],[h,E]];var w=u(v,E);if(w){var S=w[0],A=w[1],N=w[2],R=w[3],C=w[4],B=l(S,N),U=l(A,R);return B.concat([[t,C]],U)}return s(v,E)}function s(v,E){for(var T=v.length,P=E.length,I=Math.ceil((T+P)/2),D=I,w=2*I,S=new Array(w),A=new Array(w),N=0;N<w;N++)S[N]=-1,A[N]=-1;S[D+1]=0,A[D+1]=0;for(var R=T-P,C=R%2!=0,B=0,U=0,z=0,K=0,k=0;k<I;k++){for(var L=-k+B;L<=k-U;L+=2){var _=D+L,F;L==-k||L!=k&&S[_-1]<S[_+1]?F=S[_+1]:F=S[_-1]+1;for(var Q=F-L;F<T&&Q<P&&v.charAt(F)==E.charAt(Q);)F++,Q++;if(S[_]=F,F>T)U+=2;else if(Q>P)B+=2;else if(C){var q=D+R-L;if(q>=0&&q<w&&A[q]!=-1){var M=T-A[q];if(F>=M)return f(v,E,F,Q)}}}for(var H=-k+z;H<=k-K;H+=2){var q=D+H,M;H==-k||H!=k&&A[q-1]<A[q+1]?M=A[q+1]:M=A[q-1]+1;for(var W=M-H;M<T&&W<P&&v.charAt(T-M-1)==E.charAt(P-W-1);)M++,W++;if(A[q]=M,M>T)K+=2;else if(W>P)z+=2;else if(!C){var _=D+R-H;if(_>=0&&_<w&&S[_]!=-1){var F=S[_],Q=D+F-_;if(M=T-M,F>=M)return f(v,E,F,Q)}}}}return[[e,v],[h,E]]}function f(v,E,T,P){var I=v.substring(0,T),D=E.substring(0,P),w=v.substring(T),S=E.substring(P),A=l(I,D),N=l(w,S);return A.concat(N)}function i(v,E){if(!v||!E||v.charAt(0)!=E.charAt(0))return 0;for(var T=0,P=Math.min(v.length,E.length),I=P,D=0;T<I;)v.substring(D,I)==E.substring(D,I)?(T=I,D=T):P=I,I=Math.floor((P-T)/2+T);return I}function r(v,E){if(!v||!E||v.charAt(v.length-1)!=E.charAt(E.length-1))return 0;for(var T=0,P=Math.min(v.length,E.length),I=P,D=0;T<I;)v.substring(v.length-I,v.length-D)==E.substring(E.length-I,E.length-D)?(T=I,D=T):P=I,I=Math.floor((P-T)/2+T);return I}function u(v,E){var T=v.length>E.length?v:E,P=v.length>E.length?E:v;if(T.length<4||P.length*2<T.length)return null;function I(U,z,K){for(var k=U.substring(K,K+Math.floor(U.length/4)),L=-1,_="",F,Q,q,M;(L=z.indexOf(k,L+1))!=-1;){var H=i(U.substring(K),z.substring(L)),W=r(U.substring(0,K),z.substring(0,L));_.length<W+H&&(_=z.substring(L-W,L)+z.substring(L,L+H),F=U.substring(0,K-W),Q=U.substring(K+H),q=z.substring(0,L-W),M=z.substring(L+H))}return _.length*2>=U.length?[F,Q,q,M,_]:null}var D=I(T,P,Math.ceil(T.length/4)),w=I(T,P,Math.ceil(T.length/2)),S;if(!D&&!w)return null;w?D?S=D[4].length>w[4].length?D:w:S=w:S=D;var A,N,R,C;v.length>E.length?(A=S[0],N=S[1],R=S[2],C=S[3]):(R=S[0],C=S[1],A=S[2],N=S[3]);var B=S[4];return[A,N,R,C,B]}function y(v){v.push([t,""]);for(var E=0,T=0,P=0,I="",D="",w;E<v.length;)switch(v[E][0]){case h:P++,D+=v[E][1],E++;break;case e:T++,I+=v[E][1],E++;break;case t:T+P>1?(T!==0&&P!==0&&(w=i(D,I),w!==0&&(E-T-P>0&&v[E-T-P-1][0]==t?v[E-T-P-1][1]+=D.substring(0,w):(v.splice(0,0,[t,D.substring(0,w)]),E++),D=D.substring(w),I=I.substring(w)),w=r(D,I),w!==0&&(v[E][1]=D.substring(D.length-w)+v[E][1],D=D.substring(0,D.length-w),I=I.substring(0,I.length-w))),T===0?v.splice(E-P,T+P,[h,D]):P===0?v.splice(E-T,T+P,[e,I]):v.splice(E-T-P,T+P,[e,I],[h,D]),E=E-T-P+(T?1:0)+(P?1:0)+1):E!==0&&v[E-1][0]==t?(v[E-1][1]+=v[E][1],v.splice(E,1)):E++,P=0,T=0,I="",D="";break}v[v.length-1][1]===""&&v.pop();var S=!1;for(E=1;E<v.length-1;)v[E-1][0]==t&&v[E+1][0]==t&&(v[E][1].substring(v[E][1].length-v[E-1][1].length)==v[E-1][1]?(v[E][1]=v[E-1][1]+v[E][1].substring(0,v[E][1].length-v[E-1][1].length),v[E+1][1]=v[E-1][1]+v[E+1][1],v.splice(E-1,1),S=!0):v[E][1].substring(0,v[E+1][1].length)==v[E+1][1]&&(v[E-1][1]+=v[E+1][1],v[E][1]=v[E][1].substring(v[E+1][1].length)+v[E+1][1],v.splice(E+1,1),S=!0)),E++;S&&y(v)}var g=l;g.INSERT=h,g.DELETE=e,g.EQUAL=t,o.exports=g;function p(v,E){if(E===0)return[t,v];for(var T=0,P=0;P<v.length;P++){var I=v[P];if(I[0]===e||I[0]===t){var D=T+I[1].length;if(E===D)return[P+1,v];if(E<D){v=v.slice();var w=E-T,S=[I[0],I[1].slice(0,w)],A=[I[0],I[1].slice(w)];return v.splice(P,1,S,A),[P+1,v]}else T=D}}throw new Error("cursor_pos is out of bounds!")}function d(v,E){var T=p(v,E),P=T[1],I=T[0],D=P[I],w=P[I+1];if(D==null)return v;if(D[0]!==t)return v;if(w!=null&&D[1]+w[1]===w[1]+D[1])return P.splice(I,2,w,D),O(P,I,2);if(w!=null&&w[1].indexOf(D[1])===0){P.splice(I,2,[w[0],D[1]],[0,D[1]]);var S=w[1].slice(D[1].length);return S.length>0&&P.splice(I+2,0,[w[0],S]),O(P,I,3)}else return v}function c(v){for(var E=!1,T=function(w){return w.charCodeAt(0)>=56320&&w.charCodeAt(0)<=57343},P=function(w){return w.charCodeAt(w.length-1)>=55296&&w.charCodeAt(w.length-1)<=56319},I=2;I<v.length;I+=1)v[I-2][0]===t&&P(v[I-2][1])&&v[I-1][0]===e&&T(v[I-1][1])&&v[I][0]===h&&T(v[I][1])&&(E=!0,v[I-1][1]=v[I-2][1].slice(-1)+v[I-1][1],v[I][1]=v[I-2][1].slice(-1)+v[I][1],v[I-2][1]=v[I-2][1].slice(0,-1));if(!E)return v;for(var D=[],I=0;I<v.length;I+=1)v[I][1].length>0&&D.push(v[I]);return D}function O(v,E,T){for(var P=E+T-1;P>=0&&P>=E-1;P--)if(P+1<v.length){var I=v[P],D=v[P+1];I[0]===D[1]&&v.splice(P,2,[I[0],I[1]+D[1]])}return v}},function(o,n){n=o.exports=typeof Object.keys=="function"?Object.keys:e,n.shim=e;function e(h){var t=[];for(var l in h)t.push(l);return t}},function(o,n){var e=function(){return Object.prototype.toString.call(arguments)}()=="[object Arguments]";n=o.exports=e?h:t,n.supported=h;function h(l){return Object.prototype.toString.call(l)=="[object Arguments]"}n.unsupported=t;function t(l){return l&&typeof l=="object"&&typeof l.length=="number"&&Object.prototype.hasOwnProperty.call(l,"callee")&&!Object.prototype.propertyIsEnumerable.call(l,"callee")||!1}},function(o,n){var e=Object.prototype.hasOwnProperty,h="~";function t(){}Object.create&&(t.prototype=Object.create(null),new t().__proto__||(h=!1));function l(s,f,i){this.fn=s,this.context=f,this.once=i||!1}function a(){this._events=new t,this._eventsCount=0}a.prototype.eventNames=function(){var f=[],i,r;if(this._eventsCount===0)return f;for(r in i=this._events)e.call(i,r)&&f.push(h?r.slice(1):r);return Object.getOwnPropertySymbols?f.concat(Object.getOwnPropertySymbols(i)):f},a.prototype.listeners=function(f,i){var r=h?h+f:f,u=this._events[r];if(i)return!!u;if(!u)return[];if(u.fn)return[u.fn];for(var y=0,g=u.length,p=new Array(g);y<g;y++)p[y]=u[y].fn;return p},a.prototype.emit=function(f,i,r,u,y,g){var p=h?h+f:f;if(!this._events[p])return!1;var d=this._events[p],c=arguments.length,O,v;if(d.fn){switch(d.once&&this.removeListener(f,d.fn,void 0,!0),c){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,i),!0;case 3:return d.fn.call(d.context,i,r),!0;case 4:return d.fn.call(d.context,i,r,u),!0;case 5:return d.fn.call(d.context,i,r,u,y),!0;case 6:return d.fn.call(d.context,i,r,u,y,g),!0}for(v=1,O=new Array(c-1);v<c;v++)O[v-1]=arguments[v];d.fn.apply(d.context,O)}else{var E=d.length,T;for(v=0;v<E;v++)switch(d[v].once&&this.removeListener(f,d[v].fn,void 0,!0),c){case 1:d[v].fn.call(d[v].context);break;case 2:d[v].fn.call(d[v].context,i);break;case 3:d[v].fn.call(d[v].context,i,r);break;case 4:d[v].fn.call(d[v].context,i,r,u);break;default:if(!O)for(T=1,O=new Array(c-1);T<c;T++)O[T-1]=arguments[T];d[v].fn.apply(d[v].context,O)}}return!0},a.prototype.on=function(f,i,r){var u=new l(i,r||this),y=h?h+f:f;return this._events[y]?this._events[y].fn?this._events[y]=[this._events[y],u]:this._events[y].push(u):(this._events[y]=u,this._eventsCount++),this},a.prototype.once=function(f,i,r){var u=new l(i,r||this,!0),y=h?h+f:f;return this._events[y]?this._events[y].fn?this._events[y]=[this._events[y],u]:this._events[y].push(u):(this._events[y]=u,this._eventsCount++),this},a.prototype.removeListener=function(f,i,r,u){var y=h?h+f:f;if(!this._events[y])return this;if(!i)return--this._eventsCount===0?this._events=new t:delete this._events[y],this;var g=this._events[y];if(g.fn)g.fn===i&&(!u||g.once)&&(!r||g.context===r)&&(--this._eventsCount===0?this._events=new t:delete this._events[y]);else{for(var p=0,d=[],c=g.length;p<c;p++)(g[p].fn!==i||u&&!g[p].once||r&&g[p].context!==r)&&d.push(g[p]);d.length?this._events[y]=d.length===1?d[0]:d:--this._eventsCount===0?this._events=new t:delete this._events[y]}return this},a.prototype.removeAllListeners=function(f){var i;return f?(i=h?h+f:f,this._events[i]&&(--this._eventsCount===0?this._events=new t:delete this._events[i])):(this._events=new t,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prototype.setMaxListeners=function(){return this},a.prefixed=h,a.EventEmitter=a,typeof o!="undefined"&&(o.exports=a)},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.matchText=n.matchSpacing=n.matchNewline=n.matchBlot=n.matchAttributor=n.default=void 0;var h=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(Z){return typeof Z}:function(Z){return Z&&typeof Symbol=="function"&&Z.constructor===Symbol&&Z!==Symbol.prototype?"symbol":typeof Z},t=function(){function Z(tt,it){var ot=[],nt=!0,xt=!1,bt=void 0;try{for(var St=tt[Symbol.iterator](),Zt;!(nt=(Zt=St.next()).done)&&(ot.push(Zt.value),!(it&&ot.length===it));nt=!0);}catch(Yt){xt=!0,bt=Yt}finally{try{!nt&&St.return&&St.return()}finally{if(xt)throw bt}}return ot}return function(tt,it){if(Array.isArray(tt))return tt;if(Symbol.iterator in Object(tt))return Z(tt,it);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),l=function(){function Z(tt,it){for(var ot=0;ot<it.length;ot++){var nt=it[ot];nt.enumerable=nt.enumerable||!1,nt.configurable=!0,"value"in nt&&(nt.writable=!0),Object.defineProperty(tt,nt.key,nt)}}return function(tt,it,ot){return it&&Z(tt.prototype,it),ot&&Z(tt,ot),tt}}(),a=e(3),s=A(a),f=e(2),i=A(f),r=e(0),u=A(r),y=e(5),g=A(y),p=e(10),d=A(p),c=e(9),O=A(c),v=e(36),E=e(37),T=e(13),P=A(T),I=e(26),D=e(38),w=e(39),S=e(40);function A(Z){return Z&&Z.__esModule?Z:{default:Z}}function N(Z,tt,it){return tt in Z?Object.defineProperty(Z,tt,{value:it,enumerable:!0,configurable:!0,writable:!0}):Z[tt]=it,Z}function R(Z,tt){if(!(Z instanceof tt))throw new TypeError("Cannot call a class as a function")}function C(Z,tt){if(!Z)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return tt&&(typeof tt=="object"||typeof tt=="function")?tt:Z}function B(Z,tt){if(typeof tt!="function"&&tt!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof tt);Z.prototype=Object.create(tt&&tt.prototype,{constructor:{value:Z,enumerable:!1,writable:!0,configurable:!0}}),tt&&(Object.setPrototypeOf?Object.setPrototypeOf(Z,tt):Z.__proto__=tt)}var U=(0,d.default)("quill:clipboard"),z="__ql-matcher",K=[[Node.TEXT_NODE,kt],[Node.TEXT_NODE,gt],["br",ut],[Node.ELEMENT_NODE,gt],[Node.ELEMENT_NODE,et],[Node.ELEMENT_NODE,Nt],[Node.ELEMENT_NODE,$],[Node.ELEMENT_NODE,qt],["li",dt],["b",W.bind(W,"bold")],["i",W.bind(W,"italic")],["style",pt]],k=[v.AlignAttribute,D.DirectionAttribute].reduce(function(Z,tt){return Z[tt.keyName]=tt,Z},{}),L=[v.AlignStyle,E.BackgroundStyle,I.ColorStyle,D.DirectionStyle,w.FontStyle,S.SizeStyle].reduce(function(Z,tt){return Z[tt.keyName]=tt,Z},{}),_=function(Z){B(tt,Z);function tt(it,ot){R(this,tt);var nt=C(this,(tt.__proto__||Object.getPrototypeOf(tt)).call(this,it,ot));return nt.quill.root.addEventListener("paste",nt.onPaste.bind(nt)),nt.container=nt.quill.addContainer("ql-clipboard"),nt.container.setAttribute("contenteditable",!0),nt.container.setAttribute("tabindex",-1),nt.matchers=[],K.concat(nt.options.matchers).forEach(function(xt){var bt=t(xt,2),St=bt[0],Zt=bt[1];!ot.matchVisual&&Zt===Nt||nt.addMatcher(St,Zt)}),nt}return l(tt,[{key:"addMatcher",value:function(ot,nt){this.matchers.push([ot,nt])}},{key:"convert",value:function(ot){if(typeof ot=="string")return this.container.innerHTML=ot.replace(/\>\r?\n +\</g,"><"),this.convert();var nt=this.quill.getFormat(this.quill.selection.savedRange.index);if(nt[P.default.blotName]){var xt=this.container.innerText;return this.container.innerHTML="",new i.default().insert(xt,N({},P.default.blotName,nt[P.default.blotName]))}var bt=this.prepareMatching(),St=t(bt,2),Zt=St[0],Yt=St[1],jt=H(this.container,Zt,Yt);return q(jt,`
`)&&jt.ops[jt.ops.length-1].attributes==null&&(jt=jt.compose(new i.default().retain(jt.length()-1).delete(1))),U.log("convert",this.container.innerHTML,jt),this.container.innerHTML="",jt}},{key:"dangerouslyPasteHTML",value:function(ot,nt){var xt=arguments.length>2&&arguments[2]!==void 0?arguments[2]:g.default.sources.API;if(typeof ot=="string")this.quill.setContents(this.convert(ot),nt),this.quill.setSelection(0,g.default.sources.SILENT);else{var bt=this.convert(nt);this.quill.updateContents(new i.default().retain(ot).concat(bt),xt),this.quill.setSelection(ot+bt.length(),g.default.sources.SILENT)}}},{key:"onPaste",value:function(ot){var nt=this;if(!(ot.defaultPrevented||!this.quill.isEnabled())){var xt=this.quill.getSelection(),bt=new i.default().retain(xt.index),St=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(g.default.sources.SILENT),setTimeout(function(){bt=bt.concat(nt.convert()).delete(xt.length),nt.quill.updateContents(bt,g.default.sources.USER),nt.quill.setSelection(bt.length()-xt.length,g.default.sources.SILENT),nt.quill.scrollingContainer.scrollTop=St,nt.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var ot=this,nt=[],xt=[];return this.matchers.forEach(function(bt){var St=t(bt,2),Zt=St[0],Yt=St[1];switch(Zt){case Node.TEXT_NODE:xt.push(Yt);break;case Node.ELEMENT_NODE:nt.push(Yt);break;default:[].forEach.call(ot.container.querySelectorAll(Zt),function(jt){jt[z]=jt[z]||[],jt[z].push(Yt)});break}}),[nt,xt]}}]),tt}(O.default);_.DEFAULTS={matchers:[],matchVisual:!0};function F(Z,tt,it){return(typeof tt=="undefined"?"undefined":h(tt))==="object"?Object.keys(tt).reduce(function(ot,nt){return F(ot,nt,tt[nt])},Z):Z.reduce(function(ot,nt){return nt.attributes&&nt.attributes[tt]?ot.push(nt):ot.insert(nt.insert,(0,s.default)({},N({},tt,it),nt.attributes))},new i.default)}function Q(Z){if(Z.nodeType!==Node.ELEMENT_NODE)return{};var tt="__ql-computed-style";return Z[tt]||(Z[tt]=window.getComputedStyle(Z))}function q(Z,tt){for(var it="",ot=Z.ops.length-1;ot>=0&&it.length<tt.length;--ot){var nt=Z.ops[ot];if(typeof nt.insert!="string")break;it=nt.insert+it}return it.slice(-1*tt.length)===tt}function M(Z){if(Z.childNodes.length===0)return!1;var tt=Q(Z);return["block","list-item"].indexOf(tt.display)>-1}function H(Z,tt,it){return Z.nodeType===Z.TEXT_NODE?it.reduce(function(ot,nt){return nt(Z,ot)},new i.default):Z.nodeType===Z.ELEMENT_NODE?[].reduce.call(Z.childNodes||[],function(ot,nt){var xt=H(nt,tt,it);return nt.nodeType===Z.ELEMENT_NODE&&(xt=tt.reduce(function(bt,St){return St(nt,bt)},xt),xt=(nt[z]||[]).reduce(function(bt,St){return St(nt,bt)},xt)),ot.concat(xt)},new i.default):new i.default}function W(Z,tt,it){return F(it,Z,!0)}function $(Z,tt){var it=u.default.Attributor.Attribute.keys(Z),ot=u.default.Attributor.Class.keys(Z),nt=u.default.Attributor.Style.keys(Z),xt={};return it.concat(ot).concat(nt).forEach(function(bt){var St=u.default.query(bt,u.default.Scope.ATTRIBUTE);St!=null&&(xt[St.attrName]=St.value(Z),xt[St.attrName])||(St=k[bt],St!=null&&(St.attrName===bt||St.keyName===bt)&&(xt[St.attrName]=St.value(Z)||void 0),St=L[bt],St!=null&&(St.attrName===bt||St.keyName===bt)&&(St=L[bt],xt[St.attrName]=St.value(Z)||void 0))}),Object.keys(xt).length>0&&(tt=F(tt,xt)),tt}function et(Z,tt){var it=u.default.query(Z);if(it==null)return tt;if(it.prototype instanceof u.default.Embed){var ot={},nt=it.value(Z);nt!=null&&(ot[it.blotName]=nt,tt=new i.default().insert(ot,it.formats(Z)))}else typeof it.formats=="function"&&(tt=F(tt,it.blotName,it.formats(Z)));return tt}function ut(Z,tt){return q(tt,`
`)||tt.insert(`
`),tt}function pt(){return new i.default}function dt(Z,tt){var it=u.default.query(Z);if(it==null||it.blotName!=="list-item"||!q(tt,`
`))return tt;for(var ot=-1,nt=Z.parentNode;!nt.classList.contains("ql-clipboard");)(u.default.query(nt)||{}).blotName==="list"&&(ot+=1),nt=nt.parentNode;return ot<=0?tt:tt.compose(new i.default().retain(tt.length()-1).retain(1,{indent:ot}))}function gt(Z,tt){return q(tt,`
`)||(M(Z)||tt.length()>0&&Z.nextSibling&&M(Z.nextSibling))&&tt.insert(`
`),tt}function Nt(Z,tt){if(M(Z)&&Z.nextElementSibling!=null&&!q(tt,`

`)){var it=Z.offsetHeight+parseFloat(Q(Z).marginTop)+parseFloat(Q(Z).marginBottom);Z.nextElementSibling.offsetTop>Z.offsetTop+it*1.5&&tt.insert(`
`)}return tt}function qt(Z,tt){var it={},ot=Z.style||{};return ot.fontStyle&&Q(Z).fontStyle==="italic"&&(it.italic=!0),ot.fontWeight&&(Q(Z).fontWeight.startsWith("bold")||parseInt(Q(Z).fontWeight)>=700)&&(it.bold=!0),Object.keys(it).length>0&&(tt=F(tt,it)),parseFloat(ot.textIndent||0)>0&&(tt=new i.default().insert("	").concat(tt)),tt}function kt(Z,tt){var it=Z.data;if(Z.parentNode.tagName==="O:P")return tt.insert(it.trim());if(it.trim().length===0&&Z.parentNode.classList.contains("ql-clipboard"))return tt;if(!Q(Z.parentNode).whiteSpace.startsWith("pre")){var ot=function(xt,bt){return bt=bt.replace(/[^\u00a0]/g,""),bt.length<1&&xt?" ":bt};it=it.replace(/\r\n/g," ").replace(/\n/g," "),it=it.replace(/\s\s+/g,ot.bind(ot,!0)),(Z.previousSibling==null&&M(Z.parentNode)||Z.previousSibling!=null&&M(Z.previousSibling))&&(it=it.replace(/^\s+/,ot.bind(ot,!1))),(Z.nextSibling==null&&M(Z.parentNode)||Z.nextSibling!=null&&M(Z.nextSibling))&&(it=it.replace(/\s+$/,ot.bind(ot,!1)))}return tt.insert(it)}n.default=_,n.matchAttributor=$,n.matchBlot=et,n.matchNewline=gt,n.matchSpacing=Nt,n.matchText=kt},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function y(g,p){for(var d=0;d<p.length;d++){var c=p[d];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(g,c.key,c)}}return function(g,p,d){return p&&y(g.prototype,p),d&&y(g,d),g}}(),t=function y(g,p,d){g===null&&(g=Function.prototype);var c=Object.getOwnPropertyDescriptor(g,p);if(c===void 0){var O=Object.getPrototypeOf(g);return O===null?void 0:y(O,p,d)}else{if("value"in c)return c.value;var v=c.get;return v===void 0?void 0:v.call(d)}},l=e(6),a=s(l);function s(y){return y&&y.__esModule?y:{default:y}}function f(y,g){if(!(y instanceof g))throw new TypeError("Cannot call a class as a function")}function i(y,g){if(!y)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g&&(typeof g=="object"||typeof g=="function")?g:y}function r(y,g){if(typeof g!="function"&&g!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof g);y.prototype=Object.create(g&&g.prototype,{constructor:{value:y,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(y,g):y.__proto__=g)}var u=function(y){r(g,y);function g(){return f(this,g),i(this,(g.__proto__||Object.getPrototypeOf(g)).apply(this,arguments))}return h(g,[{key:"optimize",value:function(d){t(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"optimize",this).call(this,d),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return t(g.__proto__||Object.getPrototypeOf(g),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),g}(a.default);u.blotName="bold",u.tagName=["STRONG","B"],n.default=u},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.addControls=n.default=void 0;var h=function(){function S(A,N){var R=[],C=!0,B=!1,U=void 0;try{for(var z=A[Symbol.iterator](),K;!(C=(K=z.next()).done)&&(R.push(K.value),!(N&&R.length===N));C=!0);}catch(k){B=!0,U=k}finally{try{!C&&z.return&&z.return()}finally{if(B)throw U}}return R}return function(A,N){if(Array.isArray(A))return A;if(Symbol.iterator in Object(A))return S(A,N);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),t=function(){function S(A,N){for(var R=0;R<N.length;R++){var C=N[R];C.enumerable=C.enumerable||!1,C.configurable=!0,"value"in C&&(C.writable=!0),Object.defineProperty(A,C.key,C)}}return function(A,N,R){return N&&S(A.prototype,N),R&&S(A,R),A}}(),l=e(2),a=d(l),s=e(0),f=d(s),i=e(5),r=d(i),u=e(10),y=d(u),g=e(9),p=d(g);function d(S){return S&&S.__esModule?S:{default:S}}function c(S,A,N){return A in S?Object.defineProperty(S,A,{value:N,enumerable:!0,configurable:!0,writable:!0}):S[A]=N,S}function O(S,A){if(!(S instanceof A))throw new TypeError("Cannot call a class as a function")}function v(S,A){if(!S)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return A&&(typeof A=="object"||typeof A=="function")?A:S}function E(S,A){if(typeof A!="function"&&A!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof A);S.prototype=Object.create(A&&A.prototype,{constructor:{value:S,enumerable:!1,writable:!0,configurable:!0}}),A&&(Object.setPrototypeOf?Object.setPrototypeOf(S,A):S.__proto__=A)}var T=(0,y.default)("quill:toolbar"),P=function(S){E(A,S);function A(N,R){O(this,A);var C=v(this,(A.__proto__||Object.getPrototypeOf(A)).call(this,N,R));if(Array.isArray(C.options.container)){var B=document.createElement("div");D(B,C.options.container),N.container.parentNode.insertBefore(B,N.container),C.container=B}else typeof C.options.container=="string"?C.container=document.querySelector(C.options.container):C.container=C.options.container;if(!(C.container instanceof HTMLElement)){var U;return U=T.error("Container required for toolbar",C.options),v(C,U)}return C.container.classList.add("ql-toolbar"),C.controls=[],C.handlers={},Object.keys(C.options.handlers).forEach(function(z){C.addHandler(z,C.options.handlers[z])}),[].forEach.call(C.container.querySelectorAll("button, select"),function(z){C.attach(z)}),C.quill.on(r.default.events.EDITOR_CHANGE,function(z,K){z===r.default.events.SELECTION_CHANGE&&C.update(K)}),C.quill.on(r.default.events.SCROLL_OPTIMIZE,function(){var z=C.quill.selection.getRange(),K=h(z,1),k=K[0];C.update(k)}),C}return t(A,[{key:"addHandler",value:function(R,C){this.handlers[R]=C}},{key:"attach",value:function(R){var C=this,B=[].find.call(R.classList,function(z){return z.indexOf("ql-")===0});if(B){if(B=B.slice(3),R.tagName==="BUTTON"&&R.setAttribute("type","button"),this.handlers[B]==null){if(this.quill.scroll.whitelist!=null&&this.quill.scroll.whitelist[B]==null){T.warn("ignoring attaching to disabled format",B,R);return}if(f.default.query(B)==null){T.warn("ignoring attaching to nonexistent format",B,R);return}}var U=R.tagName==="SELECT"?"change":"click";R.addEventListener(U,function(z){var K=void 0;if(R.tagName==="SELECT"){if(R.selectedIndex<0)return;var k=R.options[R.selectedIndex];k.hasAttribute("selected")?K=!1:K=k.value||!1}else R.classList.contains("ql-active")?K=!1:K=R.value||!R.hasAttribute("value"),z.preventDefault();C.quill.focus();var L=C.quill.selection.getRange(),_=h(L,1),F=_[0];if(C.handlers[B]!=null)C.handlers[B].call(C,K);else if(f.default.query(B).prototype instanceof f.default.Embed){if(K=prompt("Enter "+B),!K)return;C.quill.updateContents(new a.default().retain(F.index).delete(F.length).insert(c({},B,K)),r.default.sources.USER)}else C.quill.format(B,K,r.default.sources.USER);C.update(F)}),this.controls.push([B,R])}}},{key:"update",value:function(R){var C=R==null?{}:this.quill.getFormat(R);this.controls.forEach(function(B){var U=h(B,2),z=U[0],K=U[1];if(K.tagName==="SELECT"){var k=void 0;if(R==null)k=null;else if(C[z]==null)k=K.querySelector("option[selected]");else if(!Array.isArray(C[z])){var L=C[z];typeof L=="string"&&(L=L.replace(/\"/g,'\\"')),k=K.querySelector('option[value="'+L+'"]')}k==null?(K.value="",K.selectedIndex=-1):k.selected=!0}else if(R==null)K.classList.remove("ql-active");else if(K.hasAttribute("value")){var _=C[z]===K.getAttribute("value")||C[z]!=null&&C[z].toString()===K.getAttribute("value")||C[z]==null&&!K.getAttribute("value");K.classList.toggle("ql-active",_)}else K.classList.toggle("ql-active",C[z]!=null)})}}]),A}(p.default);P.DEFAULTS={};function I(S,A,N){var R=document.createElement("button");R.setAttribute("type","button"),R.classList.add("ql-"+A),N!=null&&(R.value=N),S.appendChild(R)}function D(S,A){Array.isArray(A[0])||(A=[A]),A.forEach(function(N){var R=document.createElement("span");R.classList.add("ql-formats"),N.forEach(function(C){if(typeof C=="string")I(R,C);else{var B=Object.keys(C)[0],U=C[B];Array.isArray(U)?w(R,B,U):I(R,B,U)}}),S.appendChild(R)})}function w(S,A,N){var R=document.createElement("select");R.classList.add("ql-"+A),N.forEach(function(C){var B=document.createElement("option");C!==!1?B.setAttribute("value",C):B.setAttribute("selected","selected"),R.appendChild(B)}),S.appendChild(R)}P.DEFAULTS={container:null,handlers:{clean:function(){var A=this,N=this.quill.getSelection();if(N!=null)if(N.length==0){var R=this.quill.getFormat();Object.keys(R).forEach(function(C){f.default.query(C,f.default.Scope.INLINE)!=null&&A.quill.format(C,!1)})}else this.quill.removeFormat(N,r.default.sources.USER)},direction:function(A){var N=this.quill.getFormat().align;A==="rtl"&&N==null?this.quill.format("align","right",r.default.sources.USER):!A&&N==="right"&&this.quill.format("align",!1,r.default.sources.USER),this.quill.format("direction",A,r.default.sources.USER)},indent:function(A){var N=this.quill.getSelection(),R=this.quill.getFormat(N),C=parseInt(R.indent||0);if(A==="+1"||A==="-1"){var B=A==="+1"?1:-1;R.direction==="rtl"&&(B*=-1),this.quill.format("indent",C+B,r.default.sources.USER)}},link:function(A){A===!0&&(A=prompt("Enter link URL:")),this.quill.format("link",A,r.default.sources.USER)},list:function(A){var N=this.quill.getSelection(),R=this.quill.getFormat(N);A==="check"?R.list==="checked"||R.list==="unchecked"?this.quill.format("list",!1,r.default.sources.USER):this.quill.format("list","unchecked",r.default.sources.USER):this.quill.format("list",A,r.default.sources.USER)}}},n.default=P,n.addControls=D},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function y(g,p){for(var d=0;d<p.length;d++){var c=p[d];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(g,c.key,c)}}return function(g,p,d){return p&&y(g.prototype,p),d&&y(g,d),g}}(),t=function y(g,p,d){g===null&&(g=Function.prototype);var c=Object.getOwnPropertyDescriptor(g,p);if(c===void 0){var O=Object.getPrototypeOf(g);return O===null?void 0:y(O,p,d)}else{if("value"in c)return c.value;var v=c.get;return v===void 0?void 0:v.call(d)}},l=e(28),a=s(l);function s(y){return y&&y.__esModule?y:{default:y}}function f(y,g){if(!(y instanceof g))throw new TypeError("Cannot call a class as a function")}function i(y,g){if(!y)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g&&(typeof g=="object"||typeof g=="function")?g:y}function r(y,g){if(typeof g!="function"&&g!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof g);y.prototype=Object.create(g&&g.prototype,{constructor:{value:y,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(y,g):y.__proto__=g)}var u=function(y){r(g,y);function g(p,d){f(this,g);var c=i(this,(g.__proto__||Object.getPrototypeOf(g)).call(this,p));return c.label.innerHTML=d,c.container.classList.add("ql-color-picker"),[].slice.call(c.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(O){O.classList.add("ql-primary")}),c}return h(g,[{key:"buildItem",value:function(d){var c=t(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"buildItem",this).call(this,d);return c.style.backgroundColor=d.getAttribute("value")||"",c}},{key:"selectItem",value:function(d,c){t(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"selectItem",this).call(this,d,c);var O=this.label.querySelector(".ql-color-label"),v=d&&d.getAttribute("data-value")||"";O&&(O.tagName==="line"?O.style.stroke=v:O.style.fill=v)}}]),g}(a.default);n.default=u},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function y(g,p){for(var d=0;d<p.length;d++){var c=p[d];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(g,c.key,c)}}return function(g,p,d){return p&&y(g.prototype,p),d&&y(g,d),g}}(),t=function y(g,p,d){g===null&&(g=Function.prototype);var c=Object.getOwnPropertyDescriptor(g,p);if(c===void 0){var O=Object.getPrototypeOf(g);return O===null?void 0:y(O,p,d)}else{if("value"in c)return c.value;var v=c.get;return v===void 0?void 0:v.call(d)}},l=e(28),a=s(l);function s(y){return y&&y.__esModule?y:{default:y}}function f(y,g){if(!(y instanceof g))throw new TypeError("Cannot call a class as a function")}function i(y,g){if(!y)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g&&(typeof g=="object"||typeof g=="function")?g:y}function r(y,g){if(typeof g!="function"&&g!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof g);y.prototype=Object.create(g&&g.prototype,{constructor:{value:y,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(y,g):y.__proto__=g)}var u=function(y){r(g,y);function g(p,d){f(this,g);var c=i(this,(g.__proto__||Object.getPrototypeOf(g)).call(this,p));return c.container.classList.add("ql-icon-picker"),[].forEach.call(c.container.querySelectorAll(".ql-picker-item"),function(O){O.innerHTML=d[O.getAttribute("data-value")||""]}),c.defaultItem=c.container.querySelector(".ql-selected"),c.selectItem(c.defaultItem),c}return h(g,[{key:"selectItem",value:function(d,c){t(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"selectItem",this).call(this,d,c),d=d||this.defaultItem,this.label.innerHTML=d.innerHTML}}]),g}(a.default);n.default=u},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function a(s,f){for(var i=0;i<f.length;i++){var r=f[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(s,r.key,r)}}return function(s,f,i){return f&&a(s.prototype,f),i&&a(s,i),s}}();function t(a,s){if(!(a instanceof s))throw new TypeError("Cannot call a class as a function")}var l=function(){function a(s,f){var i=this;t(this,a),this.quill=s,this.boundsContainer=f||document.body,this.root=s.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){i.root.style.marginTop=-1*i.quill.root.scrollTop+"px"}),this.hide()}return h(a,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(f){var i=f.left+f.width/2-this.root.offsetWidth/2,r=f.bottom+this.quill.root.scrollTop;this.root.style.left=i+"px",this.root.style.top=r+"px",this.root.classList.remove("ql-flip");var u=this.boundsContainer.getBoundingClientRect(),y=this.root.getBoundingClientRect(),g=0;if(y.right>u.right&&(g=u.right-y.right,this.root.style.left=i+g+"px"),y.left<u.left&&(g=u.left-y.left,this.root.style.left=i+g+"px"),y.bottom>u.bottom){var p=y.bottom-y.top,d=f.bottom-f.top+p;this.root.style.top=r-d+"px",this.root.classList.add("ql-flip")}return g}},{key:"show",value:function(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}]),a}();n.default=l},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function w(S,A){var N=[],R=!0,C=!1,B=void 0;try{for(var U=S[Symbol.iterator](),z;!(R=(z=U.next()).done)&&(N.push(z.value),!(A&&N.length===A));R=!0);}catch(K){C=!0,B=K}finally{try{!R&&U.return&&U.return()}finally{if(C)throw B}}return N}return function(S,A){if(Array.isArray(S))return S;if(Symbol.iterator in Object(S))return w(S,A);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),t=function w(S,A,N){S===null&&(S=Function.prototype);var R=Object.getOwnPropertyDescriptor(S,A);if(R===void 0){var C=Object.getPrototypeOf(S);return C===null?void 0:w(C,A,N)}else{if("value"in R)return R.value;var B=R.get;return B===void 0?void 0:B.call(N)}},l=function(){function w(S,A){for(var N=0;N<A.length;N++){var R=A[N];R.enumerable=R.enumerable||!1,R.configurable=!0,"value"in R&&(R.writable=!0),Object.defineProperty(S,R.key,R)}}return function(S,A,N){return A&&w(S.prototype,A),N&&w(S,N),S}}(),a=e(3),s=O(a),f=e(8),i=O(f),r=e(43),u=O(r),y=e(27),g=O(y),p=e(15),d=e(41),c=O(d);function O(w){return w&&w.__esModule?w:{default:w}}function v(w,S){if(!(w instanceof S))throw new TypeError("Cannot call a class as a function")}function E(w,S){if(!w)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return S&&(typeof S=="object"||typeof S=="function")?S:w}function T(w,S){if(typeof S!="function"&&S!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof S);w.prototype=Object.create(S&&S.prototype,{constructor:{value:w,enumerable:!1,writable:!0,configurable:!0}}),S&&(Object.setPrototypeOf?Object.setPrototypeOf(w,S):w.__proto__=S)}var P=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],I=function(w){T(S,w);function S(A,N){v(this,S),N.modules.toolbar!=null&&N.modules.toolbar.container==null&&(N.modules.toolbar.container=P);var R=E(this,(S.__proto__||Object.getPrototypeOf(S)).call(this,A,N));return R.quill.container.classList.add("ql-snow"),R}return l(S,[{key:"extendToolbar",value:function(N){N.container.classList.add("ql-snow"),this.buildButtons([].slice.call(N.container.querySelectorAll("button")),c.default),this.buildPickers([].slice.call(N.container.querySelectorAll("select")),c.default),this.tooltip=new D(this.quill,this.options.bounds),N.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(R,C){N.handlers.link.call(N,!C.format.link)})}}]),S}(u.default);I.DEFAULTS=(0,s.default)(!0,{},u.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(S){if(S){var A=this.quill.getSelection();if(A==null||A.length==0)return;var N=this.quill.getText(A);/^\S+@\S+\.\S+$/.test(N)&&N.indexOf("mailto:")!==0&&(N="mailto:"+N);var R=this.quill.theme.tooltip;R.edit("link",N)}else this.quill.format("link",!1)}}}}});var D=function(w){T(S,w);function S(A,N){v(this,S);var R=E(this,(S.__proto__||Object.getPrototypeOf(S)).call(this,A,N));return R.preview=R.root.querySelector("a.ql-preview"),R}return l(S,[{key:"listen",value:function(){var N=this;t(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"listen",this).call(this),this.root.querySelector("a.ql-action").addEventListener("click",function(R){N.root.classList.contains("ql-editing")?N.save():N.edit("link",N.preview.textContent),R.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",function(R){if(N.linkRange!=null){var C=N.linkRange;N.restoreFocus(),N.quill.formatText(C,"link",!1,i.default.sources.USER),delete N.linkRange}R.preventDefault(),N.hide()}),this.quill.on(i.default.events.SELECTION_CHANGE,function(R,C,B){if(R!=null){if(R.length===0&&B===i.default.sources.USER){var U=N.quill.scroll.descendant(g.default,R.index),z=h(U,2),K=z[0],k=z[1];if(K!=null){N.linkRange=new p.Range(R.index-k,K.length());var L=g.default.formats(K.domNode);N.preview.textContent=L,N.preview.setAttribute("href",L),N.show(),N.position(N.quill.getBounds(N.linkRange));return}}else delete N.linkRange;N.hide()}})}},{key:"show",value:function(){t(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"show",this).call(this),this.root.removeAttribute("data-mode")}}]),S}(r.BaseTooltip);D.TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""),n.default=I},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=e(29),t=nt(h),l=e(36),a=e(38),s=e(64),f=e(65),i=nt(f),r=e(66),u=nt(r),y=e(67),g=nt(y),p=e(37),d=e(26),c=e(39),O=e(40),v=e(56),E=nt(v),T=e(68),P=nt(T),I=e(27),D=nt(I),w=e(69),S=nt(w),A=e(70),N=nt(A),R=e(71),C=nt(R),B=e(72),U=nt(B),z=e(73),K=nt(z),k=e(13),L=nt(k),_=e(74),F=nt(_),Q=e(75),q=nt(Q),M=e(57),H=nt(M),W=e(41),$=nt(W),et=e(28),ut=nt(et),pt=e(59),dt=nt(pt),gt=e(60),Nt=nt(gt),qt=e(61),kt=nt(qt),Z=e(108),tt=nt(Z),it=e(62),ot=nt(it);function nt(xt){return xt&&xt.__esModule?xt:{default:xt}}t.default.register({"attributors/attribute/direction":a.DirectionAttribute,"attributors/class/align":l.AlignClass,"attributors/class/background":p.BackgroundClass,"attributors/class/color":d.ColorClass,"attributors/class/direction":a.DirectionClass,"attributors/class/font":c.FontClass,"attributors/class/size":O.SizeClass,"attributors/style/align":l.AlignStyle,"attributors/style/background":p.BackgroundStyle,"attributors/style/color":d.ColorStyle,"attributors/style/direction":a.DirectionStyle,"attributors/style/font":c.FontStyle,"attributors/style/size":O.SizeStyle},!0),t.default.register({"formats/align":l.AlignClass,"formats/direction":a.DirectionClass,"formats/indent":s.IndentClass,"formats/background":p.BackgroundStyle,"formats/color":d.ColorStyle,"formats/font":c.FontClass,"formats/size":O.SizeClass,"formats/blockquote":i.default,"formats/code-block":L.default,"formats/header":u.default,"formats/list":g.default,"formats/bold":E.default,"formats/code":k.Code,"formats/italic":P.default,"formats/link":D.default,"formats/script":S.default,"formats/strike":N.default,"formats/underline":C.default,"formats/image":U.default,"formats/video":K.default,"formats/list/item":y.ListItem,"modules/formula":F.default,"modules/syntax":q.default,"modules/toolbar":H.default,"themes/bubble":tt.default,"themes/snow":ot.default,"ui/icons":$.default,"ui/picker":ut.default,"ui/icon-picker":Nt.default,"ui/color-picker":dt.default,"ui/tooltip":kt.default},!0),n.default=t.default},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.IndentClass=void 0;var h=function(){function g(p,d){for(var c=0;c<d.length;c++){var O=d[c];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(p,O.key,O)}}return function(p,d,c){return d&&g(p.prototype,d),c&&g(p,c),p}}(),t=function g(p,d,c){p===null&&(p=Function.prototype);var O=Object.getOwnPropertyDescriptor(p,d);if(O===void 0){var v=Object.getPrototypeOf(p);return v===null?void 0:g(v,d,c)}else{if("value"in O)return O.value;var E=O.get;return E===void 0?void 0:E.call(c)}},l=e(0),a=s(l);function s(g){return g&&g.__esModule?g:{default:g}}function f(g,p){if(!(g instanceof p))throw new TypeError("Cannot call a class as a function")}function i(g,p){if(!g)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return p&&(typeof p=="object"||typeof p=="function")?p:g}function r(g,p){if(typeof p!="function"&&p!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof p);g.prototype=Object.create(p&&p.prototype,{constructor:{value:g,enumerable:!1,writable:!0,configurable:!0}}),p&&(Object.setPrototypeOf?Object.setPrototypeOf(g,p):g.__proto__=p)}var u=function(g){r(p,g);function p(){return f(this,p),i(this,(p.__proto__||Object.getPrototypeOf(p)).apply(this,arguments))}return h(p,[{key:"add",value:function(c,O){if(O==="+1"||O==="-1"){var v=this.value(c)||0;O=O==="+1"?v+1:v-1}return O===0?(this.remove(c),!0):t(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"add",this).call(this,c,O)}},{key:"canAdd",value:function(c,O){return t(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"canAdd",this).call(this,c,O)||t(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"canAdd",this).call(this,c,parseInt(O))}},{key:"value",value:function(c){return parseInt(t(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"value",this).call(this,c))||void 0}}]),p}(a.default.Attributor.Class),y=new u("indent","ql-indent",{scope:a.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});n.IndentClass=y},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=e(4),t=l(h);function l(r){return r&&r.__esModule?r:{default:r}}function a(r,u){if(!(r instanceof u))throw new TypeError("Cannot call a class as a function")}function s(r,u){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u&&(typeof u=="object"||typeof u=="function")?u:r}function f(r,u){if(typeof u!="function"&&u!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof u);r.prototype=Object.create(u&&u.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),u&&(Object.setPrototypeOf?Object.setPrototypeOf(r,u):r.__proto__=u)}var i=function(r){f(u,r);function u(){return a(this,u),s(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}return u}(t.default);i.blotName="blockquote",i.tagName="blockquote",n.default=i},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function u(y,g){for(var p=0;p<g.length;p++){var d=g[p];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(y,d.key,d)}}return function(y,g,p){return g&&u(y.prototype,g),p&&u(y,p),y}}(),t=e(4),l=a(t);function a(u){return u&&u.__esModule?u:{default:u}}function s(u,y){if(!(u instanceof y))throw new TypeError("Cannot call a class as a function")}function f(u,y){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return y&&(typeof y=="object"||typeof y=="function")?y:u}function i(u,y){if(typeof y!="function"&&y!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof y);u.prototype=Object.create(y&&y.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),y&&(Object.setPrototypeOf?Object.setPrototypeOf(u,y):u.__proto__=y)}var r=function(u){i(y,u);function y(){return s(this,y),f(this,(y.__proto__||Object.getPrototypeOf(y)).apply(this,arguments))}return h(y,null,[{key:"formats",value:function(p){return this.tagName.indexOf(p.tagName)+1}}]),y}(l.default);r.blotName="header",r.tagName=["H1","H2","H3","H4","H5","H6"],n.default=r},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=n.ListItem=void 0;var h=function(){function v(E,T){for(var P=0;P<T.length;P++){var I=T[P];I.enumerable=I.enumerable||!1,I.configurable=!0,"value"in I&&(I.writable=!0),Object.defineProperty(E,I.key,I)}}return function(E,T,P){return T&&v(E.prototype,T),P&&v(E,P),E}}(),t=function v(E,T,P){E===null&&(E=Function.prototype);var I=Object.getOwnPropertyDescriptor(E,T);if(I===void 0){var D=Object.getPrototypeOf(E);return D===null?void 0:v(D,T,P)}else{if("value"in I)return I.value;var w=I.get;return w===void 0?void 0:w.call(P)}},l=e(0),a=u(l),s=e(4),f=u(s),i=e(25),r=u(i);function u(v){return v&&v.__esModule?v:{default:v}}function y(v,E,T){return E in v?Object.defineProperty(v,E,{value:T,enumerable:!0,configurable:!0,writable:!0}):v[E]=T,v}function g(v,E){if(!(v instanceof E))throw new TypeError("Cannot call a class as a function")}function p(v,E){if(!v)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E&&(typeof E=="object"||typeof E=="function")?E:v}function d(v,E){if(typeof E!="function"&&E!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof E);v.prototype=Object.create(E&&E.prototype,{constructor:{value:v,enumerable:!1,writable:!0,configurable:!0}}),E&&(Object.setPrototypeOf?Object.setPrototypeOf(v,E):v.__proto__=E)}var c=function(v){d(E,v);function E(){return g(this,E),p(this,(E.__proto__||Object.getPrototypeOf(E)).apply(this,arguments))}return h(E,[{key:"format",value:function(P,I){P===O.blotName&&!I?this.replaceWith(a.default.create(this.statics.scope)):t(E.prototype.__proto__||Object.getPrototypeOf(E.prototype),"format",this).call(this,P,I)}},{key:"remove",value:function(){this.prev==null&&this.next==null?this.parent.remove():t(E.prototype.__proto__||Object.getPrototypeOf(E.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(P,I){return this.parent.isolate(this.offset(this.parent),this.length()),P===this.parent.statics.blotName?(this.parent.replaceWith(P,I),this):(this.parent.unwrap(),t(E.prototype.__proto__||Object.getPrototypeOf(E.prototype),"replaceWith",this).call(this,P,I))}}],[{key:"formats",value:function(P){return P.tagName===this.tagName?void 0:t(E.__proto__||Object.getPrototypeOf(E),"formats",this).call(this,P)}}]),E}(f.default);c.blotName="list-item",c.tagName="LI";var O=function(v){d(E,v),h(E,null,[{key:"create",value:function(P){var I=P==="ordered"?"OL":"UL",D=t(E.__proto__||Object.getPrototypeOf(E),"create",this).call(this,I);return(P==="checked"||P==="unchecked")&&D.setAttribute("data-checked",P==="checked"),D}},{key:"formats",value:function(P){if(P.tagName==="OL")return"ordered";if(P.tagName==="UL")return P.hasAttribute("data-checked")?P.getAttribute("data-checked")==="true"?"checked":"unchecked":"bullet"}}]);function E(T){g(this,E);var P=p(this,(E.__proto__||Object.getPrototypeOf(E)).call(this,T)),I=function(w){if(w.target.parentNode===T){var S=P.statics.formats(T),A=a.default.find(w.target);S==="checked"?A.format("list","unchecked"):S==="unchecked"&&A.format("list","checked")}};return T.addEventListener("touchstart",I),T.addEventListener("mousedown",I),P}return h(E,[{key:"format",value:function(P,I){this.children.length>0&&this.children.tail.format(P,I)}},{key:"formats",value:function(){return y({},this.statics.blotName,this.statics.formats(this.domNode))}},{key:"insertBefore",value:function(P,I){if(P instanceof c)t(E.prototype.__proto__||Object.getPrototypeOf(E.prototype),"insertBefore",this).call(this,P,I);else{var D=I==null?this.length():I.offset(this),w=this.split(D);w.parent.insertBefore(P,w)}}},{key:"optimize",value:function(P){t(E.prototype.__proto__||Object.getPrototypeOf(E.prototype),"optimize",this).call(this,P);var I=this.next;I!=null&&I.prev===this&&I.statics.blotName===this.statics.blotName&&I.domNode.tagName===this.domNode.tagName&&I.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(I.moveChildren(this),I.remove())}},{key:"replace",value:function(P){if(P.statics.blotName!==this.statics.blotName){var I=a.default.create(this.statics.defaultChild);P.moveChildren(I),this.appendChild(I)}t(E.prototype.__proto__||Object.getPrototypeOf(E.prototype),"replace",this).call(this,P)}}]),E}(r.default);O.blotName="list",O.scope=a.default.Scope.BLOCK_BLOT,O.tagName=["OL","UL"],O.defaultChild="list-item",O.allowedChildren=[c],n.ListItem=c,n.default=O},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=e(56),t=l(h);function l(r){return r&&r.__esModule?r:{default:r}}function a(r,u){if(!(r instanceof u))throw new TypeError("Cannot call a class as a function")}function s(r,u){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u&&(typeof u=="object"||typeof u=="function")?u:r}function f(r,u){if(typeof u!="function"&&u!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof u);r.prototype=Object.create(u&&u.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),u&&(Object.setPrototypeOf?Object.setPrototypeOf(r,u):r.__proto__=u)}var i=function(r){f(u,r);function u(){return a(this,u),s(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}return u}(t.default);i.blotName="italic",i.tagName=["EM","I"],n.default=i},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function y(g,p){for(var d=0;d<p.length;d++){var c=p[d];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(g,c.key,c)}}return function(g,p,d){return p&&y(g.prototype,p),d&&y(g,d),g}}(),t=function y(g,p,d){g===null&&(g=Function.prototype);var c=Object.getOwnPropertyDescriptor(g,p);if(c===void 0){var O=Object.getPrototypeOf(g);return O===null?void 0:y(O,p,d)}else{if("value"in c)return c.value;var v=c.get;return v===void 0?void 0:v.call(d)}},l=e(6),a=s(l);function s(y){return y&&y.__esModule?y:{default:y}}function f(y,g){if(!(y instanceof g))throw new TypeError("Cannot call a class as a function")}function i(y,g){if(!y)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g&&(typeof g=="object"||typeof g=="function")?g:y}function r(y,g){if(typeof g!="function"&&g!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof g);y.prototype=Object.create(g&&g.prototype,{constructor:{value:y,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(y,g):y.__proto__=g)}var u=function(y){r(g,y);function g(){return f(this,g),i(this,(g.__proto__||Object.getPrototypeOf(g)).apply(this,arguments))}return h(g,null,[{key:"create",value:function(d){return d==="super"?document.createElement("sup"):d==="sub"?document.createElement("sub"):t(g.__proto__||Object.getPrototypeOf(g),"create",this).call(this,d)}},{key:"formats",value:function(d){if(d.tagName==="SUB")return"sub";if(d.tagName==="SUP")return"super"}}]),g}(a.default);u.blotName="script",u.tagName=["SUB","SUP"],n.default=u},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=e(6),t=l(h);function l(r){return r&&r.__esModule?r:{default:r}}function a(r,u){if(!(r instanceof u))throw new TypeError("Cannot call a class as a function")}function s(r,u){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u&&(typeof u=="object"||typeof u=="function")?u:r}function f(r,u){if(typeof u!="function"&&u!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof u);r.prototype=Object.create(u&&u.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),u&&(Object.setPrototypeOf?Object.setPrototypeOf(r,u):r.__proto__=u)}var i=function(r){f(u,r);function u(){return a(this,u),s(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}return u}(t.default);i.blotName="strike",i.tagName="S",n.default=i},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=e(6),t=l(h);function l(r){return r&&r.__esModule?r:{default:r}}function a(r,u){if(!(r instanceof u))throw new TypeError("Cannot call a class as a function")}function s(r,u){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u&&(typeof u=="object"||typeof u=="function")?u:r}function f(r,u){if(typeof u!="function"&&u!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof u);r.prototype=Object.create(u&&u.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),u&&(Object.setPrototypeOf?Object.setPrototypeOf(r,u):r.__proto__=u)}var i=function(r){f(u,r);function u(){return a(this,u),s(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}return u}(t.default);i.blotName="underline",i.tagName="U",n.default=i},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function p(d,c){for(var O=0;O<c.length;O++){var v=c[O];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(d,v.key,v)}}return function(d,c,O){return c&&p(d.prototype,c),O&&p(d,O),d}}(),t=function p(d,c,O){d===null&&(d=Function.prototype);var v=Object.getOwnPropertyDescriptor(d,c);if(v===void 0){var E=Object.getPrototypeOf(d);return E===null?void 0:p(E,c,O)}else{if("value"in v)return v.value;var T=v.get;return T===void 0?void 0:T.call(O)}},l=e(0),a=f(l),s=e(27);function f(p){return p&&p.__esModule?p:{default:p}}function i(p,d){if(!(p instanceof d))throw new TypeError("Cannot call a class as a function")}function r(p,d){if(!p)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return d&&(typeof d=="object"||typeof d=="function")?d:p}function u(p,d){if(typeof d!="function"&&d!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof d);p.prototype=Object.create(d&&d.prototype,{constructor:{value:p,enumerable:!1,writable:!0,configurable:!0}}),d&&(Object.setPrototypeOf?Object.setPrototypeOf(p,d):p.__proto__=d)}var y=["alt","height","width"],g=function(p){u(d,p);function d(){return i(this,d),r(this,(d.__proto__||Object.getPrototypeOf(d)).apply(this,arguments))}return h(d,[{key:"format",value:function(O,v){y.indexOf(O)>-1?v?this.domNode.setAttribute(O,v):this.domNode.removeAttribute(O):t(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"format",this).call(this,O,v)}}],[{key:"create",value:function(O){var v=t(d.__proto__||Object.getPrototypeOf(d),"create",this).call(this,O);return typeof O=="string"&&v.setAttribute("src",this.sanitize(O)),v}},{key:"formats",value:function(O){return y.reduce(function(v,E){return O.hasAttribute(E)&&(v[E]=O.getAttribute(E)),v},{})}},{key:"match",value:function(O){return/\.(jpe?g|gif|png)$/.test(O)||/^data:image\/.+;base64/.test(O)}},{key:"sanitize",value:function(O){return(0,s.sanitize)(O,["http","https","data"])?O:"//:0"}},{key:"value",value:function(O){return O.getAttribute("src")}}]),d}(a.default.Embed);g.blotName="image",g.tagName="IMG",n.default=g},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0});var h=function(){function p(d,c){for(var O=0;O<c.length;O++){var v=c[O];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(d,v.key,v)}}return function(d,c,O){return c&&p(d.prototype,c),O&&p(d,O),d}}(),t=function p(d,c,O){d===null&&(d=Function.prototype);var v=Object.getOwnPropertyDescriptor(d,c);if(v===void 0){var E=Object.getPrototypeOf(d);return E===null?void 0:p(E,c,O)}else{if("value"in v)return v.value;var T=v.get;return T===void 0?void 0:T.call(O)}},l=e(4),a=e(27),s=f(a);function f(p){return p&&p.__esModule?p:{default:p}}function i(p,d){if(!(p instanceof d))throw new TypeError("Cannot call a class as a function")}function r(p,d){if(!p)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return d&&(typeof d=="object"||typeof d=="function")?d:p}function u(p,d){if(typeof d!="function"&&d!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof d);p.prototype=Object.create(d&&d.prototype,{constructor:{value:p,enumerable:!1,writable:!0,configurable:!0}}),d&&(Object.setPrototypeOf?Object.setPrototypeOf(p,d):p.__proto__=d)}var y=["height","width"],g=function(p){u(d,p);function d(){return i(this,d),r(this,(d.__proto__||Object.getPrototypeOf(d)).apply(this,arguments))}return h(d,[{key:"format",value:function(O,v){y.indexOf(O)>-1?v?this.domNode.setAttribute(O,v):this.domNode.removeAttribute(O):t(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"format",this).call(this,O,v)}}],[{key:"create",value:function(O){var v=t(d.__proto__||Object.getPrototypeOf(d),"create",this).call(this,O);return v.setAttribute("frameborder","0"),v.setAttribute("allowfullscreen",!0),v.setAttribute("src",this.sanitize(O)),v}},{key:"formats",value:function(O){return y.reduce(function(v,E){return O.hasAttribute(E)&&(v[E]=O.getAttribute(E)),v},{})}},{key:"sanitize",value:function(O){return s.default.sanitize(O)}},{key:"value",value:function(O){return O.getAttribute("src")}}]),d}(l.BlockEmbed);g.blotName="video",g.className="ql-video",g.tagName="IFRAME",n.default=g},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=n.FormulaBlot=void 0;var h=function(){function O(v,E){for(var T=0;T<E.length;T++){var P=E[T];P.enumerable=P.enumerable||!1,P.configurable=!0,"value"in P&&(P.writable=!0),Object.defineProperty(v,P.key,P)}}return function(v,E,T){return E&&O(v.prototype,E),T&&O(v,T),v}}(),t=function O(v,E,T){v===null&&(v=Function.prototype);var P=Object.getOwnPropertyDescriptor(v,E);if(P===void 0){var I=Object.getPrototypeOf(v);return I===null?void 0:O(I,E,T)}else{if("value"in P)return P.value;var D=P.get;return D===void 0?void 0:D.call(T)}},l=e(35),a=u(l),s=e(5),f=u(s),i=e(9),r=u(i);function u(O){return O&&O.__esModule?O:{default:O}}function y(O,v){if(!(O instanceof v))throw new TypeError("Cannot call a class as a function")}function g(O,v){if(!O)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:O}function p(O,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);O.prototype=Object.create(v&&v.prototype,{constructor:{value:O,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(O,v):O.__proto__=v)}var d=function(O){p(v,O);function v(){return y(this,v),g(this,(v.__proto__||Object.getPrototypeOf(v)).apply(this,arguments))}return h(v,null,[{key:"create",value:function(T){var P=t(v.__proto__||Object.getPrototypeOf(v),"create",this).call(this,T);return typeof T=="string"&&(window.katex.render(T,P,{throwOnError:!1,errorColor:"#f00"}),P.setAttribute("data-value",T)),P}},{key:"value",value:function(T){return T.getAttribute("data-value")}}]),v}(a.default);d.blotName="formula",d.className="ql-formula",d.tagName="SPAN";var c=function(O){p(v,O),h(v,null,[{key:"register",value:function(){f.default.register(d,!0)}}]);function v(){y(this,v);var E=g(this,(v.__proto__||Object.getPrototypeOf(v)).call(this));if(window.katex==null)throw new Error("Formula module requires KaTeX.");return E}return v}(r.default);n.FormulaBlot=d,n.default=c},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=n.CodeToken=n.CodeBlock=void 0;var h=function(){function T(P,I){for(var D=0;D<I.length;D++){var w=I[D];w.enumerable=w.enumerable||!1,w.configurable=!0,"value"in w&&(w.writable=!0),Object.defineProperty(P,w.key,w)}}return function(P,I,D){return I&&T(P.prototype,I),D&&T(P,D),P}}(),t=function T(P,I,D){P===null&&(P=Function.prototype);var w=Object.getOwnPropertyDescriptor(P,I);if(w===void 0){var S=Object.getPrototypeOf(P);return S===null?void 0:T(S,I,D)}else{if("value"in w)return w.value;var A=w.get;return A===void 0?void 0:A.call(D)}},l=e(0),a=g(l),s=e(5),f=g(s),i=e(9),r=g(i),u=e(13),y=g(u);function g(T){return T&&T.__esModule?T:{default:T}}function p(T,P){if(!(T instanceof P))throw new TypeError("Cannot call a class as a function")}function d(T,P){if(!T)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return P&&(typeof P=="object"||typeof P=="function")?P:T}function c(T,P){if(typeof P!="function"&&P!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof P);T.prototype=Object.create(P&&P.prototype,{constructor:{value:T,enumerable:!1,writable:!0,configurable:!0}}),P&&(Object.setPrototypeOf?Object.setPrototypeOf(T,P):T.__proto__=P)}var O=function(T){c(P,T);function P(){return p(this,P),d(this,(P.__proto__||Object.getPrototypeOf(P)).apply(this,arguments))}return h(P,[{key:"replaceWith",value:function(D){this.domNode.textContent=this.domNode.textContent,this.attach(),t(P.prototype.__proto__||Object.getPrototypeOf(P.prototype),"replaceWith",this).call(this,D)}},{key:"highlight",value:function(D){var w=this.domNode.textContent;this.cachedText!==w&&((w.trim().length>0||this.cachedText==null)&&(this.domNode.innerHTML=D(w),this.domNode.normalize(),this.attach()),this.cachedText=w)}}]),P}(y.default);O.className="ql-syntax";var v=new a.default.Attributor.Class("token","hljs",{scope:a.default.Scope.INLINE}),E=function(T){c(P,T),h(P,null,[{key:"register",value:function(){f.default.register(v,!0),f.default.register(O,!0)}}]);function P(I,D){p(this,P);var w=d(this,(P.__proto__||Object.getPrototypeOf(P)).call(this,I,D));if(typeof w.options.highlight!="function")throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var S=null;return w.quill.on(f.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(S),S=setTimeout(function(){w.highlight(),S=null},w.options.interval)}),w.highlight(),w}return h(P,[{key:"highlight",value:function(){var D=this;if(!this.quill.selection.composing){this.quill.update(f.default.sources.USER);var w=this.quill.getSelection();this.quill.scroll.descendants(O).forEach(function(S){S.highlight(D.options.highlight)}),this.quill.update(f.default.sources.SILENT),w!=null&&this.quill.setSelection(w,f.default.sources.SILENT)}}}]),P}(r.default);E.DEFAULTS={highlight:function(){return window.hljs==null?null:function(T){var P=window.hljs.highlightAuto(T);return P.value}}(),interval:1e3},n.CodeBlock=O,n.CodeToken=v,n.default=E},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},function(o,n){o.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},function(o,n){o.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},function(o,n){o.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(o,n){o.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},function(o,n){o.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(o,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=n.BubbleTooltip=void 0;var h=function P(I,D,w){I===null&&(I=Function.prototype);var S=Object.getOwnPropertyDescriptor(I,D);if(S===void 0){var A=Object.getPrototypeOf(I);return A===null?void 0:P(A,D,w)}else{if("value"in S)return S.value;var N=S.get;return N===void 0?void 0:N.call(w)}},t=function(){function P(I,D){for(var w=0;w<D.length;w++){var S=D[w];S.enumerable=S.enumerable||!1,S.configurable=!0,"value"in S&&(S.writable=!0),Object.defineProperty(I,S.key,S)}}return function(I,D,w){return D&&P(I.prototype,D),w&&P(I,w),I}}(),l=e(3),a=p(l),s=e(8),f=p(s),i=e(43),r=p(i),u=e(15),y=e(41),g=p(y);function p(P){return P&&P.__esModule?P:{default:P}}function d(P,I){if(!(P instanceof I))throw new TypeError("Cannot call a class as a function")}function c(P,I){if(!P)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return I&&(typeof I=="object"||typeof I=="function")?I:P}function O(P,I){if(typeof I!="function"&&I!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof I);P.prototype=Object.create(I&&I.prototype,{constructor:{value:P,enumerable:!1,writable:!0,configurable:!0}}),I&&(Object.setPrototypeOf?Object.setPrototypeOf(P,I):P.__proto__=I)}var v=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],E=function(P){O(I,P);function I(D,w){d(this,I),w.modules.toolbar!=null&&w.modules.toolbar.container==null&&(w.modules.toolbar.container=v);var S=c(this,(I.__proto__||Object.getPrototypeOf(I)).call(this,D,w));return S.quill.container.classList.add("ql-bubble"),S}return t(I,[{key:"extendToolbar",value:function(w){this.tooltip=new T(this.quill,this.options.bounds),this.tooltip.root.appendChild(w.container),this.buildButtons([].slice.call(w.container.querySelectorAll("button")),g.default),this.buildPickers([].slice.call(w.container.querySelectorAll("select")),g.default)}}]),I}(r.default);E.DEFAULTS=(0,a.default)(!0,{},r.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(I){I?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var T=function(P){O(I,P);function I(D,w){d(this,I);var S=c(this,(I.__proto__||Object.getPrototypeOf(I)).call(this,D,w));return S.quill.on(f.default.events.EDITOR_CHANGE,function(A,N,R,C){if(A===f.default.events.SELECTION_CHANGE)if(N!=null&&N.length>0&&C===f.default.sources.USER){S.show(),S.root.style.left="0px",S.root.style.width="",S.root.style.width=S.root.offsetWidth+"px";var B=S.quill.getLines(N.index,N.length);if(B.length===1)S.position(S.quill.getBounds(N));else{var U=B[B.length-1],z=S.quill.getIndex(U),K=Math.min(U.length()-1,N.index+N.length-z),k=S.quill.getBounds(new u.Range(z,K));S.position(k)}}else document.activeElement!==S.textbox&&S.quill.hasFocus()&&S.hide()}),S}return t(I,[{key:"listen",value:function(){var w=this;h(I.prototype.__proto__||Object.getPrototypeOf(I.prototype),"listen",this).call(this),this.root.querySelector(".ql-close").addEventListener("click",function(){w.root.classList.remove("ql-editing")}),this.quill.on(f.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){if(!w.root.classList.contains("ql-hidden")){var S=w.quill.getSelection();S!=null&&w.position(w.quill.getBounds(S))}},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(w){var S=h(I.prototype.__proto__||Object.getPrototypeOf(I.prototype),"position",this).call(this,w),A=this.root.querySelector(".ql-tooltip-arrow");if(A.style.marginLeft="",S===0)return S;A.style.marginLeft=-1*S-A.offsetWidth/2+"px"}}]),I}(i.BaseTooltip);T.TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""),n.BubbleTooltip=T,n.default=E},function(o,n,e){o.exports=e(63)}]).default})})(Do);var Tl=Do.exports;const Ri=qi(Tl);var qe=-1,Me=1,Ae=0;function $n(b,m,o,n){if(b===m)return b?[[Ae,b]]:[];if(o!=null){var e=Il(b,m,o);if(e)return e}var h=Qi(b,m),t=b.substring(0,h);b=b.substring(h),m=m.substring(h),h=Wi(b,m);var l=b.substring(b.length-h);b=b.substring(0,b.length-h),m=m.substring(0,m.length-h);var a=Al(b,m);return t&&a.unshift([Ae,t]),l&&a.push([Ae,l]),Ro(a,n),a}function Al(b,m){var o;if(!b)return[[Me,m]];if(!m)return[[qe,b]];var n=b.length>m.length?b:m,e=b.length>m.length?m:b,h=n.indexOf(e);if(h!==-1)return o=[[Me,n.substring(0,h)],[Ae,e],[Me,n.substring(h+e.length)]],b.length>m.length&&(o[0][0]=o[2][0]=qe),o;if(e.length===1)return[[qe,b],[Me,m]];var t=Pl(b,m);if(t){var l=t[0],a=t[1],s=t[2],f=t[3],i=t[4],r=$n(l,s),u=$n(a,f);return r.concat([[Ae,i]],u)}return wl(b,m)}function wl(b,m){for(var o=b.length,n=m.length,e=Math.ceil((o+n)/2),h=e,t=2*e,l=new Array(t),a=new Array(t),s=0;s<t;s++)l[s]=-1,a[s]=-1;l[h+1]=0,a[h+1]=0;for(var f=o-n,i=f%2!==0,r=0,u=0,y=0,g=0,p=0;p<e;p++){for(var d=-p+r;d<=p-u;d+=2){var c=h+d,O;d===-p||d!==p&&l[c-1]<l[c+1]?O=l[c+1]:O=l[c-1]+1;for(var v=O-d;O<o&&v<n&&b.charAt(O)===m.charAt(v);)O++,v++;if(l[c]=O,O>o)u+=2;else if(v>n)r+=2;else if(i){var E=h+f-d;if(E>=0&&E<t&&a[E]!==-1){var T=o-a[E];if(O>=T)return fo(b,m,O,v)}}}for(var P=-p+y;P<=p-g;P+=2){var E=h+P,T;P===-p||P!==p&&a[E-1]<a[E+1]?T=a[E+1]:T=a[E-1]+1;for(var I=T-P;T<o&&I<n&&b.charAt(o-T-1)===m.charAt(n-I-1);)T++,I++;if(a[E]=T,T>o)g+=2;else if(I>n)y+=2;else if(!i){var c=h+f-P;if(c>=0&&c<t&&l[c]!==-1){var O=l[c],v=h+O-c;if(T=o-T,O>=T)return fo(b,m,O,v)}}}}return[[qe,b],[Me,m]]}function fo(b,m,o,n){var e=b.substring(0,o),h=m.substring(0,n),t=b.substring(o),l=m.substring(n),a=$n(e,h),s=$n(t,l);return a.concat(s)}function Qi(b,m){if(!b||!m||b.charAt(0)!==m.charAt(0))return 0;for(var o=0,n=Math.min(b.length,m.length),e=n,h=0;o<e;)b.substring(h,e)==m.substring(h,e)?(o=e,h=o):n=e,e=Math.floor((n-o)/2+o);return Lo(b.charCodeAt(e-1))&&e--,e}function Wi(b,m){if(!b||!m||b.slice(-1)!==m.slice(-1))return 0;for(var o=0,n=Math.min(b.length,m.length),e=n,h=0;o<e;)b.substring(b.length-e,b.length-h)==m.substring(m.length-e,m.length-h)?(o=e,h=o):n=e,e=Math.floor((n-o)/2+o);return Co(b.charCodeAt(b.length-e))&&e--,e}function Pl(b,m){var o=b.length>m.length?b:m,n=b.length>m.length?m:b;if(o.length<4||n.length*2<o.length)return null;function e(u,y,g){for(var p=u.substring(g,g+Math.floor(u.length/4)),d=-1,c="",O,v,E,T;(d=y.indexOf(p,d+1))!==-1;){var P=Qi(u.substring(g),y.substring(d)),I=Wi(u.substring(0,g),y.substring(0,d));c.length<I+P&&(c=y.substring(d-I,d)+y.substring(d,d+P),O=u.substring(0,g-I),v=u.substring(g+P),E=y.substring(0,d-I),T=y.substring(d+P))}return c.length*2>=u.length?[O,v,E,T,c]:null}var h=e(o,n,Math.ceil(o.length/4)),t=e(o,n,Math.ceil(o.length/2)),l;if(!h&&!t)return null;t?h?l=h[4].length>t[4].length?h:t:l=t:l=h;var a,s,f,i;b.length>m.length?(a=l[0],s=l[1],f=l[2],i=l[3]):(f=l[0],i=l[1],a=l[2],s=l[3]);var r=l[4];return[a,s,f,i,r]}function Ro(b,m){b.push([Ae,""]);for(var o=0,n=0,e=0,h="",t="",l;o<b.length;){if(o<b.length-1&&!b[o][1]){b.splice(o,1);continue}switch(b[o][0]){case Me:e++,t+=b[o][1],o++;break;case qe:n++,h+=b[o][1],o++;break;case Ae:var a=o-e-n-1;if(m){if(a>=0&&Mo(b[a][1])){var s=b[a][1].slice(-1);if(b[a][1]=b[a][1].slice(0,-1),h=s+h,t=s+t,!b[a][1]){b.splice(a,1),o--;var f=a-1;b[f]&&b[f][0]===Me&&(e++,t=b[f][1]+t,f--),b[f]&&b[f][0]===qe&&(n++,h=b[f][1]+h,f--),a=f}}if(jo(b[o][1])){var s=b[o][1].charAt(0);b[o][1]=b[o][1].slice(1),h+=s,t+=s}}if(o<b.length-1&&!b[o][1]){b.splice(o,1);break}if(h.length>0||t.length>0){h.length>0&&t.length>0&&(l=Qi(t,h),l!==0&&(a>=0?b[a][1]+=t.substring(0,l):(b.splice(0,0,[Ae,t.substring(0,l)]),o++),t=t.substring(l),h=h.substring(l)),l=Wi(t,h),l!==0&&(b[o][1]=t.substring(t.length-l)+b[o][1],t=t.substring(0,t.length-l),h=h.substring(0,h.length-l)));var i=e+n;h.length===0&&t.length===0?(b.splice(o-i,i),o=o-i):h.length===0?(b.splice(o-i,i,[Me,t]),o=o-i+1):t.length===0?(b.splice(o-i,i,[qe,h]),o=o-i+1):(b.splice(o-i,i,[qe,h],[Me,t]),o=o-i+2)}o!==0&&b[o-1][0]===Ae?(b[o-1][1]+=b[o][1],b.splice(o,1)):o++,e=0,n=0,h="",t="";break}}b[b.length-1][1]===""&&b.pop();var r=!1;for(o=1;o<b.length-1;)b[o-1][0]===Ae&&b[o+1][0]===Ae&&(b[o][1].substring(b[o][1].length-b[o-1][1].length)===b[o-1][1]?(b[o][1]=b[o-1][1]+b[o][1].substring(0,b[o][1].length-b[o-1][1].length),b[o+1][1]=b[o-1][1]+b[o+1][1],b.splice(o-1,1),r=!0):b[o][1].substring(0,b[o+1][1].length)==b[o+1][1]&&(b[o-1][1]+=b[o+1][1],b[o][1]=b[o][1].substring(b[o+1][1].length)+b[o+1][1],b.splice(o+1,1),r=!0)),o++;r&&Ro(b,m)}function Lo(b){return b>=55296&&b<=56319}function Co(b){return b>=56320&&b<=57343}function jo(b){return Co(b.charCodeAt(0))}function Mo(b){return Lo(b.charCodeAt(b.length-1))}function Nl(b){for(var m=[],o=0;o<b.length;o++)b[o][1].length>0&&m.push(b[o]);return m}function Li(b,m,o,n){return Mo(b)||jo(n)?null:Nl([[Ae,b],[qe,m],[Me,o],[Ae,n]])}function Il(b,m,o){var n=typeof o=="number"?{index:o,length:0}:o.oldRange,e=typeof o=="number"?null:o.newRange,h=b.length,t=m.length;if(n.length===0&&(e===null||e.length===0)){var l=n.index,a=b.slice(0,l),s=b.slice(l),f=e?e.index:null;t:{var i=l+t-h;if(f!==null&&f!==i||i<0||i>t)break t;var r=m.slice(0,i),u=m.slice(i);if(u!==s)break t;var y=Math.min(l,i),g=a.slice(0,y),p=r.slice(0,y);if(g!==p)break t;var d=a.slice(y),c=r.slice(y);return Li(g,d,c,s)}t:{if(f!==null&&f!==l)break t;var O=l,r=m.slice(0,O),u=m.slice(O);if(r!==a)break t;var v=Math.min(h-O,t-O),E=s.slice(s.length-v),T=u.slice(u.length-v);if(E!==T)break t;var d=s.slice(0,s.length-v),c=u.slice(0,u.length-v);return Li(a,d,c,E)}}if(n.length>0&&e&&e.length===0)t:{var g=b.slice(0,n.index),E=b.slice(n.index+n.length),y=g.length,v=E.length;if(t<y+v)break t;var p=m.slice(0,y),T=m.slice(t-v);if(g!==p||E!==T)break t;var d=b.slice(y,h-v),c=m.slice(y,t-v);return Li(g,d,c,E)}return null}function _r(b,m,o){return $n(b,m,o,!0)}_r.INSERT=Me;_r.DELETE=qe;_r.EQUAL=Ae;var Dl=_r,Lr={exports:{}};Lr.exports;(function(b,m){var o=200,n="__lodash_hash_undefined__",e=9007199254740991,h="[object Arguments]",t="[object Array]",l="[object Boolean]",a="[object Date]",s="[object Error]",f="[object Function]",i="[object GeneratorFunction]",r="[object Map]",u="[object Number]",y="[object Object]",g="[object Promise]",p="[object RegExp]",d="[object Set]",c="[object String]",O="[object Symbol]",v="[object WeakMap]",E="[object ArrayBuffer]",T="[object DataView]",P="[object Float32Array]",I="[object Float64Array]",D="[object Int8Array]",w="[object Int16Array]",S="[object Int32Array]",A="[object Uint8Array]",N="[object Uint8ClampedArray]",R="[object Uint16Array]",C="[object Uint32Array]",B=/[\\^$.*+?()[\]{}|]/g,U=/\w*$/,z=/^\[object .+?Constructor\]$/,K=/^(?:0|[1-9]\d*)$/,k={};k[h]=k[t]=k[E]=k[T]=k[l]=k[a]=k[P]=k[I]=k[D]=k[w]=k[S]=k[r]=k[u]=k[y]=k[p]=k[d]=k[c]=k[O]=k[A]=k[N]=k[R]=k[C]=!0,k[s]=k[f]=k[v]=!1;var L=typeof ue=="object"&&ue&&ue.Object===Object&&ue,_=typeof self=="object"&&self&&self.Object===Object&&self,F=L||_||Function("return this")(),Q=m&&!m.nodeType&&m,q=Q&&!0&&b&&!b.nodeType&&b,M=q&&q.exports===Q;function H(x,j){return x.set(j[0],j[1]),x}function W(x,j){return x.add(j),x}function $(x,j){for(var G=-1,J=x?x.length:0;++G<J&&j(x[G],G,x)!==!1;);return x}function et(x,j){for(var G=-1,J=j.length,Pt=x.length;++G<J;)x[Pt+G]=j[G];return x}function ut(x,j,G,J){for(var Pt=-1,mt=x?x.length:0;++Pt<mt;)G=j(G,x[Pt],Pt,x);return G}function pt(x,j){for(var G=-1,J=Array(x);++G<x;)J[G]=j(G);return J}function dt(x,j){return x==null?void 0:x[j]}function gt(x){var j=!1;if(x!=null&&typeof x.toString!="function")try{j=!!(x+"")}catch(G){}return j}function Nt(x){var j=-1,G=Array(x.size);return x.forEach(function(J,Pt){G[++j]=[Pt,J]}),G}function qt(x,j){return function(G){return x(j(G))}}function kt(x){var j=-1,G=Array(x.size);return x.forEach(function(J){G[++j]=J}),G}var Z=Array.prototype,tt=Function.prototype,it=Object.prototype,ot=F["__core-js_shared__"],nt=function(){var x=/[^.]+$/.exec(ot&&ot.keys&&ot.keys.IE_PROTO||"");return x?"Symbol(src)_1."+x:""}(),xt=tt.toString,bt=it.hasOwnProperty,St=it.toString,Zt=RegExp("^"+xt.call(bt).replace(B,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Yt=M?F.Buffer:void 0,jt=F.Symbol,Y=F.Uint8Array,V=qt(Object.getPrototypeOf,Object),X=Object.create,rt=it.propertyIsEnumerable,ct=Z.splice,Et=Object.getOwnPropertySymbols,At=Yt?Yt.isBuffer:void 0,Mt=qt(Object.keys,Object),Gt=Re(F,"DataView"),Ot=Re(F,"Map"),st=Re(F,"Promise"),yt=Re(F,"Set"),Dt=Re(F,"WeakMap"),Rt=Re(Object,"create"),re=me(Gt),De=me(Ot),an=me(st),_e=me(yt),Pn=me(Dt),Je=jt?jt.prototype:void 0,Jn=Je?Je.valueOf:void 0;function Ge(x){var j=-1,G=x?x.length:0;for(this.clear();++j<G;){var J=x[j];this.set(J[0],J[1])}}function kr(){this.__data__=Rt?Rt(null):{}}function Br(x){return this.has(x)&&delete this.__data__[x]}function Fr(x){var j=this.__data__;if(Rt){var G=j[x];return G===n?void 0:G}return bt.call(j,x)?j[x]:void 0}function tr(x){var j=this.__data__;return Rt?j[x]!==void 0:bt.call(j,x)}function Nn(x,j){var G=this.__data__;return G[x]=Rt&&j===void 0?n:j,this}Ge.prototype.clear=kr,Ge.prototype.delete=Br,Ge.prototype.get=Fr,Ge.prototype.has=tr,Ge.prototype.set=Nn;function se(x){var j=-1,G=x?x.length:0;for(this.clear();++j<G;){var J=x[j];this.set(J[0],J[1])}}function Ur(){this.__data__=[]}function qr(x){var j=this.__data__,G=gn(j,x);if(G<0)return!1;var J=j.length-1;return G==J?j.pop():ct.call(j,G,1),!0}function Hr(x){var j=this.__data__,G=gn(j,x);return G<0?void 0:j[G][1]}function Kr(x){return gn(this.__data__,x)>-1}function Gr(x,j){var G=this.__data__,J=gn(G,x);return J<0?G.push([x,j]):G[J][1]=j,this}se.prototype.clear=Ur,se.prototype.delete=qr,se.prototype.get=Hr,se.prototype.has=Kr,se.prototype.set=Gr;function he(x){var j=-1,G=x?x.length:0;for(this.clear();++j<G;){var J=x[j];this.set(J[0],J[1])}}function zr(){this.__data__={hash:new Ge,map:new(Ot||se),string:new Ge}}function Vr(x){return un(this,x).delete(x)}function Qr(x){return un(this,x).get(x)}function Wr(x){return un(this,x).has(x)}function $r(x,j){return un(this,x).set(x,j),this}he.prototype.clear=zr,he.prototype.delete=Vr,he.prototype.get=Qr,he.prototype.has=Wr,he.prototype.set=$r;function Oe(x){this.__data__=new se(x)}function Yr(){this.__data__=new se}function Xr(x){return this.__data__.delete(x)}function Zr(x){return this.__data__.get(x)}function Jr(x){return this.__data__.has(x)}function ti(x,j){var G=this.__data__;if(G instanceof se){var J=G.__data__;if(!Ot||J.length<o-1)return J.push([x,j]),this;G=this.__data__=new he(J)}return G.set(x,j),this}Oe.prototype.clear=Yr,Oe.prototype.delete=Xr,Oe.prototype.get=Zr,Oe.prototype.has=Jr,Oe.prototype.set=ti;function pn(x,j){var G=Ln(x)||mn(x)?pt(x.length,String):[],J=G.length,Pt=!!J;for(var mt in x)bt.call(x,mt)&&!(Pt&&(mt=="length"||vi(mt,J)))&&G.push(mt);return G}function er(x,j,G){var J=x[j];(!(bt.call(x,j)&&ar(J,G))||G===void 0&&!(j in x))&&(x[j]=G)}function gn(x,j){for(var G=x.length;G--;)if(ar(x[G][0],j))return G;return-1}function ke(x,j){return x&&Rn(j,jn(j),x)}function In(x,j,G,J,Pt,mt,Ut){var Bt;if(J&&(Bt=mt?J(x,Pt,mt,Ut):J(x)),Bt!==void 0)return Bt;if(!Fe(x))return x;var ie=Ln(x);if(ie){if(Bt=di(x),!j)return si(x,Bt)}else{var Ht=Ve(x),ve=Ht==f||Ht==i;if(lr(x))return yn(x,j);if(Ht==y||Ht==h||ve&&!mt){if(gt(x))return mt?x:{};if(Bt=Be(ve?{}:x),!j)return fi(x,ke(Bt,x))}else{if(!k[Ht])return mt?x:{};Bt=hi(x,Ht,In,j)}}Ut||(Ut=new Oe);var Ee=Ut.get(x);if(Ee)return Ee;if(Ut.set(x,Bt),!ie)var ae=G?ci(x):jn(x);return $(ae||x,function(pe,fe){ae&&(fe=pe,pe=x[fe]),er(Bt,fe,In(pe,j,G,J,fe,x,Ut))}),Bt}function ei(x){return Fe(x)?X(x):{}}function ni(x,j,G){var J=j(x);return Ln(x)?J:et(J,G(x))}function ri(x){return St.call(x)}function ii(x){if(!Fe(x)||gi(x))return!1;var j=Cn(x)||gt(x)?Zt:z;return j.test(me(x))}function oi(x){if(!ir(x))return Mt(x);var j=[];for(var G in Object(x))bt.call(x,G)&&G!="constructor"&&j.push(G);return j}function yn(x,j){if(j)return x.slice();var G=new x.constructor(x.length);return x.copy(G),G}function Dn(x){var j=new x.constructor(x.byteLength);return new Y(j).set(new Y(x)),j}function ln(x,j){var G=j?Dn(x.buffer):x.buffer;return new x.constructor(G,x.byteOffset,x.byteLength)}function nr(x,j,G){var J=j?G(Nt(x),!0):Nt(x);return ut(J,H,new x.constructor)}function rr(x){var j=new x.constructor(x.source,U.exec(x));return j.lastIndex=x.lastIndex,j}function ai(x,j,G){var J=j?G(kt(x),!0):kt(x);return ut(J,W,new x.constructor)}function li(x){return Jn?Object(Jn.call(x)):{}}function ui(x,j){var G=j?Dn(x.buffer):x.buffer;return new x.constructor(G,x.byteOffset,x.length)}function si(x,j){var G=-1,J=x.length;for(j||(j=Array(J));++G<J;)j[G]=x[G];return j}function Rn(x,j,G,J){G||(G={});for(var Pt=-1,mt=j.length;++Pt<mt;){var Ut=j[Pt],Bt=void 0;er(G,Ut,Bt===void 0?x[Ut]:Bt)}return G}function fi(x,j){return Rn(x,ze(x),j)}function ci(x){return ni(x,jn,ze)}function un(x,j){var G=x.__data__;return pi(j)?G[typeof j=="string"?"string":"hash"]:G.map}function Re(x,j){var G=dt(x,j);return ii(G)?G:void 0}var ze=Et?qt(Et,Object):mi,Ve=ri;(Gt&&Ve(new Gt(new ArrayBuffer(1)))!=T||Ot&&Ve(new Ot)!=r||st&&Ve(st.resolve())!=g||yt&&Ve(new yt)!=d||Dt&&Ve(new Dt)!=v)&&(Ve=function(x){var j=St.call(x),G=j==y?x.constructor:void 0,J=G?me(G):void 0;if(J)switch(J){case re:return T;case De:return r;case an:return g;case _e:return d;case Pn:return v}return j});function di(x){var j=x.length,G=x.constructor(j);return j&&typeof x[0]=="string"&&bt.call(x,"index")&&(G.index=x.index,G.input=x.input),G}function Be(x){return typeof x.constructor=="function"&&!ir(x)?ei(V(x)):{}}function hi(x,j,G,J){var Pt=x.constructor;switch(j){case E:return Dn(x);case l:case a:return new Pt(+x);case T:return ln(x,J);case P:case I:case D:case w:case S:case A:case N:case R:case C:return ui(x,J);case r:return nr(x,J,G);case u:case c:return new Pt(x);case p:return rr(x);case d:return ai(x,J,G);case O:return li(x)}}function vi(x,j){return j=j==null?e:j,!!j&&(typeof x=="number"||K.test(x))&&x>-1&&x%1==0&&x<j}function pi(x){var j=typeof x;return j=="string"||j=="number"||j=="symbol"||j=="boolean"?x!=="__proto__":x===null}function gi(x){return!!nt&&nt in x}function ir(x){var j=x&&x.constructor,G=typeof j=="function"&&j.prototype||it;return x===G}function me(x){if(x!=null){try{return xt.call(x)}catch(j){}try{return x+""}catch(j){}}return""}function or(x){return In(x,!0,!0)}function ar(x,j){return x===j||x!==x&&j!==j}function mn(x){return yi(x)&&bt.call(x,"callee")&&(!rt.call(x,"callee")||St.call(x)==h)}var Ln=Array.isArray;function bn(x){return x!=null&&ur(x.length)&&!Cn(x)}function yi(x){return sr(x)&&bn(x)}var lr=At||bi;function Cn(x){var j=Fe(x)?St.call(x):"";return j==f||j==i}function ur(x){return typeof x=="number"&&x>-1&&x%1==0&&x<=e}function Fe(x){var j=typeof x;return!!x&&(j=="object"||j=="function")}function sr(x){return!!x&&typeof x=="object"}function jn(x){return bn(x)?pn(x):oi(x)}function mi(){return[]}function bi(){return!1}b.exports=or})(Lr,Lr.exports);var _o=Lr.exports,Cr={exports:{}};Cr.exports;(function(b,m){var o=200,n="__lodash_hash_undefined__",e=1,h=2,t=9007199254740991,l="[object Arguments]",a="[object Array]",s="[object AsyncFunction]",f="[object Boolean]",i="[object Date]",r="[object Error]",u="[object Function]",y="[object GeneratorFunction]",g="[object Map]",p="[object Number]",d="[object Null]",c="[object Object]",O="[object Promise]",v="[object Proxy]",E="[object RegExp]",T="[object Set]",P="[object String]",I="[object Symbol]",D="[object Undefined]",w="[object WeakMap]",S="[object ArrayBuffer]",A="[object DataView]",N="[object Float32Array]",R="[object Float64Array]",C="[object Int8Array]",B="[object Int16Array]",U="[object Int32Array]",z="[object Uint8Array]",K="[object Uint8ClampedArray]",k="[object Uint16Array]",L="[object Uint32Array]",_=/[\\^$.*+?()[\]{}|]/g,F=/^\[object .+?Constructor\]$/,Q=/^(?:0|[1-9]\d*)$/,q={};q[N]=q[R]=q[C]=q[B]=q[U]=q[z]=q[K]=q[k]=q[L]=!0,q[l]=q[a]=q[S]=q[f]=q[A]=q[i]=q[r]=q[u]=q[g]=q[p]=q[c]=q[E]=q[T]=q[P]=q[w]=!1;var M=typeof ue=="object"&&ue&&ue.Object===Object&&ue,H=typeof self=="object"&&self&&self.Object===Object&&self,W=M||H||Function("return this")(),$=m&&!m.nodeType&&m,et=$&&!0&&b&&!b.nodeType&&b,ut=et&&et.exports===$,pt=ut&&M.process,dt=function(){try{return pt&&pt.binding&&pt.binding("util")}catch(x){}}(),gt=dt&&dt.isTypedArray;function Nt(x,j){for(var G=-1,J=x==null?0:x.length,Pt=0,mt=[];++G<J;){var Ut=x[G];j(Ut,G,x)&&(mt[Pt++]=Ut)}return mt}function qt(x,j){for(var G=-1,J=j.length,Pt=x.length;++G<J;)x[Pt+G]=j[G];return x}function kt(x,j){for(var G=-1,J=x==null?0:x.length;++G<J;)if(j(x[G],G,x))return!0;return!1}function Z(x,j){for(var G=-1,J=Array(x);++G<x;)J[G]=j(G);return J}function tt(x){return function(j){return x(j)}}function it(x,j){return x.has(j)}function ot(x,j){return x==null?void 0:x[j]}function nt(x){var j=-1,G=Array(x.size);return x.forEach(function(J,Pt){G[++j]=[Pt,J]}),G}function xt(x,j){return function(G){return x(j(G))}}function bt(x){var j=-1,G=Array(x.size);return x.forEach(function(J){G[++j]=J}),G}var St=Array.prototype,Zt=Function.prototype,Yt=Object.prototype,jt=W["__core-js_shared__"],Y=Zt.toString,V=Yt.hasOwnProperty,X=function(){var x=/[^.]+$/.exec(jt&&jt.keys&&jt.keys.IE_PROTO||"");return x?"Symbol(src)_1."+x:""}(),rt=Yt.toString,ct=RegExp("^"+Y.call(V).replace(_,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Et=ut?W.Buffer:void 0,At=W.Symbol,Mt=W.Uint8Array,Gt=Yt.propertyIsEnumerable,Ot=St.splice,st=At?At.toStringTag:void 0,yt=Object.getOwnPropertySymbols,Dt=Et?Et.isBuffer:void 0,Rt=xt(Object.keys,Object),re=ze(W,"DataView"),De=ze(W,"Map"),an=ze(W,"Promise"),_e=ze(W,"Set"),Pn=ze(W,"WeakMap"),Je=ze(Object,"create"),Jn=me(re),Ge=me(De),kr=me(an),Br=me(_e),Fr=me(Pn),tr=At?At.prototype:void 0,Nn=tr?tr.valueOf:void 0;function se(x){var j=-1,G=x==null?0:x.length;for(this.clear();++j<G;){var J=x[j];this.set(J[0],J[1])}}function Ur(){this.__data__=Je?Je(null):{},this.size=0}function qr(x){var j=this.has(x)&&delete this.__data__[x];return this.size-=j?1:0,j}function Hr(x){var j=this.__data__;if(Je){var G=j[x];return G===n?void 0:G}return V.call(j,x)?j[x]:void 0}function Kr(x){var j=this.__data__;return Je?j[x]!==void 0:V.call(j,x)}function Gr(x,j){var G=this.__data__;return this.size+=this.has(x)?0:1,G[x]=Je&&j===void 0?n:j,this}se.prototype.clear=Ur,se.prototype.delete=qr,se.prototype.get=Hr,se.prototype.has=Kr,se.prototype.set=Gr;function he(x){var j=-1,G=x==null?0:x.length;for(this.clear();++j<G;){var J=x[j];this.set(J[0],J[1])}}function zr(){this.__data__=[],this.size=0}function Vr(x){var j=this.__data__,G=yn(j,x);if(G<0)return!1;var J=j.length-1;return G==J?j.pop():Ot.call(j,G,1),--this.size,!0}function Qr(x){var j=this.__data__,G=yn(j,x);return G<0?void 0:j[G][1]}function Wr(x){return yn(this.__data__,x)>-1}function $r(x,j){var G=this.__data__,J=yn(G,x);return J<0?(++this.size,G.push([x,j])):G[J][1]=j,this}he.prototype.clear=zr,he.prototype.delete=Vr,he.prototype.get=Qr,he.prototype.has=Wr,he.prototype.set=$r;function Oe(x){var j=-1,G=x==null?0:x.length;for(this.clear();++j<G;){var J=x[j];this.set(J[0],J[1])}}function Yr(){this.size=0,this.__data__={hash:new se,map:new(De||he),string:new se}}function Xr(x){var j=Re(this,x).delete(x);return this.size-=j?1:0,j}function Zr(x){return Re(this,x).get(x)}function Jr(x){return Re(this,x).has(x)}function ti(x,j){var G=Re(this,x),J=G.size;return G.set(x,j),this.size+=G.size==J?0:1,this}Oe.prototype.clear=Yr,Oe.prototype.delete=Xr,Oe.prototype.get=Zr,Oe.prototype.has=Jr,Oe.prototype.set=ti;function pn(x){var j=-1,G=x==null?0:x.length;for(this.__data__=new Oe;++j<G;)this.add(x[j])}function er(x){return this.__data__.set(x,n),this}function gn(x){return this.__data__.has(x)}pn.prototype.add=pn.prototype.push=er,pn.prototype.has=gn;function ke(x){var j=this.__data__=new he(x);this.size=j.size}function In(){this.__data__=new he,this.size=0}function ei(x){var j=this.__data__,G=j.delete(x);return this.size=j.size,G}function ni(x){return this.__data__.get(x)}function ri(x){return this.__data__.has(x)}function ii(x,j){var G=this.__data__;if(G instanceof he){var J=G.__data__;if(!De||J.length<o-1)return J.push([x,j]),this.size=++G.size,this;G=this.__data__=new Oe(J)}return G.set(x,j),this.size=G.size,this}ke.prototype.clear=In,ke.prototype.delete=ei,ke.prototype.get=ni,ke.prototype.has=ri,ke.prototype.set=ii;function oi(x,j){var G=mn(x),J=!G&&ar(x),Pt=!G&&!J&&bn(x),mt=!G&&!J&&!Pt&&sr(x),Ut=G||J||Pt||mt,Bt=Ut?Z(x.length,String):[],ie=Bt.length;for(var Ht in x)V.call(x,Ht)&&!(Ut&&(Ht=="length"||Pt&&(Ht=="offset"||Ht=="parent")||mt&&(Ht=="buffer"||Ht=="byteLength"||Ht=="byteOffset")||hi(Ht,ie)))&&Bt.push(Ht);return Bt}function yn(x,j){for(var G=x.length;G--;)if(or(x[G][0],j))return G;return-1}function Dn(x,j,G){var J=j(x);return mn(x)?J:qt(J,G(x))}function ln(x){return x==null?x===void 0?D:d:st&&st in Object(x)?Ve(x):ir(x)}function nr(x){return Fe(x)&&ln(x)==l}function rr(x,j,G,J,Pt){return x===j?!0:x==null||j==null||!Fe(x)&&!Fe(j)?x!==x&&j!==j:ai(x,j,G,J,rr,Pt)}function ai(x,j,G,J,Pt,mt){var Ut=mn(x),Bt=mn(j),ie=Ut?a:Be(x),Ht=Bt?a:Be(j);ie=ie==l?c:ie,Ht=Ht==l?c:Ht;var ve=ie==c,Ee=Ht==c,ae=ie==Ht;if(ae&&bn(x)){if(!bn(j))return!1;Ut=!0,ve=!1}if(ae&&!ve)return mt||(mt=new ke),Ut||sr(x)?Rn(x,j,G,J,Pt,mt):fi(x,j,ie,G,J,Pt,mt);if(!(G&e)){var pe=ve&&V.call(x,"__wrapped__"),fe=Ee&&V.call(j,"__wrapped__");if(pe||fe){var tn=pe?x.value():x,Qe=fe?j.value():j;return mt||(mt=new ke),Pt(tn,Qe,G,J,mt)}}return ae?(mt||(mt=new ke),ci(x,j,G,J,Pt,mt)):!1}function li(x){if(!ur(x)||pi(x))return!1;var j=lr(x)?ct:F;return j.test(me(x))}function ui(x){return Fe(x)&&Cn(x.length)&&!!q[ln(x)]}function si(x){if(!gi(x))return Rt(x);var j=[];for(var G in Object(x))V.call(x,G)&&G!="constructor"&&j.push(G);return j}function Rn(x,j,G,J,Pt,mt){var Ut=G&e,Bt=x.length,ie=j.length;if(Bt!=ie&&!(Ut&&ie>Bt))return!1;var Ht=mt.get(x);if(Ht&&mt.get(j))return Ht==j;var ve=-1,Ee=!0,ae=G&h?new pn:void 0;for(mt.set(x,j),mt.set(j,x);++ve<Bt;){var pe=x[ve],fe=j[ve];if(J)var tn=Ut?J(fe,pe,ve,j,x,mt):J(pe,fe,ve,x,j,mt);if(tn!==void 0){if(tn)continue;Ee=!1;break}if(ae){if(!kt(j,function(Qe,sn){if(!it(ae,sn)&&(pe===Qe||Pt(pe,Qe,G,J,mt)))return ae.push(sn)})){Ee=!1;break}}else if(!(pe===fe||Pt(pe,fe,G,J,mt))){Ee=!1;break}}return mt.delete(x),mt.delete(j),Ee}function fi(x,j,G,J,Pt,mt,Ut){switch(G){case A:if(x.byteLength!=j.byteLength||x.byteOffset!=j.byteOffset)return!1;x=x.buffer,j=j.buffer;case S:return!(x.byteLength!=j.byteLength||!mt(new Mt(x),new Mt(j)));case f:case i:case p:return or(+x,+j);case r:return x.name==j.name&&x.message==j.message;case E:case P:return x==j+"";case g:var Bt=nt;case T:var ie=J&e;if(Bt||(Bt=bt),x.size!=j.size&&!ie)return!1;var Ht=Ut.get(x);if(Ht)return Ht==j;J|=h,Ut.set(x,j);var ve=Rn(Bt(x),Bt(j),J,Pt,mt,Ut);return Ut.delete(x),ve;case I:if(Nn)return Nn.call(x)==Nn.call(j)}return!1}function ci(x,j,G,J,Pt,mt){var Ut=G&e,Bt=un(x),ie=Bt.length,Ht=un(j),ve=Ht.length;if(ie!=ve&&!Ut)return!1;for(var Ee=ie;Ee--;){var ae=Bt[Ee];if(!(Ut?ae in j:V.call(j,ae)))return!1}var pe=mt.get(x);if(pe&&mt.get(j))return pe==j;var fe=!0;mt.set(x,j),mt.set(j,x);for(var tn=Ut;++Ee<ie;){ae=Bt[Ee];var Qe=x[ae],sn=j[ae];if(J)var Yi=Ut?J(sn,Qe,ae,j,x,mt):J(Qe,sn,ae,x,j,mt);if(!(Yi===void 0?Qe===sn||Pt(Qe,sn,G,J,mt):Yi)){fe=!1;break}tn||(tn=ae=="constructor")}if(fe&&!tn){var fr=x.constructor,cr=j.constructor;fr!=cr&&"constructor"in x&&"constructor"in j&&!(typeof fr=="function"&&fr instanceof fr&&typeof cr=="function"&&cr instanceof cr)&&(fe=!1)}return mt.delete(x),mt.delete(j),fe}function un(x){return Dn(x,jn,di)}function Re(x,j){var G=x.__data__;return vi(j)?G[typeof j=="string"?"string":"hash"]:G.map}function ze(x,j){var G=ot(x,j);return li(G)?G:void 0}function Ve(x){var j=V.call(x,st),G=x[st];try{x[st]=void 0;var J=!0}catch(mt){}var Pt=rt.call(x);return J&&(j?x[st]=G:delete x[st]),Pt}var di=yt?function(x){return x==null?[]:(x=Object(x),Nt(yt(x),function(j){return Gt.call(x,j)}))}:mi,Be=ln;(re&&Be(new re(new ArrayBuffer(1)))!=A||De&&Be(new De)!=g||an&&Be(an.resolve())!=O||_e&&Be(new _e)!=T||Pn&&Be(new Pn)!=w)&&(Be=function(x){var j=ln(x),G=j==c?x.constructor:void 0,J=G?me(G):"";if(J)switch(J){case Jn:return A;case Ge:return g;case kr:return O;case Br:return T;case Fr:return w}return j});function hi(x,j){return j=j==null?t:j,!!j&&(typeof x=="number"||Q.test(x))&&x>-1&&x%1==0&&x<j}function vi(x){var j=typeof x;return j=="string"||j=="number"||j=="symbol"||j=="boolean"?x!=="__proto__":x===null}function pi(x){return!!X&&X in x}function gi(x){var j=x&&x.constructor,G=typeof j=="function"&&j.prototype||Yt;return x===G}function ir(x){return rt.call(x)}function me(x){if(x!=null){try{return Y.call(x)}catch(j){}try{return x+""}catch(j){}}return""}function or(x,j){return x===j||x!==x&&j!==j}var ar=nr(function(){return arguments}())?nr:function(x){return Fe(x)&&V.call(x,"callee")&&!Gt.call(x,"callee")},mn=Array.isArray;function Ln(x){return x!=null&&Cn(x.length)&&!lr(x)}var bn=Dt||bi;function yi(x,j){return rr(x,j)}function lr(x){if(!ur(x))return!1;var j=ln(x);return j==u||j==y||j==s||j==v}function Cn(x){return typeof x=="number"&&x>-1&&x%1==0&&x<=t}function ur(x){var j=typeof x;return x!=null&&(j=="object"||j=="function")}function Fe(x){return x!=null&&typeof x=="object"}var sr=gt?tt(gt):ui;function jn(x){return Ln(x)?oi(x):si(x)}function mi(){return[]}function bi(){return!1}b.exports=yi})(Cr,Cr.exports);var ko=Cr.exports,$i={},Bo=ue&&ue.__importDefault||function(b){return b&&b.__esModule?b:{default:b}};Object.defineProperty($i,"__esModule",{value:!0});var Rl=Bo(_o),Ll=Bo(ko),Ui;(function(b){function m(h,t,l){h===void 0&&(h={}),t===void 0&&(t={}),typeof h!="object"&&(h={}),typeof t!="object"&&(t={});var a=Rl.default(t);l||(a=Object.keys(a).reduce(function(f,i){return a[i]!=null&&(f[i]=a[i]),f},{}));for(var s in h)h[s]!==void 0&&t[s]===void 0&&(a[s]=h[s]);return Object.keys(a).length>0?a:void 0}b.compose=m;function o(h,t){h===void 0&&(h={}),t===void 0&&(t={}),typeof h!="object"&&(h={}),typeof t!="object"&&(t={});var l=Object.keys(h).concat(Object.keys(t)).reduce(function(a,s){return Ll.default(h[s],t[s])||(a[s]=t[s]===void 0?null:t[s]),a},{});return Object.keys(l).length>0?l:void 0}b.diff=o;function n(h,t){h===void 0&&(h={}),t===void 0&&(t={}),h=h||{};var l=Object.keys(t).reduce(function(a,s){return t[s]!==h[s]&&h[s]!==void 0&&(a[s]=t[s]),a},{});return Object.keys(h).reduce(function(a,s){return h[s]!==t[s]&&t[s]===void 0&&(a[s]=null),a},l)}b.invert=n;function e(h,t,l){if(l===void 0&&(l=!1),typeof h!="object")return t;if(typeof t=="object"){if(!l)return t;var a=Object.keys(t).reduce(function(s,f){return h[f]===void 0&&(s[f]=t[f]),s},{});return Object.keys(a).length>0?a:void 0}}b.transform=e})(Ui||(Ui={}));$i.default=Ui;var mr={},br={},co;function Cl(){if(co)return br;co=1;var b=ue&&ue.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(br,"__esModule",{value:!0});var m=b(Fo()),o=function(){function n(e){this.ops=e,this.index=0,this.offset=0}return n.prototype.hasNext=function(){return this.peekLength()<1/0},n.prototype.next=function(e){e||(e=1/0);var h=this.ops[this.index];if(h){var t=this.offset,l=m.default.length(h);if(e>=l-t?(e=l-t,this.index+=1,this.offset=0):this.offset+=e,typeof h.delete=="number")return{delete:e};var a={};return h.attributes&&(a.attributes=h.attributes),typeof h.retain=="number"?a.retain=e:typeof h.insert=="string"?a.insert=h.insert.substr(t,e):a.insert=h.insert,a}else return{retain:1/0}},n.prototype.peek=function(){return this.ops[this.index]},n.prototype.peekLength=function(){return this.ops[this.index]?m.default.length(this.ops[this.index])-this.offset:1/0},n.prototype.peekType=function(){return this.ops[this.index]?typeof this.ops[this.index].delete=="number"?"delete":typeof this.ops[this.index].retain=="number"?"retain":"insert":"retain"},n.prototype.rest=function(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);var e=this.offset,h=this.index,t=this.next(),l=this.ops.slice(this.index);return this.offset=e,this.index=h,[t].concat(l)}else return[]},n}();return br.default=o,br}var ho;function Fo(){if(ho)return mr;ho=1;var b=ue&&ue.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(mr,"__esModule",{value:!0});var m=b(Cl()),o;return function(n){function e(t){return new m.default(t)}n.iterator=e;function h(t){return typeof t.delete=="number"?t.delete:typeof t.retain=="number"?t.retain:typeof t.insert=="string"?t.insert.length:1}n.length=h}(o||(o={})),mr.default=o,mr}var Zn=ue&&ue.__importDefault||function(b){return b&&b.__esModule?b:{default:b}},Or=Zn(Dl),jl=Zn(_o),Ci=Zn(ko),Bn=Zn($i),ce=Zn(Fo()),Ml="\0",_l=function(){function b(m){Array.isArray(m)?this.ops=m:m!=null&&Array.isArray(m.ops)?this.ops=m.ops:this.ops=[]}return b.prototype.insert=function(m,o){var n={};return typeof m=="string"&&m.length===0?this:(n.insert=m,o!=null&&typeof o=="object"&&Object.keys(o).length>0&&(n.attributes=o),this.push(n))},b.prototype.delete=function(m){return m<=0?this:this.push({delete:m})},b.prototype.retain=function(m,o){if(m<=0)return this;var n={retain:m};return o!=null&&typeof o=="object"&&Object.keys(o).length>0&&(n.attributes=o),this.push(n)},b.prototype.push=function(m){var o=this.ops.length,n=this.ops[o-1];if(m=jl.default(m),typeof n=="object"){if(typeof m.delete=="number"&&typeof n.delete=="number")return this.ops[o-1]={delete:n.delete+m.delete},this;if(typeof n.delete=="number"&&m.insert!=null&&(o-=1,n=this.ops[o-1],typeof n!="object"))return this.ops.unshift(m),this;if(Ci.default(m.attributes,n.attributes)){if(typeof m.insert=="string"&&typeof n.insert=="string")return this.ops[o-1]={insert:n.insert+m.insert},typeof m.attributes=="object"&&(this.ops[o-1].attributes=m.attributes),this;if(typeof m.retain=="number"&&typeof n.retain=="number")return this.ops[o-1]={retain:n.retain+m.retain},typeof m.attributes=="object"&&(this.ops[o-1].attributes=m.attributes),this}}return o===this.ops.length?this.ops.push(m):this.ops.splice(o,0,m),this},b.prototype.chop=function(){var m=this.ops[this.ops.length-1];return m&&m.retain&&!m.attributes&&this.ops.pop(),this},b.prototype.filter=function(m){return this.ops.filter(m)},b.prototype.forEach=function(m){this.ops.forEach(m)},b.prototype.map=function(m){return this.ops.map(m)},b.prototype.partition=function(m){var o=[],n=[];return this.forEach(function(e){var h=m(e)?o:n;h.push(e)}),[o,n]},b.prototype.reduce=function(m,o){return this.ops.reduce(m,o)},b.prototype.changeLength=function(){return this.reduce(function(m,o){return o.insert?m+ce.default.length(o):o.delete?m-o.delete:m},0)},b.prototype.length=function(){return this.reduce(function(m,o){return m+ce.default.length(o)},0)},b.prototype.slice=function(m,o){m===void 0&&(m=0),o===void 0&&(o=1/0);for(var n=[],e=ce.default.iterator(this.ops),h=0;h<o&&e.hasNext();){var t=void 0;h<m?t=e.next(m-h):(t=e.next(o-h),n.push(t)),h+=ce.default.length(t)}return new b(n)},b.prototype.compose=function(m){var o=ce.default.iterator(this.ops),n=ce.default.iterator(m.ops),e=[],h=n.peek();if(h!=null&&typeof h.retain=="number"&&h.attributes==null){for(var t=h.retain;o.peekType()==="insert"&&o.peekLength()<=t;)t-=o.peekLength(),e.push(o.next());h.retain-t>0&&n.next(h.retain-t)}for(var l=new b(e);o.hasNext()||n.hasNext();)if(n.peekType()==="insert")l.push(n.next());else if(o.peekType()==="delete")l.push(o.next());else{var a=Math.min(o.peekLength(),n.peekLength()),s=o.next(a),f=n.next(a);if(typeof f.retain=="number"){var i={};typeof s.retain=="number"?i.retain=a:i.insert=s.insert;var r=Bn.default.compose(s.attributes,f.attributes,typeof s.retain=="number");if(r&&(i.attributes=r),l.push(i),!n.hasNext()&&Ci.default(l.ops[l.ops.length-1],i)){var u=new b(o.rest());return l.concat(u).chop()}}else typeof f.delete=="number"&&typeof s.retain=="number"&&l.push(f)}return l.chop()},b.prototype.concat=function(m){var o=new b(this.ops.slice());return m.ops.length>0&&(o.push(m.ops[0]),o.ops=o.ops.concat(m.ops.slice(1))),o},b.prototype.diff=function(m,o){if(this.ops===m.ops)return new b;var n=[this,m].map(function(a){return a.map(function(s){if(s.insert!=null)return typeof s.insert=="string"?s.insert:Ml;var f=a===m?"on":"with";throw new Error("diff() called "+f+" non-document")}).join("")}),e=new b,h=Or.default(n[0],n[1],o),t=ce.default.iterator(this.ops),l=ce.default.iterator(m.ops);return h.forEach(function(a){for(var s=a[1].length;s>0;){var f=0;switch(a[0]){case Or.default.INSERT:f=Math.min(l.peekLength(),s),e.push(l.next(f));break;case Or.default.DELETE:f=Math.min(s,t.peekLength()),t.next(f),e.delete(f);break;case Or.default.EQUAL:f=Math.min(t.peekLength(),l.peekLength(),s);var i=t.next(f),r=l.next(f);Ci.default(i.insert,r.insert)?e.retain(f,Bn.default.diff(i.attributes,r.attributes)):e.push(r).delete(f);break}s-=f}}),e.chop()},b.prototype.eachLine=function(m,o){o===void 0&&(o=`
`);for(var n=ce.default.iterator(this.ops),e=new b,h=0;n.hasNext();){if(n.peekType()!=="insert")return;var t=n.peek(),l=ce.default.length(t)-n.peekLength(),a=typeof t.insert=="string"?t.insert.indexOf(o,l)-l:-1;if(a<0)e.push(n.next());else if(a>0)e.push(n.next(a));else{if(m(e,n.next(1).attributes||{},h)===!1)return;h+=1,e=new b}}e.length()>0&&m(e,{},h)},b.prototype.invert=function(m){var o=new b;return this.reduce(function(n,e){if(e.insert)o.delete(ce.default.length(e));else{if(e.retain&&e.attributes==null)return o.retain(e.retain),n+e.retain;if(e.delete||e.retain&&e.attributes){var h=e.delete||e.retain,t=m.slice(n,n+h);return t.forEach(function(l){e.delete?o.push(l):e.retain&&e.attributes&&o.retain(ce.default.length(l),Bn.default.invert(e.attributes,l.attributes))}),n+h}}return n},0),o.chop()},b.prototype.transform=function(m,o){if(o===void 0&&(o=!1),o=!!o,typeof m=="number")return this.transformPosition(m,o);for(var n=m,e=ce.default.iterator(this.ops),h=ce.default.iterator(n.ops),t=new b;e.hasNext()||h.hasNext();)if(e.peekType()==="insert"&&(o||h.peekType()!=="insert"))t.retain(ce.default.length(e.next()));else if(h.peekType()==="insert")t.push(h.next());else{var l=Math.min(e.peekLength(),h.peekLength()),a=e.next(l),s=h.next(l);if(a.delete)continue;s.delete?t.push(s):t.retain(l,Bn.default.transform(a.attributes,s.attributes,o))}return t.chop()},b.prototype.transformPosition=function(m,o){o===void 0&&(o=!1),o=!!o;for(var n=ce.default.iterator(this.ops),e=0;n.hasNext()&&e<=m;){var h=n.peekLength(),t=n.peekType();if(n.next(),t==="delete"){m-=Math.min(h,m-e);continue}else t==="insert"&&(e<m||!o)&&(m+=h);e+=h}return m},b.Op=ce.default,b.AttributeMap=Bn.default,b}(),kl=_l;const Bl=qi(kl);/*!
 * VueQuill @vueup/vue-quill v1.2.0
 * https://vueup.github.io/vue-quill/
 * 
 * Includes quill v1.3.7
 * https://quilljs.com/
 * 
 * Copyright (c) 2023 Ahmad Luthfi Masruri
 * Released under the MIT license
 * Date: 2023-05-12T08:44:03.742Z
 */const vo={essential:[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"},{align:[]}],["blockquote","code-block","link"],[{color:[]},"clean"]],minimal:[[{header:1},{header:2}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"},{align:[]}]],full:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["link","video","image"],["clean"]]},Fl=jr({name:"QuillEditor",inheritAttrs:!1,props:{content:{type:[String,Object]},contentType:{type:String,default:"delta",validator:b=>["delta","html","text"].includes(b)},enable:{type:Boolean,default:!0},readOnly:{type:Boolean,default:!1},placeholder:{type:String,required:!1},theme:{type:String,default:"snow",validator:b=>["snow","bubble",""].includes(b)},toolbar:{type:[String,Array,Object],required:!1,validator:b=>typeof b=="string"&&b!==""?b.charAt(0)==="#"?!0:Object.keys(vo).indexOf(b)!==-1:!0},modules:{type:Object,required:!1},options:{type:Object,required:!1},globalOptions:{type:Object,required:!1}},emits:["textChange","selectionChange","editorChange","update:content","focus","blur","ready"],setup:(b,m)=>{po(()=>{h()}),Wo(()=>{o=null});let o,n;const e=ne(),h=()=>{var S;if(e.value){if(n=t(),b.modules)if(Array.isArray(b.modules))for(const A of b.modules)Ri.register(`modules/${A.name}`,A.module);else Ri.register(`modules/${b.modules.name}`,b.modules.module);o=new Ri(e.value,n),O(b.content),o.on("text-change",i),o.on("selection-change",u),o.on("editor-change",y),b.theme!=="bubble"&&e.value.classList.remove("ql-bubble"),b.theme!=="snow"&&e.value.classList.remove("ql-snow"),(S=o.getModule("toolbar"))===null||S===void 0||S.container.addEventListener("mousedown",A=>{A.preventDefault()}),m.emit("ready",o)}},t=()=>{const S={};if(b.theme!==""&&(S.theme=b.theme),b.readOnly&&(S.readOnly=b.readOnly),b.placeholder&&(S.placeholder=b.placeholder),b.toolbar&&b.toolbar!==""&&(S.modules={toolbar:(()=>{if(typeof b.toolbar=="object")return b.toolbar;if(typeof b.toolbar=="string")return b.toolbar.charAt(0)==="#"?b.toolbar:vo[b.toolbar]})()}),b.modules){const A=(()=>{var N,R;const C={};if(Array.isArray(b.modules))for(const B of b.modules)C[B.name]=(N=B.options)!==null&&N!==void 0?N:{};else C[b.modules.name]=(R=b.modules.options)!==null&&R!==void 0?R:{};return C})();S.modules=Object.assign({},S.modules,A)}return Object.assign({},b.globalOptions,b.options,S)},l=S=>typeof S=="object"&&S?S.slice():S,a=S=>Object.values(S.ops).some(A=>!A.retain||Object.keys(A).length!==1);let s;const f=S=>{if(typeof s==typeof S){if(S===s)return!0;if(typeof S=="object"&&S&&typeof s=="object"&&s)return!a(s.diff(S))}return!1},i=(S,A,N)=>{s=l(c()),f(b.content)||m.emit("update:content",s),m.emit("textChange",{delta:S,oldContents:A,source:N})},r=ne(),u=(S,A,N)=>{r.value=!!(o!=null&&o.hasFocus()),m.emit("selectionChange",{range:S,oldRange:A,source:N})};hn(r,S=>{S?m.emit("focus",e):m.emit("blur",e)});const y=(...S)=>{S[0]==="text-change"&&m.emit("editorChange",{name:S[0],delta:S[1],oldContents:S[2],source:S[3]}),S[0]==="selection-change"&&m.emit("editorChange",{name:S[0],range:S[1],oldRange:S[2],source:S[3]})},g=()=>e.value,p=()=>{var S;return(S=o==null?void 0:o.getModule("toolbar"))===null||S===void 0?void 0:S.container},d=()=>{if(o)return o;throw`The quill editor hasn't been instantiated yet,
                  make sure to call this method when the editor ready
                  or use v-on:ready="onReady(quill)" event instead.`},c=(S,A)=>b.contentType==="html"?T():b.contentType==="text"?v(S,A):o==null?void 0:o.getContents(S,A),O=(S,A="api")=>{const N=S||(b.contentType==="delta"?new Bl:"");b.contentType==="html"?P(N):b.contentType==="text"?E(N,A):o==null||o.setContents(N,A),s=l(N)},v=(S,A)=>{var N;return(N=o==null?void 0:o.getText(S,A))!==null&&N!==void 0?N:""},E=(S,A="api")=>{o==null||o.setText(S,A)},T=()=>{var S;return(S=o==null?void 0:o.root.innerHTML)!==null&&S!==void 0?S:""},P=S=>{o&&(o.root.innerHTML=S)},I=(S,A="api")=>{const N=o==null?void 0:o.clipboard.convert(S);N&&(o==null||o.setContents(N,A))},D=()=>{o==null||o.focus()},w=()=>{wr(()=>{var S;!m.slots.toolbar&&o&&((S=o.getModule("toolbar"))===null||S===void 0||S.container.remove()),h()})};return hn(()=>b.content,S=>{if(!o||!S||f(S))return;const A=o.getSelection();A&&wr(()=>o==null?void 0:o.setSelection(A)),O(S)},{deep:!0}),hn(()=>b.enable,S=>{o&&o.enable(S)}),{editor:e,getEditor:g,getToolbar:p,getQuill:d,getContents:c,setContents:O,getHTML:T,setHTML:P,pasteHTML:I,focus:D,getText:v,setText:E,reinit:w}},render(){var b,m;return[(m=(b=this.$slots).toolbar)===null||m===void 0?void 0:m.call(b),Qo("div",Se({ref:"editor"},this.$attrs))]}}),Ul=jr({__name:"QuestionEditor",props:{question:{type:Object,required:!0}},emits:["update:question","save","cancel","delete"],setup(b,{expose:m,emit:o}){m();const n=b,e=o,h=ne(),t=ne(),l=ne(Se({},n.question)),a=ne(!1),s=ne(!1),f=ne(!1),i=ne(""),r=ne([]),u=[{label:"单选题",value:"single_choice"},{label:"多选题",value:"multiple_choice"},{label:"判断题",value:"true_false"},{label:"填空题",value:"fill_blank"},{label:"简答题",value:"short_answer"},{label:"解答题",value:"essay"},{label:"计算题",value:"calculation"},{label:"应用题",value:"application"},{label:"分析题",value:"analysis"},{label:"综合题",value:"comprehensive"}],y={single_choice:"单选题",multiple_choice:"多选题",true_false:"判断题",fill_blank:"填空题",short_answer:"简答题",essay:"解答题",calculation:"计算题",application:"应用题",analysis:"分析题",comprehensive:"综合题"},g={theme:"snow",modules:{toolbar:[["bold","italic","underline","strike"],[{color:[]},{background:[]}],[{script:"sub"},{script:"super"}],[{header:[1,2,3,!1]}],["blockquote","code-block"],[{list:"ordered"},{list:"bullet"}],["link","image","formula"],["clean"]]},placeholder:"请输入内容..."},p=Wn(()=>["single_choice","multiple_choice","true_false"].includes(l.value.tags.questionType)),d=z=>y[z]||z,c=z=>{const K=l.value.tags.questionType;l.value.tags.questionType=z,z==="true_false"?l.value.content.options=["正确","错误"]:["single_choice","multiple_choice"].includes(z)&&!l.value.content.options?l.value.content.options=["选项A","选项B","选项C","选项D"]:["single_choice","multiple_choice","true_false"].includes(z)||(l.value.content.options=void 0),K!==z&&(l.value.content.answer="",r.value=[]),w()},O=()=>{l.value.content.options||(l.value.content.options=[]);const z=l.value.content.options.length;l.value.content.options.push(`选项${String.fromCharCode(65+z)}`),w()},v=z=>{l.value.content.options&&l.value.content.options.length>2&&(l.value.content.options.splice(z,1),w())},E=z=>{l.value.content.answer=z.join(","),w()},T=z=>{l.value.tags.knowledgePoint.splice(z,1),w()},P=()=>{f.value=!0,wr(()=>{var z;(z=t.value)==null||z.focus()})},I=()=>{const z=i.value.trim();z&&!l.value.tags.knowledgePoint.includes(z)&&(l.value.tags.knowledgePoint.push(z),w()),f.value=!1,i.value=""},D=()=>{e("update:question",Se({},l.value))},w=()=>{B&&clearTimeout(B),B=setTimeout(()=>{D()},300)},S=()=>{if(console.log("Attempting to save question:",l.value),!l.value.content.stem.trim()||l.value.content.stem.trim()==="请输入题目内容..."){le.warning("请输入题目内容");return}if(!l.value.content.answer.trim()){le.warning("请输入正确答案");return}if(p.value){if(!l.value.content.options||l.value.content.options.length<2){le.warning("选择题至少需要2个选项");return}if(l.value.content.options.filter(_=>!_.trim()).length>0){le.warning("请填写所有选项内容");return}const K=l.value.content.answer.split(",").map(_=>_.trim()),k=l.value.content.options.map((_,F)=>String.fromCharCode(65+F));if(K.filter(_=>!k.includes(_)).length>0){le.warning("请选择有效的答案选项");return}}console.log("Validation passed, emitting save event"),a.value=!0,e("save")},A=()=>{e("cancel")},N=()=>We(this,null,function*(){try{yield ji.confirm("确定要删除这道题目吗？","删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}),e("delete")}catch(z){}}),R=()=>{s.value=!0},C=ne(!1);hn(()=>n.question,z=>{C.value=!0,l.value=Se({},z),z.tags.questionType==="multiple_choice"&&z.content.answer?r.value=z.content.answer.split(",").filter(Boolean):r.value=[],a.value=!1,wr(()=>{setTimeout(()=>{C.value=!1},50)})},{immediate:!0,deep:!0});let B=null;hn(l,()=>{C.value||(B&&clearTimeout(B),B=setTimeout(()=>{D()},300))},{deep:!0}),go(()=>{B&&clearTimeout(B)});const U={props:n,emit:e,formRef:h,knowledgePointInputRef:t,questionData:l,saving:a,previewVisible:s,knowledgePointInputVisible:f,knowledgePointInputValue:i,multipleAnswer:r,questionTypes:u,questionTypeLabels:y,quillOptions:g,isChoiceQuestion:p,getQuestionTypeLabel:d,handleTypeChange:c,addOption:O,removeOption:v,handleMultipleAnswerChange:E,removeKnowledgePoint:T,showKnowledgePointInput:P,handleKnowledgePointConfirm:I,emitUpdate:D,debouncedEmitUpdate:w,handleSave:S,handleCancel:A,handleDelete:N,handlePreview:R,isUpdatingFromProps:C,get updateTimeout(){return B},set updateTimeout(z){B=z},get Plus(){return na},get QuillEditor(){return Fl}};return Object.defineProperty(U,"__isScriptSetup",{enumerable:!1,value:!0}),U}}),ql={class:"question-editor"},Hl={class:"rich-editor"},Kl={class:"options-editor"},Gl={class:"option-header"},zl={class:"option-label"},Vl={key:0,class:"choice-answer"},Ql={class:"rich-editor"},Wl={class:"editor-actions"},$l={class:"question-preview"},Yl={class:"preview-header"},Xl={class:"preview-content"},Zl=["innerHTML"],Jl={key:0,class:"preview-options"},tu={class:"preview-answer"},eu={key:1,class:"preview-explanation"},nu=["innerHTML"];function ru(b,m,o,n,e,h){const t=Ft("el-option"),l=Ft("el-select"),a=Ft("el-form-item"),s=Ft("el-button"),f=Ft("el-input"),i=Ft("el-icon"),r=Ft("el-checkbox"),u=Ft("el-checkbox-group"),y=Ft("el-radio"),g=Ft("el-radio-group"),p=Ft("el-col"),d=Ft("el-input-number"),c=Ft("el-row"),O=Ft("el-tag"),v=Ft("el-form"),E=Ft("el-dialog");return Ct(),zt("div",ql,[at(v,{model:n.questionData,ref:"formRef","label-width":"100px"},{default:ft(()=>[at(a,{label:"题目类型",required:""},{default:ft(()=>[at(l,{modelValue:n.questionData.tags.questionType,"onUpdate:modelValue":m[0]||(m[0]=T=>n.questionData.tags.questionType=T),onChange:n.handleTypeChange,style:{width:"200px"}},{default:ft(()=>[(Ct(),zt($e,null,Ye(n.questionTypes,T=>at(t,{key:T.value,label:T.label,value:T.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),at(a,{label:"题目内容",required:""},{default:ft(()=>[ht("div",Hl,[at(n.QuillEditor,{content:n.questionData.content.stem,"onUpdate:content":m[1]||(m[1]=T=>n.questionData.content.stem=T),contentType:"html",options:n.quillOptions,style:{"min-height":"200px"}},null,8,["content"])])]),_:1}),n.isChoiceQuestion?(Ct(),Ue(a,{key:0,label:"选项设置",required:""},{default:ft(()=>[ht("div",Kl,[(Ct(!0),zt($e,null,Ye(n.questionData.content.options,(T,P)=>(Ct(),zt("div",{key:P,class:"option-item"},[ht("div",Gl,[ht("span",zl,"选项 "+Kt(String.fromCharCode(65+P)),1),at(s,{size:"small",type:"danger",text:"",onClick:I=>n.removeOption(P),disabled:n.questionData.content.options.length<=2},{default:ft(()=>[...m[11]||(m[11]=[Vt(" 删除 ",-1)])]),_:2,__:[11]},1032,["onClick","disabled"])]),at(f,{modelValue:n.questionData.content.options[P],"onUpdate:modelValue":I=>n.questionData.content.options[P]=I,placeholder:"请输入选项内容",type:"textarea",rows:2},null,8,["modelValue","onUpdate:modelValue"])]))),128)),at(s,{onClick:n.addOption,type:"primary",text:"",disabled:n.questionData.content.options.length>=6},{default:ft(()=>[at(i,null,{default:ft(()=>[at(n.Plus)]),_:1}),m[12]||(m[12]=Vt(" 添加选项 ",-1))]),_:1,__:[12]},8,["disabled"])])]),_:1})):nn("",!0),at(a,{label:"正确答案",required:""},{default:ft(()=>[n.isChoiceQuestion?(Ct(),zt("div",Vl,[n.questionData.tags.questionType==="multiple_choice"?(Ct(),Ue(u,{key:0,modelValue:n.multipleAnswer,"onUpdate:modelValue":m[2]||(m[2]=T=>n.multipleAnswer=T),onChange:n.handleMultipleAnswerChange},{default:ft(()=>[(Ct(!0),zt($e,null,Ye(n.questionData.content.options,(T,P)=>(Ct(),Ue(r,{key:P,value:String.fromCharCode(65+P)},{default:ft(()=>[Vt(Kt(String.fromCharCode(65+P)),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])):(Ct(),Ue(g,{key:1,modelValue:n.questionData.content.answer,"onUpdate:modelValue":m[3]||(m[3]=T=>n.questionData.content.answer=T)},{default:ft(()=>[(Ct(!0),zt($e,null,Ye(n.questionData.content.options,(T,P)=>(Ct(),Ue(y,{key:P,value:String.fromCharCode(65+P)},{default:ft(()=>[Vt(Kt(String.fromCharCode(65+P)),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"]))])):(Ct(),Ue(f,{key:1,modelValue:n.questionData.content.answer,"onUpdate:modelValue":m[4]||(m[4]=T=>n.questionData.content.answer=T),placeholder:"请输入正确答案",type:"textarea",rows:3},null,8,["modelValue"]))]),_:1}),at(a,{label:"答案解析"},{default:ft(()=>[ht("div",Ql,[at(n.QuillEditor,{content:n.questionData.content.explanation,"onUpdate:content":[m[5]||(m[5]=T=>n.questionData.content.explanation=T),n.debouncedEmitUpdate],contentType:"html",options:n.quillOptions,style:{"min-height":"150px"}},null,8,["content"])])]),_:1}),at(c,{gutter:16},{default:ft(()=>[at(p,{span:8},{default:ft(()=>[at(a,{label:"难度"},{default:ft(()=>[at(l,{modelValue:n.questionData.tags.difficulty,"onUpdate:modelValue":m[6]||(m[6]=T=>n.questionData.tags.difficulty=T),style:{width:"100%"}},{default:ft(()=>[at(t,{label:"容易",value:"easy"}),at(t,{label:"中等",value:"medium"}),at(t,{label:"困难",value:"hard"}),at(t,{label:"很难",value:"very_hard"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),at(p,{span:8},{default:ft(()=>[at(a,{label:"分值"},{default:ft(()=>[at(d,{modelValue:n.questionData.score,"onUpdate:modelValue":m[7]||(m[7]=T=>n.questionData.score=T),min:1,max:100,step:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),at(p,{span:8},{default:ft(()=>[at(a,{label:"学科"},{default:ft(()=>[at(f,{modelValue:n.questionData.tags.subject,"onUpdate:modelValue":m[8]||(m[8]=T=>n.questionData.tags.subject=T),style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),at(a,{label:"知识点"},{default:ft(()=>[(Ct(!0),zt($e,null,Ye(n.questionData.tags.knowledgePoint,(T,P)=>(Ct(),Ue(O,{key:P,closable:"",onClose:I=>n.removeKnowledgePoint(P),style:{"margin-right":"8px","margin-bottom":"8px"}},{default:ft(()=>[Vt(Kt(T),1)]),_:2},1032,["onClose"]))),128)),n.knowledgePointInputVisible?(Ct(),Ue(f,{key:0,ref:"knowledgePointInputRef",modelValue:n.knowledgePointInputValue,"onUpdate:modelValue":m[9]||(m[9]=T=>n.knowledgePointInputValue=T),size:"small",style:{width:"150px"},onKeyup:$o(n.handleKnowledgePointConfirm,["enter"]),onBlur:n.handleKnowledgePointConfirm},null,8,["modelValue"])):(Ct(),Ue(s,{key:1,size:"small",text:"",onClick:n.showKnowledgePointInput},{default:ft(()=>[at(i,null,{default:ft(()=>[at(n.Plus)]),_:1}),m[13]||(m[13]=Vt(" 添加知识点 ",-1))]),_:1,__:[13]}))]),_:1})]),_:1},8,["model"]),ht("div",Wl,[at(s,{onClick:n.handleCancel},{default:ft(()=>m[14]||(m[14]=[Vt("取消",-1)])),_:1,__:[14]}),at(s,{onClick:n.handlePreview},{default:ft(()=>m[15]||(m[15]=[Vt("预览",-1)])),_:1,__:[15]}),at(s,{type:"danger",onClick:n.handleDelete},{default:ft(()=>m[16]||(m[16]=[Vt("删除",-1)])),_:1,__:[16]}),at(s,{type:"primary",onClick:n.handleSave,loading:n.saving},{default:ft(()=>m[17]||(m[17]=[Vt(" 保存题目 ",-1)])),_:1,__:[17]},8,["loading"])]),at(E,{modelValue:n.previewVisible,"onUpdate:modelValue":m[10]||(m[10]=T=>n.previewVisible=T),title:"题目预览",width:"60%"},{default:ft(()=>[ht("div",$l,[ht("div",Yl,[ht("h3",null,Kt(n.getQuestionTypeLabel(n.questionData.tags.questionType))+" ("+Kt(n.questionData.score)+"分)",1)]),ht("div",Xl,[ht("div",{class:"preview-stem",innerHTML:n.questionData.content.stem},null,8,Zl),n.questionData.content.options&&n.questionData.content.options.length>0?(Ct(),zt("div",Jl,[(Ct(!0),zt($e,null,Ye(n.questionData.content.options,(T,P)=>(Ct(),zt("div",{key:P,class:"preview-option"},Kt(String.fromCharCode(65+P))+". "+Kt(T),1))),128))])):nn("",!0),ht("div",tu,[m[18]||(m[18]=ht("strong",null,"答案：",-1)),Vt(Kt(n.questionData.content.answer),1)]),n.questionData.content.explanation?(Ct(),zt("div",eu,[m[19]||(m[19]=ht("strong",null,"解析：",-1)),ht("div",{innerHTML:n.questionData.content.explanation},null,8,nu)])):nn("",!0)])])]),_:1},8,["modelValue"])])}const iu=Hi(Ul,[["render",ru],["__scopeId","data-v-3ca6158e"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/components/ui/QuestionEditor.vue"]]),ou=jr({__name:"index",setup(b,{expose:m}){m();const o=Xo(),n=Yo(),e=ne([]),h=ne([]),t=ne(null),l=ne(!1),a=ne(!1),s=ne(0);let f=null;const i=()=>{f&&clearTimeout(f),f=setTimeout(()=>{s.value++},500)},r=Wn(()=>e.value.reduce((C,B)=>C+B.score,0)),u=Wn(()=>{const C={};return e.value.forEach(B=>{const U=B.tags.questionType;C[U]=(C[U]||0)+1}),C}),y={single_choice:"单选题",multiple_choice:"多选题",true_false:"判断题",fill_blank:"填空题",short_answer:"简答题",essay:"解答题",calculation:"计算题",application:"应用题",analysis:"分析题",comprehensive:"综合题"},g=C=>y[C]||C,p=C=>({single_choice:"primary",multiple_choice:"success",true_false:"info",fill_blank:"warning",short_answer:"danger",essay:"",calculation:"primary",application:"success",analysis:"info",comprehensive:"warning"})[C]||"",d=C=>{e.value=[...C]},c=C=>{h.value=C},O=C=>{const B=e.value.find(U=>U.id===C);B&&(t.value=Se({},B))},v=C=>We(this,null,function*(){var B,U;if(C.startsWith("test-question-")||C.startsWith("new-question-")){e.value=e.value.filter(z=>z.id!==C),((B=t.value)==null?void 0:B.id)===C&&(t.value=null),i(),le.success("题目删除成功");return}try{e.value=e.value.filter(z=>z.id!==C),((U=t.value)==null?void 0:U.id)===C&&(t.value=null),le.success("题目删除成功"),i();try{yield On.deleteQuestion(C)}catch(z){console.warn("Failed to sync deletion to server, keeping local change:",z)}}catch(z){console.error("Failed to delete question:",z),le.error("删除题目失败")}}),E=C=>{e.value=[...C],i(),le.success("题目顺序已更新")},T=(C,B)=>We(this,null,function*(){try{const U=e.value.findIndex(z=>z.id===C);if(U!==-1){const z=[...e.value];z[U]=Oi(Se({},z[U]),{score:B}),e.value=z,yield On.updateQuestion(C,{score:B}),i(),le.success("分值更新成功")}}catch(U){console.error("Failed to update score:",U),le.error("分值更新失败")}}),P=()=>{const C={id:`new-question-${Date.now()}`,content:{stem:"请输入题目内容...",options:["选项A","选项B","选项C","选项D"],answer:"A",explanation:"",attachments:[]},tags:{grade:o.filterParams.grade||"grade9",subject:o.filterParams.subject||"数学",questionType:"single_choice",difficulty:"medium",knowledgePoint:[],scenario:"练习",sourceType:"手动添加"},score:5,order:e.value.length+1};e.value=[...e.value,C],t.value=Se({},C),i()},I=C=>{const B=e.value.findIndex(U=>U.id===C.id);if(B!==-1){const U=[...e.value];U[B]=Se({},C),e.value=U,t.value=Se({},C),i()}},D=()=>We(this,null,function*(){if(t.value){console.log("Starting to save question:",t.value);try{a.value=!0,f&&(clearTimeout(f),f=null);let C;if(t.value.id.startsWith("new-question-")){console.log("Creating new question");try{C=yield On.createQuestion(t.value),t.value.id=C.id,console.log("New question created successfully:",C)}catch(U){console.warn("Failed to create question via API, keeping local copy:",U),t.value.id=`question-${Date.now()}`,C=t.value}}else{console.log("Updating existing question");try{C=yield On.updateQuestion(t.value.id,t.value),console.log("Question updated successfully:",C)}catch(U){console.warn("Failed to update question via API, keeping local changes:",U),C=t.value}}const B=e.value.findIndex(U=>U.id===t.value.id||U.id===C.id);if(B!==-1){const U=[...e.value];U[B]=Se({},C),e.value=U,t.value=Se({},C)}s.value=Math.max(0,s.value-1),le.success("题目保存成功"),console.log("Question saved successfully")}catch(C){console.error("Failed to save question:",C),le.error("保存题目失败")}finally{a.value=!1}}}),w=()=>{t.value=null},S=()=>We(this,null,function*(){t.value&&(yield v(t.value.id),t.value=null)}),A=()=>We(this,null,function*(){if(s.value===0){le.info("没有需要保存的更改");return}try{a.value=!0,f&&(clearTimeout(f),f=null);const C=e.value.filter(z=>z.id.startsWith("new-question-")),B=e.value.filter(z=>!z.id.startsWith("new-question-")&&!z.id.startsWith("test-question-"));let U=0;for(const z of C)try{const K=yield On.createQuestion(z),k=e.value.findIndex(L=>L.id===z.id);k!==-1&&(e.value[k]=Se({},K)),U++}catch(K){console.warn("Failed to save new question, keeping local copy:",K);const k=e.value.findIndex(L=>L.id===z.id);k!==-1&&(e.value[k].id=`question-${Date.now()}-${k}`),U++}if(B.length>0)try{const z=B.map(K=>({id:K.id,updates:K}));yield On.batchUpdateQuestions(z),U+=B.length}catch(z){console.warn("Failed to batch update questions, keeping local changes:",z),U+=B.length}s.value=0,le.success(`成功保存 ${U} 道题目`)}catch(C){console.error("Failed to save all questions:",C),le.error("保存失败")}finally{a.value=!1}}),N=()=>{if(e.value.length===0){le.warning("没有题目可以导出");return}n.push("/layout")};po(()=>{o.questions.length>0?e.value=[...o.questions]:e.value=[{id:"test-question-1",content:{stem:"已知函数 f(x) = x² + 2x + 1，求 f(x) 的最小值。",options:["A. 0","B. 1","C. 2","D. 3"],answer:"A",explanation:"f(x) = x² + 2x + 1 = (x + 1)²，所以最小值为 0。",attachments:[]},tags:{grade:"grade9",subject:"数学",questionType:"single_choice",difficulty:"medium",knowledgePoint:["二次函数","配方法"],scenario:"练习",sourceType:"手动添加"},score:5,order:1},{id:"test-question-2",content:{stem:"下列哪个选项是正确的？",options:["A. 1 + 1 = 3","B. 2 + 2 = 4","C. 3 + 3 = 5","D. 4 + 4 = 9"],answer:"B",explanation:"2 + 2 = 4 是正确的数学运算。",attachments:[]},tags:{grade:"grade9",subject:"数学",questionType:"single_choice",difficulty:"easy",knowledgePoint:["基础运算"],scenario:"练习",sourceType:"手动添加"},score:3,order:2}]}),hn(()=>o.questions,C=>{C.length>0&&(e.value=[...C])},{deep:!0,immediate:!1}),go(()=>{f&&clearTimeout(f)});const R={filterStore:o,router:n,questions:e,selectedQuestions:h,editingQuestion:t,loading:l,saving:a,unsavedChanges:s,get changeTimeout(){return f},set changeTimeout(C){f=C},incrementUnsavedChanges:i,totalScore:r,questionTypeStats:u,questionTypeLabels:y,getQuestionTypeLabel:g,getTagType:p,handleQuestionsUpdate:d,handleQuestionSelect:c,handleQuestionEdit:O,handleQuestionDelete:v,handleQuestionsReorder:E,handleScoreChange:T,handleAddQuestion:P,handleQuestionUpdate:I,handleSaveQuestion:D,handleCancelEdit:w,handleDeleteCurrentQuestion:S,handleSaveAll:A,handleExportTest:N,QuestionList:xl,QuestionEditor:iu};return Object.defineProperty(R,"__isScriptSetup",{enumerable:!1,value:!0}),R}}),au={class:"question-edit"},lu={class:"edit-layout"},uu={class:"left-panel"},su={class:"right-panel"},fu={class:"editor-header"},cu={class:"header-actions"},du={class:"editor-content"},hu={key:0,class:"no-selection"},vu={key:1,class:"question-editor"},pu={class:"stats-panel"},gu={class:"stats-content"},yu={class:"type-distribution"},mu={class:"type-tags"};function bu(b,m,o,n,e,h){const t=Ft("el-button"),l=Ft("el-empty"),a=Ft("el-card"),s=Ft("el-statistic"),f=Ft("el-col"),i=Ft("el-row"),r=Ft("el-tag");return Ct(),zt("div",au,[ht("div",lu,[ht("div",uu,[at(n.QuestionList,{questions:n.questions,loading:n.loading,"onUpdate:questions":n.handleQuestionsUpdate,onQuestionSelect:n.handleQuestionSelect,onQuestionEdit:n.handleQuestionEdit,onQuestionDelete:n.handleQuestionDelete,onQuestionsReorder:n.handleQuestionsReorder,onScoreChange:n.handleScoreChange,onAddQuestion:n.handleAddQuestion},null,8,["questions","loading"])]),ht("div",su,[at(a,null,{header:ft(()=>[ht("div",fu,[m[2]||(m[2]=ht("span",null,"题目编辑器",-1)),ht("div",cu,[at(t,{size:"small",onClick:n.handleSaveAll,loading:n.saving},{default:ft(()=>m[0]||(m[0]=[Vt(" 保存所有更改 ",-1)])),_:1,__:[0]},8,["loading"]),at(t,{size:"small",type:"primary",onClick:n.handleExportTest},{default:ft(()=>m[1]||(m[1]=[Vt(" 导出试卷 ",-1)])),_:1,__:[1]})])])]),default:ft(()=>[ht("div",du,[n.editingQuestion?(Ct(),zt("div",vu,[at(n.QuestionEditor,{question:n.editingQuestion,"onUpdate:question":n.handleQuestionUpdate,onSave:n.handleSaveQuestion,onCancel:n.handleCancelEdit,onDelete:n.handleDeleteCurrentQuestion},null,8,["question"])])):(Ct(),zt("div",hu,[at(l,{description:"请选择要编辑的题目"},{default:ft(()=>[at(t,{type:"primary",onClick:n.handleAddQuestion},{default:ft(()=>m[3]||(m[3]=[Vt("添加新题目",-1)])),_:1,__:[3]})]),_:1})]))])]),_:1})])]),ht("div",pu,[at(a,null,{header:ft(()=>m[4]||(m[4]=[ht("span",null,"试卷统计",-1)])),default:ft(()=>[ht("div",gu,[at(i,{gutter:16},{default:ft(()=>[at(f,{span:6},{default:ft(()=>[at(s,{title:"题目总数",value:n.questions.length},null,8,["value"])]),_:1}),at(f,{span:6},{default:ft(()=>[at(s,{title:"总分",value:n.totalScore},null,8,["value"])]),_:1}),at(f,{span:6},{default:ft(()=>[at(s,{title:"已选题目",value:n.selectedQuestions.length},null,8,["value"])]),_:1}),at(f,{span:6},{default:ft(()=>[at(s,{title:"未保存更改",value:n.unsavedChanges},null,8,["value"])]),_:1})]),_:1}),ht("div",yu,[m[5]||(m[5]=ht("h4",null,"题型分布",-1)),ht("div",mu,[(Ct(!0),zt($e,null,Ye(n.questionTypeStats,(u,y)=>(Ct(),Ue(r,{key:y,type:n.getTagType(y),size:"small"},{default:ft(()=>[Vt(Kt(n.getQuestionTypeLabel(y))+": "+Kt(u),1)]),_:2},1032,["type"]))),128))])])])]),_:1})])])}const wu=Hi(ou,[["render",bu],["__scopeId","data-v-f18aae54"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/views/QuestionEdit/index.vue"]]);export{wu as default};
