var le=(H,e,m)=>new Promise((l,y)=>{var h=u=>{try{f(m.next(u))}catch(c){y(c)}},_=u=>{try{f(m.throw(u))}catch(c){y(c)}},f=u=>u.done?l(u.value):Promise.resolve(u.value).then(h,_);f((m=m.apply(H,e)).next())});import{x as te,y as p,A as a,K as V,O as i,P as T,a6 as C,M as r,C as oe,z as d,ax as _e,r as x,c as S,h as fe,j as pe,al as g,Q as t,I as o,H as Z}from"./vendor-CPqkYfXn.js";import{a as R,h as ge,z as ve,j as ye}from"./ui-CjjzzDsP.js";import{u as be}from"./filter-t8MhSl_r.js";import{_ as ne}from"./index-BdEKvwRr.js";import"./utils-DxgFcSvi.js";const ce=te({__name:"QuestionPreview",props:{question:{type:Object,required:!0},number:{type:Number,required:!0},showAnswer:{type:Boolean,required:!1,default:!1},showExplanation:{type:Boolean,required:!1,default:!1},fontSize:{type:Number,required:!1,default:14},lineHeight:{type:Number,required:!1,default:1.5}},setup(H,{expose:e}){e();const l={getAnswerLines:y=>{switch(y){case"fill_blank":return 2;case"short_answer":return 4;case"essay":return 8;default:return 0}}};return Object.defineProperty(l,"__isScriptSetup",{enumerable:!1,value:!0}),l}}),we={class:"question-header"},xe={class:"question-number"},Pe={class:"question-score"},Ve=["innerHTML"],ze={key:0,class:"question-options"},he={key:1,class:"answer-space"},qe={class:"answer-lines"},ke={key:2,class:"question-answer"},Se={key:3,class:"question-explanation"},Te=["innerHTML"];function Ce(H,e,m,l,y,h){return d(),p("div",{class:"question-preview",style:oe({fontSize:m.fontSize+"px",lineHeight:m.lineHeight})},[a("div",we,[a("span",xe,i(m.number)+".",1),a("span",Pe,"("+i(m.question.score)+"分)",1)]),a("div",{class:"question-stem",innerHTML:m.question.content.stem},null,8,Ve),m.question.content.options&&m.question.content.options.length>0?(d(),p("div",ze,[(d(!0),p(T,null,C(m.question.content.options,(_,f)=>(d(),p("div",{key:f,class:"option-item"},i(String.fromCharCode(65+f))+". "+i(_),1))),128))])):["fill_blank","short_answer","essay"].includes(m.question.tags.questionType)?(d(),p("div",he,[a("div",qe,[(d(!0),p(T,null,C(l.getAnswerLines(m.question.tags.questionType),_=>(d(),p("div",{key:_,class:"answer-line"}))),128))])])):V("",!0),m.showAnswer?(d(),p("div",ke,[e[0]||(e[0]=a("strong",null,"答案：",-1)),r(i(m.question.content.answer),1)])):V("",!0),m.showExplanation&&m.question.content.explanation?(d(),p("div",Se,[e[1]||(e[1]=a("strong",null,"解析：",-1)),a("div",{innerHTML:m.question.content.explanation},null,8,Te)])):V("",!0)],4)}const Ae=ne(ce,[["render",Ce],["__scopeId","data-v-106e3998"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/components/ui/QuestionPreview.vue"]]),Ue=te({__name:"index",setup(H,{expose:e}){e();const m=be(),l=_e(),y=x([]),h=x({paperSize:"A4",orientation:"portrait",fontSize:14,titleFontSize:24,lineHeight:1.5,margin:{top:25,right:20,bottom:25,left:20},showAnswer:!1,showExplanation:!1,questionGrouping:"byType"}),_=x(1),f=x(),u=x(!1),c=x(!1),v=x(1),z=x(6),P=x("期末考试试卷"),A=x(120),U=x({format:"pdf",filename:"试卷",includes:["questions"]}),L=S(()=>y.value.reduce((s,b)=>s+b.score,0)),F=S(()=>{const s={};return y.value.forEach(b=>{const w=b.tags.questionType;s[w]=(s[w]||0)+1}),s}),O=S(()=>Math.max(1,Math.ceil(y.value.length/z.value))),E=S(()=>{const s=(v.value-1)*z.value,b=s+z.value;return y.value.slice(s,b)}),D=S(()=>{const s={};return E.value.forEach(b=>{const w=b.tags.questionType;s[w]||(s[w]=[]),s[w].push(b)}),s}),q=S(()=>{if(y.value.length>0){const s=[...new Set(y.value.map(b=>b.tags.subject))];return s.length===1?s[0]:"综合"}return"数学"}),W=S(()=>{const{paperSize:s,orientation:b,margin:w,fontSize:ee,titleFontSize:re,lineHeight:ue}=h.value;let X=s==="A4"?210:297,Y=s==="A4"?297:420;b==="landscape"&&([X,Y]=[Y,X]);const Q=3.779527559,de=X*Q*_.value,me=Y*Q*_.value;return{width:`${de}px`,minHeight:`${me}px`,padding:`${w.top*Q*_.value}px ${w.right*Q*_.value}px ${w.bottom*Q*_.value}px ${w.left*Q*_.value}px`,fontSize:`${ee*_.value}px`,lineHeight:ue,transform:`scale(${_.value})`,transformOrigin:"top left","--title-font-size":`${re*_.value}px`,"--content-font-size":`${ee*_.value}px`}}),I={single_choice:"单选题",multiple_choice:"多选题",true_false:"判断题",fill_blank:"填空题",short_answer:"简答题",essay:"解答题",calculation:"计算题",application:"应用题",analysis:"分析题",comprehensive:"综合题"},G=s=>I[s]||s,j=s=>y.value.findIndex(b=>b.id===s)+1,M=()=>{_.value<2&&(_.value=Math.min(2,_.value+.1))},K=()=>{_.value>.5&&(_.value=Math.max(.5,_.value-.1))},J=()=>{_.value=1},n=s=>{s>=1&&s<=O.value&&(v.value=s)},k=()=>{v.value<O.value&&v.value++},B=()=>{v.value>1&&v.value--},N=s=>{z.value=s,v.value=1},ae=()=>{h.value={paperSize:"A4",orientation:"portrait",fontSize:14,titleFontSize:24,lineHeight:1.5,margin:{top:25,right:20,bottom:25,left:20},showAnswer:!1,showExplanation:!1,questionGrouping:"byType"},P.value="期末考试试卷",A.value=120,R.success("排版设置已重置")},se=()=>{if(y.value.length===0){R.warning("没有题目可以导出");return}U.value.filename=`${P.value}_${new Date().toISOString().slice(0,10)}`,u.value=!0},ie=()=>le(this,null,function*(){try{c.value=!0,l.push("/export"),R.success("跳转到导出页面"),u.value=!1}catch(s){console.error("Navigation failed:",s),R.error("跳转失败，请重试")}finally{c.value=!1}});fe(()=>{m.questions.length>0&&(y.value=[...m.questions])}),pe(()=>m.questions,s=>{s.length>0&&(y.value=[...s])},{deep:!0});const $={filterStore:m,router:l,questions:y,layoutParams:h,zoomLevel:_,previewRef:f,exportDialogVisible:u,exporting:c,currentPage:v,questionsPerPage:z,paperTitle:P,examDuration:A,exportOptions:U,totalScore:L,questionTypeStats:F,estimatedPages:O,currentPageQuestions:E,currentPageGroupedQuestions:D,paperSubject:q,previewStyle:W,questionTypeLabels:I,getQuestionTypeLabel:G,getQuestionNumber:j,zoomIn:M,zoomOut:K,resetZoom:J,goToPage:n,nextPage:k,prevPage:B,updateQuestionsPerPage:N,handleResetLayout:ae,handleExport:se,handleConfirmExport:ie,get ZoomIn(){return ye},get ZoomOut(){return ve},get Download(){return ge},QuestionPreview:Ae};return Object.defineProperty($,"__isScriptSetup",{enumerable:!1,value:!0}),$}}),Le={class:"paper-layout"},Oe={class:"layout-container"},Qe={class:"settings-panel"},He={class:"settings-content"},Ee={class:"quick-titles"},De={class:"input-label"},je={class:"input-label"},Me={class:"paper-stats"},Ne={class:"type-distribution"},Fe={class:"preview-panel"},Ie={class:"preview-header"},Ge={class:"preview-actions"},Be={key:0,class:"pagination-controls"},Ze={class:"preview-content"},Re={key:0,class:"paper-header"},We={class:"paper-title"},Ke={class:"paper-info"},Je={class:"info-row"},Xe={class:"info-row"},Ye={key:0},$e={key:1},el={key:1,class:"page-header"},ll={class:"page-info"},tl={class:"questions-content"},ol={key:0},nl={class:"group-title"},al={class:"group-info"},sl={key:1},il={class:"export-options"};function rl(H,e,m,l,y,h){const _=g("el-input"),f=g("el-button"),u=g("el-form-item"),c=g("el-input-number"),v=g("el-option"),z=g("el-select"),P=g("el-radio"),A=g("el-radio-group"),U=g("el-slider"),L=g("el-col"),F=g("el-row"),O=g("el-switch"),E=g("el-form"),D=g("el-card"),q=g("el-descriptions-item"),W=g("el-tag"),I=g("el-descriptions"),G=g("el-button-group"),j=g("el-icon"),M=g("el-checkbox"),K=g("el-checkbox-group"),J=g("el-dialog");return d(),p("div",Le,[a("div",Oe,[a("div",Qe,[t(D,null,{header:o(()=>e[29]||(e[29]=[a("span",null,"排版设置",-1)])),default:o(()=>[a("div",He,[t(E,{model:l.layoutParams,"label-width":"80px"},{default:o(()=>[t(u,{label:"试卷标题"},{default:o(()=>[t(_,{modelValue:l.paperTitle,"onUpdate:modelValue":e[0]||(e[0]=n=>l.paperTitle=n),placeholder:"请输入试卷标题",maxlength:"50","show-word-limit":"",style:{width:"100%"}},null,8,["modelValue"]),a("div",Ee,[t(f,{size:"small",text:"",onClick:e[1]||(e[1]=n=>l.paperTitle="期末考试试卷")},{default:o(()=>e[30]||(e[30]=[r(" 期末考试 ",-1)])),_:1,__:[30]}),t(f,{size:"small",text:"",onClick:e[2]||(e[2]=n=>l.paperTitle="期中考试试卷")},{default:o(()=>e[31]||(e[31]=[r(" 期中考试 ",-1)])),_:1,__:[31]}),t(f,{size:"small",text:"",onClick:e[3]||(e[3]=n=>l.paperTitle="单元测试试卷")},{default:o(()=>e[32]||(e[32]=[r(" 单元测试 ",-1)])),_:1,__:[32]}),t(f,{size:"small",text:"",onClick:e[4]||(e[4]=n=>l.paperTitle="模拟考试试卷")},{default:o(()=>e[33]||(e[33]=[r(" 模拟考试 ",-1)])),_:1,__:[33]}),t(f,{size:"small",text:"",onClick:e[5]||(e[5]=n=>l.paperTitle="月考试卷")},{default:o(()=>e[34]||(e[34]=[r(" 月考 ",-1)])),_:1,__:[34]}),t(f,{size:"small",text:"",onClick:e[6]||(e[6]=n=>l.paperTitle="练习题")},{default:o(()=>e[35]||(e[35]=[r(" 练习题 ",-1)])),_:1,__:[35]})])]),_:1}),t(u,{label:"考试时长"},{default:o(()=>[t(c,{modelValue:l.examDuration,"onUpdate:modelValue":e[7]||(e[7]=n=>l.examDuration=n),min:30,max:300,step:15,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"]),e[36]||(e[36]=a("div",{class:"input-label"},"分钟",-1))]),_:1,__:[36]}),t(u,{label:"纸张大小"},{default:o(()=>[t(z,{modelValue:l.layoutParams.paperSize,"onUpdate:modelValue":e[8]||(e[8]=n=>l.layoutParams.paperSize=n),style:{width:"100%"}},{default:o(()=>[t(v,{label:"A4",value:"A4"}),t(v,{label:"A3",value:"A3"})]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"纸张方向"},{default:o(()=>[t(A,{modelValue:l.layoutParams.orientation,"onUpdate:modelValue":e[9]||(e[9]=n=>l.layoutParams.orientation=n)},{default:o(()=>[t(P,{value:"portrait"},{default:o(()=>e[37]||(e[37]=[r("纵向",-1)])),_:1,__:[37]}),t(P,{value:"landscape"},{default:o(()=>e[38]||(e[38]=[r("横向",-1)])),_:1,__:[38]})]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"正文字体"},{default:o(()=>[t(U,{modelValue:l.layoutParams.fontSize,"onUpdate:modelValue":e[10]||(e[10]=n=>l.layoutParams.fontSize=n),min:10,max:24,step:1,"show-input":"",style:{width:"100%"}},null,8,["modelValue"]),a("div",De,i(l.layoutParams.fontSize)+"px (正文字体大小)",1)]),_:1}),t(u,{label:"标题字体"},{default:o(()=>[t(U,{modelValue:l.layoutParams.titleFontSize,"onUpdate:modelValue":e[11]||(e[11]=n=>l.layoutParams.titleFontSize=n),min:16,max:32,step:1,"show-input":"",style:{width:"100%"}},null,8,["modelValue"]),a("div",je,i(l.layoutParams.titleFontSize)+"px (标题字体大小)",1)]),_:1}),t(u,{label:"行距"},{default:o(()=>[t(U,{modelValue:l.layoutParams.lineHeight,"onUpdate:modelValue":e[12]||(e[12]=n=>l.layoutParams.lineHeight=n),min:1,max:2.5,step:.1,"show-input":"",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(u,{label:"页边距"},{default:o(()=>[t(F,{gutter:8},{default:o(()=>[t(L,{span:12},{default:o(()=>[t(c,{modelValue:l.layoutParams.margin.top,"onUpdate:modelValue":e[13]||(e[13]=n=>l.layoutParams.margin.top=n),min:10,max:50,size:"small","controls-position":"right"},null,8,["modelValue"]),e[39]||(e[39]=a("div",{class:"margin-label"},"上边距(mm)",-1))]),_:1,__:[39]}),t(L,{span:12},{default:o(()=>[t(c,{modelValue:l.layoutParams.margin.bottom,"onUpdate:modelValue":e[14]||(e[14]=n=>l.layoutParams.margin.bottom=n),min:10,max:50,size:"small","controls-position":"right"},null,8,["modelValue"]),e[40]||(e[40]=a("div",{class:"margin-label"},"下边距(mm)",-1))]),_:1,__:[40]})]),_:1}),t(F,{gutter:8,style:{"margin-top":"8px"}},{default:o(()=>[t(L,{span:12},{default:o(()=>[t(c,{modelValue:l.layoutParams.margin.left,"onUpdate:modelValue":e[15]||(e[15]=n=>l.layoutParams.margin.left=n),min:10,max:50,size:"small","controls-position":"right"},null,8,["modelValue"]),e[41]||(e[41]=a("div",{class:"margin-label"},"左边距(mm)",-1))]),_:1,__:[41]}),t(L,{span:12},{default:o(()=>[t(c,{modelValue:l.layoutParams.margin.right,"onUpdate:modelValue":e[16]||(e[16]=n=>l.layoutParams.margin.right=n),min:10,max:50,size:"small","controls-position":"right"},null,8,["modelValue"]),e[42]||(e[42]=a("div",{class:"margin-label"},"右边距(mm)",-1))]),_:1,__:[42]})]),_:1})]),_:1}),t(u,{label:"题型排列"},{default:o(()=>[t(A,{modelValue:l.layoutParams.questionGrouping,"onUpdate:modelValue":e[17]||(e[17]=n=>l.layoutParams.questionGrouping=n)},{default:o(()=>[t(P,{value:"byType"},{default:o(()=>e[43]||(e[43]=[r("按题型分组",-1)])),_:1,__:[43]}),t(P,{value:"mixed"},{default:o(()=>e[44]||(e[44]=[r("混合排列",-1)])),_:1,__:[44]})]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"答案显示"},{default:o(()=>[t(O,{modelValue:l.layoutParams.showAnswer,"onUpdate:modelValue":e[18]||(e[18]=n=>l.layoutParams.showAnswer=n),"active-text":"显示答案","inactive-text":"隐藏答案"},null,8,["modelValue"])]),_:1}),t(u,{label:"解析显示"},{default:o(()=>[t(O,{modelValue:l.layoutParams.showExplanation,"onUpdate:modelValue":e[19]||(e[19]=n=>l.layoutParams.showExplanation=n),"active-text":"显示解析","inactive-text":"隐藏解析"},null,8,["modelValue"])]),_:1}),t(u,{label:"每页题数"},{default:o(()=>[t(z,{modelValue:l.questionsPerPage,"onUpdate:modelValue":e[20]||(e[20]=n=>l.questionsPerPage=n),onChange:e[21]||(e[21]=n=>l.updateQuestionsPerPage(n)),style:{width:"100%"}},{default:o(()=>[t(v,{label:"3题/页",value:3}),t(v,{label:"4题/页",value:4}),t(v,{label:"5题/页",value:5}),t(v,{label:"6题/页",value:6}),t(v,{label:"8题/页",value:8}),t(v,{label:"10题/页",value:10}),t(v,{label:"全部题目",value:999})]),_:1},8,["modelValue"])]),_:1}),l.estimatedPages>1?(d(),Z(u,{key:0,label:"跳转页面"},{default:o(()=>[t(z,{modelValue:l.currentPage,"onUpdate:modelValue":e[22]||(e[22]=n=>l.currentPage=n),onChange:e[23]||(e[23]=n=>l.goToPage(n)),style:{width:"100%"}},{default:o(()=>[(d(!0),p(T,null,C(l.estimatedPages,n=>(d(),Z(v,{key:n,label:`第${n}页`,value:n},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):V("",!0),t(u,null,{default:o(()=>[t(f,{onClick:l.handleResetLayout,style:{width:"100%"}},{default:o(()=>e[45]||(e[45]=[r(" 重置设置 ",-1)])),_:1,__:[45]})]),_:1})]),_:1},8,["model"])])]),_:1}),t(D,{style:{"margin-top":"16px"}},{header:o(()=>e[46]||(e[46]=[a("span",null,"试卷信息",-1)])),default:o(()=>[a("div",Me,[t(I,{column:1,size:"small"},{default:o(()=>[t(q,{label:"题目总数"},{default:o(()=>[r(i(l.questions.length),1)]),_:1}),t(q,{label:"总分"},{default:o(()=>[r(i(l.totalScore),1)]),_:1}),t(q,{label:"总页数"},{default:o(()=>[r(i(l.estimatedPages),1)]),_:1}),l.estimatedPages>1?(d(),Z(q,{key:0,label:"当前页"},{default:o(()=>[r(i(l.currentPage)+" / "+i(l.estimatedPages),1)]),_:1})):V("",!0),t(q,{label:"题型分布"},{default:o(()=>[a("div",Ne,[(d(!0),p(T,null,C(l.questionTypeStats,(n,k)=>(d(),Z(W,{key:k,size:"small",style:{margin:"2px"}},{default:o(()=>[r(i(l.getQuestionTypeLabel(k))+": "+i(n),1)]),_:2},1024))),128))])]),_:1})]),_:1})])]),_:1})]),a("div",Fe,[t(D,null,{header:o(()=>[a("div",Ie,[e[50]||(e[50]=a("span",null,"试卷预览",-1)),a("div",Ge,[l.estimatedPages>1?(d(),p("div",Be,[t(G,{size:"small"},{default:o(()=>[t(f,{onClick:l.prevPage,disabled:l.currentPage===1},{default:o(()=>e[47]||(e[47]=[r(" 上一页 ",-1)])),_:1,__:[47]},8,["disabled"]),t(f,{disabled:""},{default:o(()=>[r(i(l.currentPage)+" / "+i(l.estimatedPages),1)]),_:1}),t(f,{onClick:l.nextPage,disabled:l.currentPage===l.estimatedPages},{default:o(()=>e[48]||(e[48]=[r(" 下一页 ",-1)])),_:1,__:[48]},8,["disabled"])]),_:1})])):V("",!0),t(G,null,{default:o(()=>[t(f,{size:"small",onClick:l.zoomOut,disabled:l.zoomLevel<=.5},{default:o(()=>[t(j,null,{default:o(()=>[t(l.ZoomOut)]),_:1})]),_:1},8,["disabled"]),t(f,{size:"small",onClick:l.resetZoom},{default:o(()=>[r(i((l.zoomLevel*100).toFixed(0))+"% ",1)]),_:1}),t(f,{size:"small",onClick:l.zoomIn,disabled:l.zoomLevel>=2},{default:o(()=>[t(j,null,{default:o(()=>[t(l.ZoomIn)]),_:1})]),_:1},8,["disabled"])]),_:1}),t(f,{size:"small",type:"primary",onClick:l.handleExport},{default:o(()=>[t(j,null,{default:o(()=>[t(l.Download)]),_:1}),e[49]||(e[49]=r(" 导出试卷 ",-1))]),_:1,__:[49]})])])]),default:o(()=>[a("div",Ze,[a("div",{class:"paper-preview",style:oe(l.previewStyle),ref:"previewRef"},[l.currentPage===1?(d(),p("div",Re,[a("h1",We,i(l.paperTitle),1),a("div",Ke,[e[51]||(e[51]=a("div",{class:"info-row"},[a("span",null,"姓名：__________"),a("span",null,"班级：__________"),a("span",null,"学号：__________")],-1)),a("div",Je,[a("span",null,"学科："+i(l.paperSubject),1),a("span",null,"总分："+i(l.totalScore)+"分",1),a("span",null,"时间："+i(l.examDuration)+"分钟",1)]),a("div",Xe,[a("span",null,"题目数："+i(l.questions.length)+"题",1),l.estimatedPages>1?(d(),p("span",Ye,"第"+i(l.currentPage)+"页 / 共"+i(l.estimatedPages)+"页",1)):V("",!0),l.estimatedPages>1?(d(),p("span",$e,"当前页："+i(l.currentPageQuestions.length)+"题",1)):V("",!0)])])])):V("",!0),l.currentPage>1&&l.estimatedPages>1?(d(),p("div",el,[a("div",ll,[a("span",null,i(l.paperTitle),1),a("span",null,"第"+i(l.currentPage)+"页 / 共"+i(l.estimatedPages)+"页",1)])])):V("",!0),a("div",tl,[l.layoutParams.questionGrouping==="byType"?(d(),p("div",ol,[(d(!0),p(T,null,C(l.currentPageGroupedQuestions,(n,k)=>{var B;return d(),p("div",{key:k,class:"question-group"},[a("h3",nl,[r(i(l.getQuestionTypeLabel(k))+" ",1),a("span",al,"(共"+i(n.length)+"题，每题"+i((B=n[0])==null?void 0:B.score)+"分)",1)]),(d(!0),p(T,null,C(n,N=>(d(),p("div",{key:N.id,class:"question-item"},[t(l.QuestionPreview,{question:N,number:l.getQuestionNumber(N.id),"show-answer":l.layoutParams.showAnswer,"show-explanation":l.layoutParams.showExplanation,"font-size":l.layoutParams.fontSize,"line-height":l.layoutParams.lineHeight},null,8,["question","number","show-answer","show-explanation","font-size","line-height"])]))),128))])}),128))])):(d(),p("div",sl,[(d(!0),p(T,null,C(l.currentPageQuestions,n=>(d(),p("div",{key:n.id,class:"question-item"},[t(l.QuestionPreview,{question:n,number:l.getQuestionNumber(n.id),"show-answer":l.layoutParams.showAnswer,"show-explanation":l.layoutParams.showExplanation,"font-size":l.layoutParams.fontSize,"line-height":l.layoutParams.lineHeight},null,8,["question","number","show-answer","show-explanation","font-size","line-height"])]))),128))]))])],4)])]),_:1})])]),t(J,{modelValue:l.exportDialogVisible,"onUpdate:modelValue":e[28]||(e[28]=n=>l.exportDialogVisible=n),title:"导出试卷",width:"40%"},{footer:o(()=>[t(f,{onClick:e[27]||(e[27]=n=>l.exportDialogVisible=!1)},{default:o(()=>e[57]||(e[57]=[r("取消",-1)])),_:1,__:[57]}),t(f,{type:"primary",onClick:l.handleConfirmExport,loading:l.exporting},{default:o(()=>e[58]||(e[58]=[r(" 确定导出 ",-1)])),_:1,__:[58]},8,["loading"])]),default:o(()=>[a("div",il,[t(E,{model:l.exportOptions,"label-width":"100px"},{default:o(()=>[t(u,{label:"导出格式"},{default:o(()=>[t(A,{modelValue:l.exportOptions.format,"onUpdate:modelValue":e[24]||(e[24]=n=>l.exportOptions.format=n)},{default:o(()=>[t(P,{value:"pdf"},{default:o(()=>e[52]||(e[52]=[r("PDF格式",-1)])),_:1,__:[52]}),t(P,{value:"word"},{default:o(()=>e[53]||(e[53]=[r("Word格式",-1)])),_:1,__:[53]})]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"文件名"},{default:o(()=>[t(_,{modelValue:l.exportOptions.filename,"onUpdate:modelValue":e[25]||(e[25]=n=>l.exportOptions.filename=n),placeholder:"请输入文件名"},null,8,["modelValue"])]),_:1}),t(u,{label:"包含内容"},{default:o(()=>[t(K,{modelValue:l.exportOptions.includes,"onUpdate:modelValue":e[26]||(e[26]=n=>l.exportOptions.includes=n)},{default:o(()=>[t(M,{value:"questions"},{default:o(()=>e[54]||(e[54]=[r("题目",-1)])),_:1,__:[54]}),t(M,{value:"answers"},{default:o(()=>e[55]||(e[55]=[r("答案",-1)])),_:1,__:[55]}),t(M,{value:"explanations"},{default:o(()=>e[56]||(e[56]=[r("解析",-1)])),_:1,__:[56]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"])])}const gl=ne(Ue,[["render",rl],["__scopeId","data-v-79883815"],["__file","D:/组卷2.0/zhijuanyun-frontend/src/views/PaperLayout/index.vue"]]);export{gl as default};
